
local print = print
local require = require
local string = string
local tonumber = tonumber
local tostring = tostring
local coroutine = coroutine
local debug = debug
local xpcall = xpcall
local typeof = typeof
local pairs = pairs

local util = require "util"
local log = require "log"

local HTTPReq = CS.War.Script.HTTPReq
local GameObject = CS.UnityEngine.GameObject
local Application = CS.UnityEngine.Application
local Utility = CS.War.Script.Utility
local IsInEditor = CS.War.Script.Utility.IsInEditor
local UnityWebRequest = CS.UnityEngine.Networking.UnityWebRequest
local Path = CS.System.IO.Path
local File = CS.System.IO.File
local DirectoryInfo = CS.System.IO.DirectoryInfo
local FileInfo = CS.System.IO.FileInfo
local Int32 = CS.System.Int32
local Debug = CS.UnityEngine.Debug
local DownloadHandlerFile = CS.UnityEngine.Networking.DownloadHandlerFile
local FileStream = CS.System.IO.FileStream
local FileMode = CS.System.IO.FileMode
local FileAccess = CS.System.IO.FileAccess
local BgDownloadMgrIns = CS.War.Base.BgDownloadMgr.Instance
local RequestUrl = BgDownloadMgrIns.RequestUrl
local Texture2D = CS.UnityEngine.Texture2D
local Sprite = CS.UnityEngine.Sprite
local Rect = CS.UnityEngine.Rect
local Vector2 = CS.UnityEngine.Vector2
local ImageConversion = CS.UnityEngine.ImageConversion

module("http_inst")

local inst = nil
local instReq = nil
local downloadHandlerFileExist = false
-- 创建Sprite缓存
local spriteCache = {}

local MAX_RETRY_COUNT = 10 -- 最大重试次数

function init()
    if not util.IsObjNull(inst) then return end

    inst = GameObject()
    instReq = inst:AddComponent(typeof(HTTPReq))
    GameObject.DontDestroyOnLoad(inst)
    CheckDownloadHandlerFileExist()
end


function req_raw(url,callback)
    init()
    print("req_raw",url)
    instReq:RequestRecord_Raw(url, function(bytes, hasError)
        if not callback then return end
        
        callback(bytes,hasError)
    end)
end

function Req_Timeout(url,timeout,callback)
    init()
    print("Req_Timeout",url)
    local request = instReq.RequestRecord_Timeout
    request(instReq,url, timeout, function(str, hasError, bytes)
        if not callback then return end

----        print("Req:",url,str)
        callback(str, hasError, bytes)
    end)
end

function Request_Timeout_WithHeaders(url, timeout, headers, callback, useText)
    init()
    print("Request_Timeout_WithHeaders", url)
    local request = UnityWebRequest.Get(url)
    request.timeout = timeout
    -- 设置自定义请求头
    if headers then
        for k, v in pairs(headers) do
            request:SetRequestHeader(k, tostring(v))
        end
    end
    local asyncOperation = request:SendWebRequest()
    local unityCoroutine = instReq:StartUnityCoroutine(asyncOperation, function()
        local f, res =
            xpcall(
            function()
                if callback then
                    callback(useText and (request.downloadHandler and request.downloadHandler.text or "") or "",
                        request.error,
                        request.downloadHandler and request.downloadHandler.data or nil,
                        request.downloadedBytes,
                        request)
                end
            end,
            debug.traceback
        )
        if not f then
            Debug.LogError(res)
        end
        request:Dispose()
    end)
    return request
end
-- return Coroutine
-- 此方法对某些迭代器似乎有问题，慎用，使用 instReq:StartUnityCoroutine 接口替代
function LuaStartCoroutine(ienumerator, callback)
    local util = require 'xlua.util'
    local t_fun = util.cs_generator(function()
        coroutine.yield(ienumerator)
        if callback then
            callback()
        end
    end)

    return instReq:StartCoroutine(t_fun)
end

function Request_ContentLength( url, timeout, callback )
    init()
    print("Request_ContentLength",url)

    local headRequest = UnityWebRequest.Head(url)
    headRequest.timeout = timeout

    local asyncOperation = headRequest:SendWebRequest()
    LuaStartCoroutine(headRequest, function ()
        local totalLength = headRequest:GetResponseHeader("Content-Length")
        headRequest:Dispose()
        if callback then
            callback(tonumber(totalLength))
        end
    end)
end

-- 2021/3/6 添加此接口。通过 UnityWebRequest 下载资源
---@param useText boolean @ 默认不使用downloadHandler.text，使用该值会将downloadHandler.data转为字符串，造成大量GC，慎用
function Request_Timeout( url, timeout, callback, useText)
    init()
    print("Request_Timeout",url)

    local request = UnityWebRequest.Get(url)
    request.timeout = timeout

    local asyncOperation = request:SendWebRequest()
    -- 2021/3/6 添加 StartUnityCoroutine 接口，注意兼容外网
    local unityCoroutine = instReq:StartUnityCoroutine(asyncOperation, function ()
        -- 避免 callback 中异常导致未正确释放 request 对象
        local f,res = xpcall(function ()
            if callback then
                callback(useText and request.downloadHandler.text or "", request.error, request.downloadHandler.data, request.downloadedBytes, request)
            end
        end,debug.traceback)
        if not f then
            Debug.LogError(res)
        end
        request:Dispose()
    end)
    return request
end

---返回文件大小
function getFileSize(filepath)
    if File.Exists(filepath) then
        local fileInfo = FileInfo(filepath)
        return Int32.Parse(tostring(fileInfo.Length)) -- 获取文件大小
    end
    --文件不存在或无法访问
    return -1
end
---如果文件存在则删除
function deleteFileIfExists(filepath)
    if File.Exists(filepath) then
        File.Delete(filepath)
    end
end
---保存 data字节数组到文件 filepath
function  saveDataToFile(filepath, data, append)
    local rDir = Path.GetDirectoryName(filepath)
    if not File.Exists(rDir) then
        local directoryInfo = DirectoryInfo(rDir)
        directoryInfo:Create()
    end
    if append then
        local streamWriter = FileStream(filepath, FileMode.Append, FileAccess.Write)
        --print("saveDataToFile", data.Length)
        streamWriter:Write(data, 0, data.Length)
        streamWriter:Flush()
        streamWriter:Close()
        streamWriter:Dispose()
    else
        File.WriteAllBytes(filepath, data)
    end
end
--[[ UnityWebRequest 下载资源保存在 savePath, removeLocalFile 是否删除本地原文件,fileSize 下载文件大小,errTryCount 错误重试次数(默认3次
-- callback完成回调 ( savePath 保存路径
request.error 下载错误,如果有
request.downloadedBytes 下载字节大小)
return UnityWebRequest, 本地是否有文件
]]
function Request_DownloadFile(url, timeout, callback, savePath, removeLocalFile, fileSize, errTryCount)
    init()
    if errTryCount == nil then errTryCount = 0 end
    print("Request_DownloadFile",errTryCount,url,savePath)
    local localSize = 0
    local request = nil
    if removeLocalFile or not RequestUrl then
        deleteFileIfExists(savePath)
    else
        localSize = getFileSize(savePath)
        if localSize > 0 then
            if fileSize then
                if localSize > fileSize then
                    deleteFileIfExists(savePath)
                    localSize = 0
                elseif localSize == fileSize then
                    callback(savePath, nil, fileSize)
                    return
                end
            end
            --request:SetRequestHeader("Range", "bytes=" .. localSize .. "-")--处理器断点下载有bug,错误会写入文件
            log.Warning("Request_DownloadFile localSize=", localSize)
        end
    end
    if RequestUrl then --新资源下载接口,支持断点续传
        local Requester, OnErr,OnCancle,OnFinish, OnClear
        OnClear = function(savePath, errorDes, size, cb)
            BgDownloadMgrIns:UnRegisterDownloadErrorHandler(OnErr)
            BgDownloadMgrIns:UnRegisterDownloadFinishHandler(OnFinish)
            BgDownloadMgrIns:UnRegisterDownloadCancleHandler(OnCancle)
            Requester = nil
            if cb then cb(savePath, errorDes, size) end
        end
        OnErr = function(abname, url2, errorCode, errorDes)
            if abname ~= url then return end
            log.Warning("Request_DownloadFile err",abname, errorCode, errorDes,Requester.Offset,Requester.Length)
            local err = errorDes or errorCode
            if errTryCount > 0 and err then
                errTryCount = errTryCount - 1
                OnClear()
                return Request_DownloadFile(url, timeout, callback, savePath, removeLocalFile, fileSize,errTryCount)
            else
                OnClear(savePath, err, Requester.Length, callback)
            end
        end
        OnCancle = function(abname)
            if abname ~= url then return end
            log.Warning("Request_DownloadFile cancle",savePath,Requester.Offset,Requester.Length)
            OnClear(savePath, "cancle", Requester.Length, callback)
        end
        OnFinish = function(abname)
            if abname ~= url then return end
            log.Warning("Request_DownloadFile ok",savePath,Requester.Offset,Requester.Length )
            OnClear(savePath, nil, Requester.Length, callback)
        end
        BgDownloadMgrIns:RegisterDownloadErrorHandler(OnErr)
        BgDownloadMgrIns:RegisterDownloadFinishHandler(OnFinish)
        BgDownloadMgrIns:RegisterDownloadCancleHandler(OnCancle)
        BgDownloadMgrIns:Start()
        Requester = BgDownloadMgrIns:RequestUrl(url, savePath)
        Requester:Start()
    else
        request = UnityWebRequest.Get(url)
        if downloadHandlerFileExist then
            request.downloadHandler = DownloadHandlerFile(savePath, true)
        end
        request.timeout = timeout
        local asyncOperation = request:SendWebRequest()
        local unityCoroutine = instReq:StartUnityCoroutine(asyncOperation, function ()
            -- 避免 callback 中异常导致未正确释放 request 对象
            local f,res = xpcall(function ()
                local err = request.error
                local downloadedBytes = request.downloadedBytes
                log.Warning("Request_DownloadFile cb", err, request.responseCode,request.result,
                        request.isNetworkError, request.isHttpError, request.downloadProgress, request.isDone)
                if errTryCount > 0 and err then
                    deleteFileIfExists(savePath)
                    errTryCount = errTryCount - 1
                    return Request_DownloadFile(url, timeout, callback, savePath, removeLocalFile, fileSize,errTryCount)
                end
                if not downloadHandlerFileExist then  --如果DownloadHandlerFile类没有导出
                    saveDataToFile(savePath, request.downloadHandler.data)
                end
                callback(savePath, err, localSize + downloadedBytes)
            end,debug.traceback)
            if not f then log.Warning("Request_DownloadFile err", res) end
            request:Dispose()
        end)
    end
    return request, localSize > 0
end

--判断DownloadHandlerFile类是否导出
function CheckDownloadHandlerFileExist()
    downloadHandlerFileExist = util.IsCSharpClass(DownloadHandlerFile)
    log.Warning("http_inst downloadHandlerFileExist:", downloadHandlerFileExist,RequestUrl)
end

--请求下载文件
function RequestDownloadFile(url, savePath, timeout, callback, param)
    init()
    --print("RequestDownloadFile",url,savePath)
    if string.IsNullOrEmpty(url) or string.IsNullOrEmpty(savePath) then
        if callback then
            callback(0, url, savePath, param)
        end
        return
    end

    local request = nil
    if downloadHandlerFileExist then
        request = UnityWebRequest(url, "GET", DownloadHandlerFile(savePath), null)
    else 
        request = UnityWebRequest.Get(url)
    end
    request.timeout = timeout or 5
    
    local asyncOperation = request:SendWebRequest()
    -- 2021/3/6 添加 StartUnityCoroutine 接口，注意兼容外网
    local unityCoroutine = instReq:StartUnityCoroutine(asyncOperation, function ()
        -- 避免 callback 中异常导致未正确释放 request 对象
        local f,res = xpcall(function ()
            local result = 0
            if request then
                local writeFile = function()
                    local rDir = Path.GetDirectoryName(savePath)
                    if not File.Exists(rDir) then
                        local directoryInfo = DirectoryInfo(rDir)
                        directoryInfo:Create()
                    end
                    File.WriteAllBytes(savePath, request.downloadHandler.data)
                end
                local strError = request.error
                if string.IsNullOrEmpty(strError) then
                    result = 1
                    if not downloadHandlerFileExist then  --如果DownloadHandlerFile类没有导出
                        local status = xpcall(writeFile, function(err)
                            log.Warning("http_inst.RequestDownloadFile.writeFile()发生异常!!!!!!", err)
                        end)
                    end
                end
            end
            if callback then
                callback(result, url, savePath, param)
            end
        end,debug.traceback)
        if not f then
            if callback then
                callback(0, url, savePath, param)
            end
        end
        if request then
            request:Dispose()
        end
    end)
    return request
end

function HttpPostJson(url,jsonData,callback)
    init()
    print("HttpPostJson",url,jsonData)
    -- 版本控制
    if IsInEditor or  Utility.VersionIsHigher(Application.version, '1.0.39') then
        instReq:HttpPostJson(url, jsonData, function(str, hasError, bytes)
            if hasError then
                log.Error("url======", url, "hasError====", tostring(hasError))
            end
            if not callback then return end
            
            callback(str, hasError, bytes)
        end)
    else
----        print("no support")
    end

    
end

-- 替换Image
function DownLoadImageNew(url, callback,timeout,errTryCount,isTexture)
    init()
    -- 检查缓存中是否已有该Sprite
    if spriteCache[url] then
        local cachedSprite = spriteCache[url]
        -- 确保Sprite没有被销毁
        if not util.IsObjNull(cachedSprite) then
            if callback then
                callback(cachedSprite)
            end
            return
        else
            -- 如果Sprite已被销毁，从缓存中移除
            spriteCache[url] = nil
        end
    end
    if errTryCount == nil then errTryCount = 0 end
    if timeout == nil then timeout = 5 end

    -- 检查重试次数是否超过最大值
    if errTryCount > MAX_RETRY_COUNT then
        --log.Warning("DownLoadImageNew: Max retry count exceeded for URL: " .. url)
        if callback then callback(nil) end
        return
    end
    
    local request = UnityWebRequest.Get(url)
    request.timeout = timeout
    local asyncOperation = request:SendWebRequest()
    local unityCoroutine = instReq:StartUnityCoroutine(asyncOperation, function ()
        -- 避免 callback 中异常导致未正确释放 request 对象
        local f,res = xpcall(function ()
            local err = request.error
            --这里重要，因为就算是没有error，在isDone的情况下也有可能得到为0的内容，渲染的texture就是红色问号
            local downloadedBytes = tonumber(tostring(request.downloadedBytes))
            local isRealDone = request.isDone and (downloadedBytes > 0)
            --log.Warning("DownLoadImageNew cb", err, request.responseCode,request.result,
            --        request.isNetworkError, request.isHttpError, request.downloadProgress, request.isDone)
            if errTryCount > 0 and (err or not isRealDone) then
                errTryCount = errTryCount - 1
                -- 添加0.2秒延迟后重试
                --log.Warning("DownLoadImageNew TryCount",errTryCount,url )
                util.DelayCallOnce(0.2, function()
                    DownLoadImageNew(url, callback, timeout,errTryCount)
                end)
                return
                --return DownLoadImageNew(url, callback, timeout,errTryCount)
            end
            if request.downloadHandler and request.downloadHandler.data then
                local texture2d = Texture2D(2,2)
                ImageConversion.LoadImage(texture2d,request.downloadHandler.data)
                if isTexture then
                    spriteCache[url] = texture2d
                else
                    local sp =  Sprite.Create(texture2d, Rect(0, 0, texture2d.width, texture2d.height), Vector2(0.5, 0.5))
                    -- 将Sprite添加到缓存
                    spriteCache[url] = sp
                end
                
                if callback then
                    callback(spriteCache[url])
                end
            end
        end,debug.traceback)
        if not f then log.Warning("Request_DownloadFile err", res) end
        request:Dispose()
    end)
end

function GetImageCache(url)
    if not url then
        return nil
    end

    local sprite = spriteCache[url]
    if not sprite then
        return nil
    end
    return image
end

function ClearImageCache(url)
    if url then
        spriteCache[url] = nil
    end
end

-- 清理所有图片缓存
function ClearAllImageCache()
    spriteCache = {}
end

-- 替换Image
function DownLoadImage(url, callback)
    init()
    --log.Warning("http_inst----instReq.DownLoadImage",instReq.DownLoadImage)
    if instReq.DownLoadImage then
        instReq:DownLoadImage(url,callback)
    else
        log.Error("版本过低")
    end
end

-- 替换Image
function ReleaseImage(url)
    init()
    if instReq.ReleaseImage then
        instReq:ReleaseImage(url)
    else
        log.Error("版本过低")
    end
end

-- 替换RawImage
function DownLoadRawImage(url, callback,spriteCallback)
    init()
    log.Warning("http_inst----instReq.DownLoadRawImage",instReq.DownLoadRawImage)
    if instReq.DownLoadRawImage then
        instReq:DownLoadRawImage(url,callback)
    else
        DownLoadImage(url, spriteCallback)
    end
end
