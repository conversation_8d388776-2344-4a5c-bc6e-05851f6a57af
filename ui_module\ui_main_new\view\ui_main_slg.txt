﻿--region FileHead
--- ui_main_slg.txt
-- author:  author
-- ver:     1.0
-- desc:    
-------------------------------------------------
--endregion 

--region Require
local print = print
local require = require
local type = type
local pairs = pairs
local ipairs = ipairs
local typeof = typeof
local string = string
local table = table
local math = math
local tostring = tostring
local log = log
local LeanTween = CS.LeanTween
local GameObject = CS.UnityEngine.GameObject
local Button = CS.UnityEngine.UI.Button
local Quaternion = CS.UnityEngine.Quaternion
local CScrollRect = typeof(CS.War.UI.CScrollRect)
local Vector2 = CS.UnityEngine.Vector2
local VerticalLayoutGroup = typeof(CS.UnityEngine.UI.VerticalLayoutGroup)
local TextMeshProUGUI = CS.TMPro.TextMeshProUGUI
local ScrollRectItem = CS.UI.UGUIExtend.ScrollRectItem

local ui_util = require "ui_util"
local red_const = require "red_const"
local item_list_component = require "item_list_component"
local os = require "os"
local time_util = require "time_util"
local main_slg_mgr = require "main_slg_mgr"
local util = require "util"
local face_item = require "face_item_new"
local enum_define = require "enum_define"
local event = require "event"
local class = require "class"
local lang = require "lang"
local ui_base = require "ui_base"
local new_grade_mgr = require "new_grade_mgr"
local GWHomeMgr = GWHomeMgr
local GWAdmin = GWAdmin
local GWG = GWG
local Common_Util = CS.Common_Util.UIUtil
local SpriteSwitcher = CS.War.UI.SpriteSwitcher
local RectTransform = CS.UnityEngine.RectTransform
local UGUIGray = CS.War.UI.UGUIGray
local windowMgr = require "ui_window_mgr"
local main_slg_const = require "main_slg_const"
local gw_const = require "gw_const"
local sceneType = gw_const.ESceneType
local effect_item = require "effect_item"
local gw_task_util = require "gw_task_util"
local gw_activity_red_mgr = require "gw_activity_red_mgr"
local screen_util = require "screen_util"
local val = require("val")
local redPerf = val.IsTrue("sw_red_system_perf", 0)
local Canvas = CS.UnityEngine.Canvas
local UIUtil = CS.Common_Util.UIUtil
local TextScroll = CS.War.UI.TextScroll
local ReviewingUtil = require "ReviewingUtil"
local isRightShrink = false--右边栏缩放状态
local rightMaxOpenCount = 3
local rightShrinkShowItemPriority = {
    1002, --超值活动
    1005, --同盟对决 
    1003  --活动中心

}
--endregion 

--region ModuleDeclare
module("ui_main_slg")
local ui_path = "ui/prefabs/gw/gw_main/uimainslg.prefab"
local window = nil
local UIView = {}
--endregion 

--region WidgetTable
UIView.widget_table = {
    Btn_PowerShow = { path = "Auto_Left/NoDesertStormUI/Auto_PowerShow", type = "Button", event_name = "OnBtn_PowerShowClickedProxy" },
    Btn_VipShowBtn = { path = "Auto_Left/NoDesertStormUI/Auto_LeftTop_All/Auto_VipShowBtn", type = "Button", event_name = "OnBtn_VipShowBtnClickedProxy" },
    grayGroup_VipShowBtn = { path = "Auto_Left/NoDesertStormUI/Auto_LeftTop_All/Auto_VipShowBtn", type = UGUIGray },
    btn_vipCustomer = { path = "Auto_Left/NoDesertStormUI/Auto_LeftTop_All/Auto_VipShowBtn/btn_vipCustomer", type = "Button", event_name = "OnBtn_VipCustomerBtnClickedProxy" },
    LeftTop_Item = { path = "Auto_Left/NoDesertStormUI/Auto_LeftTop_All/Auto_LeftTop_Item", type = "Button" },
    LeftBottom_Item = { path = "Auto_Left/Auto_LeftBottom_Content/Auto_LeftBottom_Item", type = "Button" },
    LeftBottom_Item2 = { path = "Auto_Left/Auto_LeftBottom_Content2/Auto_LeftBottom2_Content/Auto_LeftBottom_Item2", type = "Button" },
    LeftBottom_Item4 = { path = "Auto_Left/Auto_LeftBottom_Content2/Auto_LeftBottom4_Content/Auto_LeftBottom_Item4", type = "Button" },
    LeftBottom_Item5 = { path = "Auto_Left/Auto_LeftBottom_Content2/Auto_LeftBottom5_Content/Auto_LeftBottom_Item5", type = "Button" },
    RightTop_Item = { path = "Auto_Right/NoDesertStormUI/right_scroll_view_1/Viewport/rt_rig_top1/Auto_RightTop_Item", type = "Button" },
    RightTop_Item2 = { path = "Auto_Right/NoDesertStormUI/right_scroll_view_2/Viewport/rt_rig_top2/Auto_RightTop_Item", type = "Button" },
    RightBottom_Item = { path = "Auto_Right/Auto_RightBottom_Content/Auto_SubRightBottom_Content/Auto_RightBottom_Item", type = "Button" },
    Btn_GatherBtn = { path = "Auto_Right/Auto_RightBottom_Content/Auto_GatherBtn", type = "Button", event_name = "OnBtn_GatherBtnClickedProxy" },
    Rtsf_InvitingBubbles = { path = "Auto_Right/Auto_RightBottom_Content/Auto_GatherBtn/obj_InvitingBubbles", type = "RectTransform" },
    btn_invite = { path = "Auto_Right/Auto_RightBottom_Content/Auto_GatherBtn/obj_InvitingBubbles/btn_invite", type = "Button", event_name = "OnBtn_InviteBtnClickedProxy" },
    gatherTimeTxt = { path = "Auto_Right/Auto_RightBottom_Content/Auto_GatherBtn/Time", type = "Text" },
    warningTimeTxt = { path = "Auto_Right/Auto_RightBottom_Content/Auto_ReinforceBtn/Time", type = "Text" },
    ssw_ReinforceBtn = { path = "Auto_Right/Auto_RightBottom_Content/Auto_ReinforceBtn", type = SpriteSwitcher },
    Btn_ReinforceBtn = { path = "Auto_Right/Auto_RightBottom_Content/Auto_ReinforceBtn", type = "Button", event_name = "OnBtn_ReinforceBtnClickedProxy" },
    Text_PowerNumText = { path = "Auto_Left/NoDesertStormUI/Auto_PowerShow/Auto_PowerNumText", type = "Text", event_name = "" },
    Text_VipTxet = { path = "Auto_Left/NoDesertStormUI/Auto_LeftTop_All/Auto_VipShowBtn/Auto_VipTxet", type = "Text", event_name = "" },
    Img_NewTip = { path = "Auto_Left/NoDesertStormUI/Auto_PowerShow/Auto_NewTip", type = "Image", event_name = "" },
    Rtsf_Left = { path = "Auto_Left", type = "RectTransform", event_name = "" },
    PowerSlider = { path = "Auto_Left/NoDesertStormUI/Auto_PowerShow/Auto_PowerSlider", type = "Slider", event_name = "" },
    Rtsf_Rect_PlayerIcon = { path = "Auto_Left/NoDesertStormUI/Auto_Rect_PlayerIcon", type = "RectTransform", event_name = "" },
    faceRoot = { path = "Auto_Left/NoDesertStormUI/Auto_Rect_PlayerIcon/faceRoot", type = "RectTransform"},
    CongressRedRoot = {path = "Auto_Left/NoDesertStormUI/Auto_Rect_PlayerIcon/congressRedRoot",type = "RectTransform"},
    Rtsf_LeftTop_Content = { path = "Auto_Left/NoDesertStormUI/Auto_LeftTop_All", type = "RectTransform", event_name = "" },
    Rtsf_LeftBottom_Content = { path = "Auto_Left/Auto_LeftBottom_Content", type = "RectTransform", event_name = "" },
    Layout_LeftBottom_Content = { path = "Auto_Left/Auto_LeftBottom_Content", type = VerticalLayoutGroup },
    Rtsf_LeftBottom2_Content = { path = "Auto_Left/Auto_LeftBottom_Content2/Auto_LeftBottom2_Content", type = "RectTransform", event_name = "" },
    Rtsf_LeftBottom4_Content = { path = "Auto_Left/Auto_LeftBottom_Content2/Auto_LeftBottom4_Content", type = "RectTransform", event_name = "" },
    Rtsf_TaskContent = { path = "Auto_Left/NoDesertStormUI/Auto_TaskContent", type = "RectTransform", event_name = "" },
    Rtsf_Right = { path = "Auto_Right", type = "RectTransform", event_name = "" },
    Rtsf_Scroll_RightTop2 = { path = "Auto_Right/NoDesertStormUI/right_scroll_view_2", type = RectTransform },
    Scroll_RightTop2 = { path = "Auto_Right/NoDesertStormUI/right_scroll_view_2", type = CScrollRect },

    Rtsf_Scroll_RightTop = { path = "Auto_Right/NoDesertStormUI/right_scroll_view_1", type = RectTransform },
    Scroll_RightTop = { path = "Auto_Right/NoDesertStormUI/right_scroll_view_1", type = CScrollRect },
    Rtsf_RightTop_Content = { path = "Auto_Right/NoDesertStormUI/right_scroll_view_1/Viewport/rt_rig_top1", type = "RectTransform", event_name = "" },
    LayoutGroup_RightTop = { path = "Auto_Right/NoDesertStormUI/right_scroll_view_1/Viewport/rt_rig_top1", type = VerticalLayoutGroup },
    Viewport_RightTop = { path = "Auto_Right/NoDesertStormUI/right_scroll_view_1/Viewport", type = "RectTransform" },

    Btn_RightTop_Expand = { path = "Auto_Right/NoDesertStormUI/right_scroll_view_1/Btn_expand", type = "Button", event_name = "OnBtn_RightExpandClickedProxy" },
    Img_Right_expand = { path = "Auto_Right/NoDesertStormUI/right_scroll_view_1/Btn_expand/rtf_expand_red", type = "RectTransform" },
    Rtsf_RightTop_Expand = { path = "Auto_Right/NoDesertStormUI/right_scroll_view_1/Btn_expand/Image", type = "RectTransform" },

    Rtsf_RightBottom_Content = { path = "Auto_Right/Auto_RightBottom_Content", type = "RectTransform", event_name = "" },
    Rtsf_SubRightBottom_Content = { path = "Auto_Right/Auto_RightBottom_Content/Auto_SubRightBottom_Content", type = "RectTransform", event_name = "" },
    Btn_TaskContent = { path = "Auto_Left/NoDesertStormUI/Auto_TaskContent/Bg", type = "Button", event_name = "OnBtnTaskContentClicked" },
    Btn_TaskIcon = { path = "Auto_Left/NoDesertStormUI/Auto_TaskContent/Auto_TaskIconBtn", type = "Button", event_name = "OnBtnTaskClicked" },
    Btn_TaskIconCanvas = { path = "Auto_Left/NoDesertStormUI/Auto_TaskContent/Auto_TaskIconBtn", type = Canvas, event_name = "" },
    Rect_TaskContentBg = { path = "Auto_Left/NoDesertStormUI/Auto_TaskContent/Bg", type = "RectTransform", event_name = "" },
    Text_TaskRewardDesc = { path = "Auto_Left/NoDesertStormUI/Auto_TaskContent/Bg/Auto_TaskRewardBg/Auto_TaskDescReward", type = "Text", event_name = "" },
    Text_TaskRewardTextScroll = { path = "Auto_Left/NoDesertStormUI/Auto_TaskContent/Bg/Auto_TaskRewardBg/Auto_TaskDescReward", type = TextScroll },
    Text_TaskRewardBg = { path = "Auto_Left/NoDesertStormUI/Auto_TaskContent/Bg/Auto_TaskRewardBg", type = "Image", event_name = "" },
    Rect_TaskRewardBg = { path = "Auto_Left/NoDesertStormUI/Auto_TaskContent/Bg/Auto_TaskRewardBg", type = "RectTransform" },
    --解救人质
    parent_BomberMan = { path = "Auto_Left/NoDesertStormUI/Auto_LeftTop_All/parent_BomberMan", type = "RectTransform" },
   

    Rtsf_TaskFinger = { path = "Auto_Left/NoDesertStormUI/Auto_TaskContent/Auto_TaskFinger", type = "RectTransform" },
    Rtsf_TaskAperture = { path = "Auto_Left/NoDesertStormUI/Auto_TaskContent/Auto_TaskAperture", type = "RectTransform" },
    Rtsf_TaskLoopEffect = { path = "Auto_Left/NoDesertStormUI/Auto_TaskContent/Bg/Auto_TaskRewardBg/Auto_TaskLoopParent", type = GameObject },
    Rtsf_TaskPlayEffect = { path = "Auto_Left/NoDesertStormUI/Auto_TaskContent/Bg/Auto_TaskRewardBg/Auto_TaskPlayParent", type = GameObject },
    Rtsf_TaskRewardRightBg = { path = "Auto_Left/NoDesertStormUI/Auto_TaskContent/Auto_TaskIconBtn/Auto_TaskRewardRightBg", type = "RectTransform" },

    --region 攻城号角
    Btn_WarHorns = { path = "Auto_Right/Auto_RightBottom_Content/Auto_WarHorns", type = "Button", event_name = "OnBtn_WarHornsBtnClickedProxy" },
    Rtsf_WarStart = { path = "Auto_Right/Auto_RightBottom_Content/Auto_WarHorns/obj_warStart", type = "RectTransform" },
    Rtsf_WarReady = { path = "Auto_Right/Auto_RightBottom_Content/Auto_WarHorns/obj_warReady", type = "RectTransform" },
    Text_WarTime = { path = "Auto_Right/Auto_RightBottom_Content/Auto_WarHorns/txt_warTime", type = "Text" },
    --endregion

    --region 沙漠风暴宝箱进度
    Btn_RewardChess = {path = "Auto_Right/Auto_RightBottom_Content/Auto_Chess",type = "Button",event_name = "OnAuto_ChessBtnClickedProxy"},
    Image_RewardChessRing = {path = "Auto_Right/Auto_RightBottom_Content/Auto_Chess/obj_warReady/Ring",type = "Image"},
    Text_RewardChessRate = {path = "Auto_Right/Auto_RightBottom_Content/Auto_Chess/obj_warReady/Text",type = "Text"},
    --endregion
    
    LeftNoDesertObj = {path = "Auto_Left/NoDesertStormUI", type = "RectTransform" },
    RightNoDesertObj = {path = "Auto_Right/NoDesertStormUI", type = "RectTransform" },

    --region 怪物攻城 入口已经迁移至ui_sand_box_monsters_approching 原本的位置需要占位 因为有布局
    parent_MonsterApproaching = { path = "Auto_Left/NoDesertStormUI/Auto_LeftTop_All/parent_MonsterApproaching", type = "RectTransform"},
    -- Rtf_MonsterApproaching = { path = "Auto_Left/NoDesertStormUI/Auto_LeftTop_All/Auto_Monster_Approaching", type = "RectTransform" },
    -- btnMonsterApproClick = { path = "Auto_Left/NoDesertStormUI/Auto_LeftTop_All/Auto_Monster_Approaching/btnMonsterApproClick", type = "Button", event_name = "OnBtn_MonsterApproClickedProxy" },
    -- Switch_MonsterAppro = { path = "Auto_Left/NoDesertStormUI/Auto_LeftTop_All/Auto_Monster_Approaching/img_Monster_Appro", type = SpriteSwitcher },
    -- Text_MonsterApproDes ={ path = "Auto_Left/NoDesertStormUI/Auto_LeftTop_All/Auto_Monster_Approaching/img_Monster_Appro/txt_MonsterApproDes", type = "Text" },
    -- Text_MonsterApproTime ={ path = "Auto_Left/NoDesertStormUI/Auto_LeftTop_All/Auto_Monster_Approaching/img_Monster_Appro/txt_MonsterApproTime", type = "Text" },
    -- Img_BubbleBg = { path = "Auto_Left/NoDesertStormUI/Auto_LeftTop_All/Auto_Monster_Approaching/img_Monster_Appro/img_BubbleBg", type = "RectTransform" },
    -- Text_MonsterApproPop = { path = "Auto_Left/NoDesertStormUI/Auto_LeftTop_All/Auto_Monster_Approaching/img_Monster_Appro/img_BubbleBg/txt_MonsterApproPop", type = "Text" },
    -- Sld_MonsterAppro = { path = "Auto_Left/NoDesertStormUI/Auto_LeftTop_All/Auto_Monster_Approaching/img_Monster_Appro/sld_MonsterAppro", type = "Slider" },
    -- Text_MonsterApproSlider = { path = "Auto_Left/NoDesertStormUI/Auto_LeftTop_All/Auto_Monster_Approaching/img_Monster_Appro/sld_MonsterAppro/txt_BomberSlider", type = "Text" },
    -- Rtf_MonsterEffect = {path = "Auto_Left/NoDesertStormUI/Auto_LeftTop_All/Auto_Monster_Approaching/img_Monster_Appro/rtf_MonsterEffect", type = "RectTransform"},
    --endregion
    
    --region 按钮Tips的模版

    rtf_buttonTips = { path = "buttonTips", type = "RectTransform" },
    --模版父物体
    rtf_Template = { path = "buttonTips/Template", type = "RectTransform" },
    effectMask = { path = "Auto_Right/NoDesertStormUI/right_scroll_view_1/effectMask", type = "RectTransform", effect_mask = "" },

    Btn_grayLoad = { path = "h/Auto_GrayLoad", type = "Button", event_name = "onGrayLoadClickProxy" },
    Text_grayLoadPercent = { path = "h/Auto_GrayLoad/percent", type = TextMeshProUGUI },

    --临时测试调查问卷
    rightSideBar = { path = "h", type = "RectTransform" },
    --endregion

    --region 右上第三队列
    RightTop_Item3 = { path = "Auto_Right/NoDesertStormUI/right_scroll_view_3/Viewport/rt_rig_top3/Auto_RightTop_Item", type = "Button" },
    --endregion

    --region 左下角,第二水平队列
    Rtsf_SimpleModel = { path = "Auto_Left/Auto_LeftBottom_Content2/Auto_LeftBottom3_Content/Auto_SimpleModel", type = "RectTransform" },
    --endregion
    
    rtsf_tipsTrans = {path = "TipsTran",type = "RectTransform"},

    --region 战区对决按钮
    Btn_ZoneDuel = { path = "Auto_Right/Auto_RightBottom_Content/Auto_DuelZone", type = "Button", event_name = "OnBtn_DuelZoneBtnClickedProxy" },
    ScrItem_selfCongressProgress = { path = "Auto_Right/Auto_RightBottom_Content/Auto_DuelZone/Bg/scrItem_selfCongressProgress", type = ScrollRectItem },
    ScrItem_enemyCongressProgress = { path = "Auto_Right/Auto_RightBottom_Content/Auto_DuelZone/Bg/scrItem_enemyCongressProgress", type = ScrollRectItem },
    Text_duelZoneTime = { path = "Auto_Right/Auto_RightBottom_Content/Auto_DuelZone/Icon/txt_duelZoneTime", type = "Text" },
    --endregion

}
--endregion 

--region function 设置View-Controller模式的UI
-- return type  ---- 未定义/VC/纯V   
-- 注意，View-Controller模式的ui必须要重写这个接口
function UIView:SetVCTypeUI()
    return self.__base:SetVCTypeUI(enum_define.enum_ui_vc_type.vc)
end
--endregion 

--region WindowInit
--[[窗口初始化]]
function UIView:Init()
    self.VData = {}
    self:SetVCTypeUI()
    self.__base:Init(self)
    self:SubscribeEvents()
    self.rightBgMaxHeight = screen_util.GetScreenAspect() < 720 / 1280 and 734 or 624
    self.rightBgMinHeight = 350
    self.baseLeftCommonPosY = self.Rtsf_LeftBottom_Content.transform.localPosition.y
    self.baseLeftCommonOffset = 85
    --region User

    self.recycle_ui = true
    self.entryEffList = {}
    self.buttonGroupTypeMap = {
        [main_slg_const.MainButtonGroupType.LeftTop] = self.LeftTop_Item,
        [main_slg_const.MainButtonGroupType.LeftBottom] = self.LeftBottom_Item,
        [main_slg_const.MainButtonGroupType.LeftBottom2] = self.LeftBottom_Item2,
        [main_slg_const.MainButtonGroupType.LeftBottom4] = self.LeftBottom_Item4,
        [main_slg_const.MainButtonGroupType.LeftBottom5] = self.LeftBottom_Item5,
        [main_slg_const.MainButtonGroupType.RightTop] = self.RightTop_Item,
        [main_slg_const.MainButtonGroupType.RightBottom] = self.RightBottom_Item,
        [main_slg_const.MainButtonGroupType.RightTop2] = self.RightTop_Item2,
        [main_slg_const.MainButtonGroupType.RightTop3] = self.RightTop_Item3,
    }
    self:BindUIRed(self.Btn_ReinforceBtn.transform, red_const.Enum.ReinforceMain, nil, { redPath = red_const.Type.Num })
    self:BindUIRed(self.PowerSlider.transform, red_const.Enum.StaminaMain, nil, { redPath = red_const.Type.Default })
    self:BindUIRed(self.Btn_GatherBtn.transform, red_const.Enum.GatherMain, nil, { redPath = red_const.Type.Num })
    self:BindUIRed(self.Btn_TaskIcon.transform, red_const.Enum.TaskMain, nil, { pos = { x = 23.5, y = 45 }, redPath = red_const.Type.Num })
    self:BindUIRed(self.CongressRedRoot.transform, red_const.Enum.Congress, nil, { pos = { x = 12, y = 12 }, redPath = red_const.Type.Mark })
    self:BindUIRed(self.Rtsf_Rect_PlayerIcon.transform, red_const.Enum.AICustomerService, nil, {pos = {x = 5,y = 8},redPath = red_const.Type.GM})
    self:BindUIRed(self.faceRoot, red_const.Enum.RankingFaceRed, nil, { redPath = red_const.Type.Default })
    --endregion 

    --region 一个通用的定时器

    --TODO 主界面最好保留一个定时器，不同模块的定时任务都在其中执行
    self.MainMenuCommonTimer = self.MainMenuCommonTimer or util.IntervalCall(1, function()
        --主机界面每秒更新按钮text文本事件
        if self.getTextTimerFuncArr then
            for buttonType, FuncData in pairs(self.getTextTimerFuncArr) do
                FuncData.func(FuncData.textUI)
            end
        end
        if self.bubbleFuncArr then
            for buttonType, FuncData in pairs(self.bubbleFuncArr) do
                FuncData.func()
            end
        end
        if self.checkData then
            for buttonType, FuncData in pairs(self.checkData) do
                FuncData.baseInterval = FuncData.baseInterval + 1
                if FuncData.baseInterval >= FuncData.interval then
                    FuncData.baseInterval = 0
                    FuncData.func()
                end
            end
        end
    end)
    self.bomberEnterWnd = nil
    --endregion
end

--endregion 


--region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function UIView:OnShow()
    self.__base:OnShow()
    self.TweenToShow()
    self.Btn_TaskIconCanvas.sortingOrder = self.curOrder + 1
    --CheckGrayLoad()
    self:SetQuestionnaireItem()
end

function UIView:UpdateUI()
    local bomberEnterWnd = self:GetBomberManEnter()
    if bomberEnterWnd and bomberEnterWnd.UpdateUIEx then
        bomberEnterWnd:UpdateUIEx()
    end
    event.Trigger(event.MAIN_SLG_UPDATE_UI)
    ----触发一下刷新按钮层级事件    
    --local gw_zombie_treasure_data = require "gw_zombie_treasure_data"
    --gw_zombie_treasure_data.RefreshMainEntranceButton(nil,nil,nil,true)
    if util.IsObjNull(self.Btn_TaskIconCanvas) then
        return
    end
    self.Btn_TaskIconCanvas.sortingOrder = self.curOrder + 1
end

--endregion

function UIView:SetQuestionnaireItem()
    --只处理逻辑发送请求的逻辑
    --local ReviewingUtil = require "ReviewingUtil"
    --log.Warning(" ReviewingUtil.IsReviewing()", ReviewingUtil.IsReviewing())
    --if ReviewingUtil.IsReviewing() then
    --    self:DisposeQuestionnaireItem()
    --    return 
    --end
    --local questionnaire_investigation_mgr = require "questionnaire_investigation_mgr"
    --
    --questionnaire_investigation_mgr.Init(self.rightSideBar.transform, function(showQuestionnaire,go)
    --    if not window:IsValid() then
    --        return
    --    end
    --end)
    local net_questionnaire_module = require "net_questionnaire_module"
    net_questionnaire_module.RequestQuestionnaire()
end

--function UIView:DisposeQuestionnaireItem()
--    local questionnaire_investigation_mgr = require "questionnaire_investigation_mgr"
--    questionnaire_investigation_mgr.HideQuestionnaireIcon(true)
--end

--region WindowOnHide
--[[界面隐藏时调用]]
function UIView:OnHide()
    self.__base:OnHide()
end

--endregion 

function UIView.TweenToShow()
    if not window or not window:IsValid() then
        return
    end

    local self = window
    if self.isShowFinish then
        return
    end
    main_slg_mgr.MoveTweenXAuto(self.Rtsf_Left, true, true)
    main_slg_mgr.MoveTweenXAuto(self.Rtsf_Right, false, true, function()
        self.isShowFinish = true
    end)
end

function UIView.TweenToClose()
    if not window or not window:IsValid() then
        return
    end
    local self = window
    self.isShowFinish = false
    main_slg_mgr.MoveTweenXAuto(self.Rtsf_Left, true, false)
    main_slg_mgr.MoveTweenXAuto(self.Rtsf_Right, false, false, function()
        if not redPerf then
            --移动到屏幕外面后就看不到了，不需要卸载以降低卡顿
            windowMgr:UnloadModule(_NAME)
        end
    end)
end

--region WindowClose
function UIView:Close()
    --self:DisposeQuestionnaireItem()
    if self.qipao_Trucks then
        self.qipao_Trucks:Dispose()
        self.qipao_Trucks = nil
    end
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    
    if window.TimerMgr then
        for i, v in pairs(window.TimerMgr) do
            if v and v.SwitchTimer then
                util.RemoveDelayCall(v.SwitchTimer)
                v.SwitchTimer = nil
            end
            if v and v.CountDown then
                util.RemoveDelayCall(v.CountDown)
                v.CountDown = nil
            end
        end
    end

    if self.loadTicker then
        util.RemoveDelayCall(self.loadTicker)
        self.loadTicker = nil
    end
    if self.MonsterAttackTime then
        self:RemoveTimer(self.MonsterAttackTime)
        self.MonsterAttackTime = nil
    end
    if self.MonsterEffect then 
        self.MonsterEffect:Dispose()
        self.MonsterEffect = nil
        self.MonsterMainSlgEffect = nil
    end
    ---注意Init跟Close不是一一对应的，SubscribeEvents和UnsubscribeEvents是一一对应的，所以这里取消注册的逻辑要放到UnsubscribeEvents里面
    if self.recycle_ui then
        self.__base:Close()
        isRightShrink = true
        self:OnBtn_RightExpandClicked()
    else
        self:UnsubscribeEvents()
        self.__base:Close()
    end
    --self:ClearBomberMan()
    if self.entryEffList then
        for k, v in pairs(self.entryEffList) do
            if v then
                for key, _v in pairs(v) do
                    if _v then
                        for index, value in pairs(_v) do
                            if value then
                                value:Dispose()
                            end
                        end
                    end
                end
            end
        end
        self.entryEffList = nil
    end

    --region User
    --endregion 
end
--endregion 

--region 事件注册
function UIView:SubscribeEvents()
    -- 注意 事件建议使用self:RegisterEvent(event_name,func)来注册，不需要手动维护注销了       
    self.RigTopDragEvent = function()
        -- self:OnScrollRight()
    end
    self.Scroll_RightTop.onDrag = self.RigTopDragEvent
    --self.Scroll_RightTop.onInertia:AddListener(self.StopRigTopDragEvent) 
    self.ShowOrHideDesertStormUI = function(_,value)
        Common_Util.SetActive(self.LeftNoDesertObj,value ~= sceneType.Storm)
        Common_Util.SetActive(self.RightNoDesertObj,value ~= sceneType.Storm)
        local targetPosY = value ~= sceneType.Storm and self.baseLeftCommonPosY or self.baseLeftCommonPosY - self.baseLeftCommonOffset
        Common_Util.SetLocalPos(self.Rtsf_LeftBottom_Content,self.Rtsf_LeftBottom_Content.transform.localPosition.x,targetPosY,0)
    end
    self:RegisterEvent(event.GW_SCENE_CHANGE_SUCCESS, self.ShowOrHideDesertStormUI)
    --检测主线通过事件
    self.OnGWLevelPassEvent = function(_,win,type)
        if ReviewingUtil.IsReviewing() then
            return
        end
        if win then
            local gw_zombie_treasure_data = require "gw_zombie_treasure_data"
            local laymain_data = require "laymain_data"
            local curlevel = laymain_data.GetPassLevel()
            if curlevel and curlevel >= gw_zombie_treasure_data.GetMainSlgEntranceShow() then 
                gw_zombie_treasure_data.RefreshMainEntranceButton(nil,nil,nil,nil,false)
            else
                if type == 0 then 
                    local delayTime = 0
                    local funcID = 1530
                    local function_open_mgr = require "function_open_mgr"
                    if not function_open_mgr.CheckFunctionIsOpen(funcID) then return end
                    local player_mgr = require "player_mgr"
                    local funcIDKey = string.format("function_open_anim_main#%s#%s",funcID,player_mgr.GetPlayerRoleID())
                    local player_prefs = require "player_prefs"
                    local hasPass = player_prefs.GetCacheData(funcIDKey, 0)
                    if hasPass ~= 1 then
                        delayTime = 2.4;
                        player_prefs.SetCacheData(funcIDKey, 1)
                        local game_scheme = require "game_scheme"
                        local cfg = game_scheme:FunctionOpen_0(funcID)
                        if cfg then
                            windowMgr:ShowModule("ui_function_unlock_tips", nil, function()
                            end, {
                                showPathIndex = cfg.OpenEffectType,
                                trailPathIndex = 1,
                                targetPosIndex = 9,
                                icon = cfg.OpenIcon,
                            })
                        end
                    end
                end
                if curlevel < gw_zombie_treasure_data.GetShowMainLineEntrance() then 
                    gw_zombie_treasure_data.RefreshMainEntranceButton(true) 
                end
                
            end
        end   
    end
    self:RegisterEvent(event.GW_LEVEL_PASS_EVENT, self.OnGWLevelPassEvent)
end

function UIView:UnsubscribeEvents()
    if self.totalList then
        for k, list in pairs(self.totalList) do
            if list.Dispose then
                list:Dispose()
            end
        end
    end
    if self.faceShow then
        self.faceShow:Dispose()
    end

    if self.Scroll_RightTop then
        self.Scroll_RightTop.onDrag = nil
    end
    self.vipRedInit = false
    --self.Scroll_RightTop.onInertia:RemoveAllListeners()

    --通用定时器
    if self.MainMenuCommonTimer then
        util.RemoveDelayCall(self.MainMenuCommonTimer)
        self.MainMenuCommonTimer = nil
    end
    
    --右上活动按钮的定时器
    if self.rightTopBtnTimer then
        for i, v in pairs(self.rightTopBtnTimer) do
            util.RemoveDelayCall(v)
        end
        self.rightTopBtnTimer = nil
    end
end

--endregion

--region 功能函数区
---********************功能函数区**********---

--==========================右边栏伸缩=============================

function UIView:UpdateRightContent()
    if isRightShrink then
        self.Rtsf_Scroll_RightTop.sizeDelta = { x = self.Rtsf_Scroll_RightTop.sizeDelta.x, y = self.rightBgMinHeight }
        self.Rtsf_RightTop_Expand.localRotation = Quaternion.Euler(0, 0, 180)
        util.DelayCallOnce(0.2, function()
            if self and self.UIRoot and not self.UIRoot:IsNull() then
                self:SetRightExpandBtnRedTips()
            end
        end)
        self.Rtsf_RightTop_Content.anchoredPosition = Vector2.zero--不归0会导致下拉错位
        self.Scroll_RightTop.enabled = false
    else
        isRightShrink = false
        local openCount = self.rigTopCount
        local openMaxHeight = 110 * openCount + 25
        local sizeY = openMaxHeight >= self.rightBgMaxHeight and self.rightBgMaxHeight or openMaxHeight
        self.Rtsf_Scroll_RightTop.sizeDelta = { x = self.Rtsf_Scroll_RightTop.sizeDelta.x, y = sizeY }
        self.Viewport_RightTop.anchoredPosition = Vector2.zero--不归0会导致下拉错位
        self:SetRightExpandBtnRedTips()
        self.Rtsf_RightTop_Expand.localRotation = Quaternion.identity
        self.Scroll_RightTop.enabled = true
        self:OnRefreshRightTopContent()
        self.Scroll_RightTop.vertical = self.rigTopCount > rightMaxOpenCount
    end
    UIUtil.SetActive(self.Btn_RightTop_Expand, self.rigTopCount > table.count(self.rigTopPriorityList))
end

function UIView:OnBtn_RightExpandClicked()
    --if self.isFlying then
    --    return
    --end
    LeanTween.cancel(self.Rtsf_Scroll_RightTop)
    if not isRightShrink then
        isRightShrink = true

        LeanTween.size(self.Rtsf_Scroll_RightTop, { x = self.Rtsf_Scroll_RightTop.sizeDelta.x, y = self.rightBgMinHeight }, 0.2):setOnComplete(function()
            if self:IsValid() then
                self:SetRightExpandBtnRedTips()
            end
        end)
        self.Rtsf_RightTop_Expand.localRotation = Quaternion.Euler(0, 0, 180)

        self.Rtsf_RightTop_Content.anchoredPosition = Vector2.zero--不归0会导致下拉错位
        self.Scroll_RightTop.enabled = false
    else
        isRightShrink = false
        local openCount = self.rigTopCount
        local openMaxHeight = 110 * openCount + 25
        local sizeY = openMaxHeight >= self.rightBgMaxHeight and self.rightBgMaxHeight or openMaxHeight
        LeanTween.size(self.Rtsf_Scroll_RightTop, { x = self.Rtsf_Scroll_RightTop.sizeDelta.x, y = sizeY }, 0.2)
        self:SetRightExpandBtnRedTips()
        self.Rtsf_RightTop_Expand.localRotation = Quaternion.identity
        self.Scroll_RightTop.enabled = true
        self:OnRefreshRightTopContent()
        self.Scroll_RightTop.vertical = self.rigTopCount > rightMaxOpenCount

    end
end

--[[设置缩放状态下提示显示在缩放图标按钮上]]
function UIView:SetRightExpandBtnRedTips()
    if isRightShrink then
        self.Img_Right_expand.gameObject:SetActive(true)
        if self.rigTopPriorityList then
            for k, v in ipairs(self.rigTopPriorityList) do
                if not util.IsObjNull(v.trans) then
                    if self:IsValid() then
                        v.trans:SetSiblingIndex(v.shrinkIndex)
                    end
                end
            end
        end
    else
        self.Img_Right_expand.gameObject:SetActive(false)
        if self.rigTopPriorityList then
            for k, v in ipairs(self.rigTopPriorityList) do
                if not util.IsObjNull(v.trans) then
                    v.trans:SetSiblingIndex(v.curIndex)
                end
            end
        end
    end
end

--====================================================================

--设置体力
function UIView:SetStaminaData(data)
    if not data then
        return
    end

    local value
    if data.realPower == 0 then
        value = 0
    else
        value = data.realPower / data.maxPower
        if value > 1 then
            value = 1
        end
    end
    self.PowerSlider.value = value
    self.Text_PowerNumText.text = data.realPower
    self.Img_NewTip.gameObject:SetActive(data.isHasNew)
end


--设置vip
function UIView:SetVipData(data, active)
    Common_Util.SetActive(self.Btn_VipShowBtn, data.isShow)
    if data.isShow then
        self.Text_VipTxet.text = data.vipLevel and tostring(data.vipLevel) or "0"
        --设置是否是置灰
        self.grayGroup_VipShowBtn:SetEnable(data.isGray)
        if not self.vipRedInit then
            self.vipRedInit = true
            self:BindUIRed(self.Btn_VipShowBtn.transform, red_const.Enum.VipFreeExp, nil, { redPath = red_const.Type.Default })
        end
    end
end

--设置vip
function UIView:SetVipCustomer(active)
    Common_Util.SetActive(self.btn_vipCustomer, active)
end

--个人信息开放
function UIView:SetPlayerInfoOpen(isOpen)
    Common_Util.SetActive(self.Rtsf_Rect_PlayerIcon, isOpen)
    Common_Util.SetActive(self.Btn_PowerShow, isOpen)
end

--设置头像
function UIView:SetPlayerFaceData(data)
    self.faceShow = self.faceShow or face_item.CFaceItem()
    self.faceShow:Init(self.faceRoot, nil, 1.25)
    if data.customAvatarData and data.customAvatarData.used then
        self.faceShow:SetCustomFaceInfo(data.customAvatarData, function()
            local mgr_personalInfo = require "mgr_personalInfo"
            mgr_personalInfo.ShowPersonalInfoView()
        end)
    else
        self.faceShow:SetFaceInfo(data.faceID, function()
            local mgr_personalInfo = require "mgr_personalInfo"
            mgr_personalInfo.ShowPersonalInfoView()
        end)
    end
    --self.faceShow:SetActorIcon(data.faceID)
    self.faceShow:SetActorLvText(true, data.playerLevel)
    self.faceShow:SetFrameID(data.frameID, true)

end

--设置联盟战争入口(集结按钮)
function UIView:SetUnionWarBtn(data)
    if not data then
        return
    end
    self.Btn_GatherBtn.gameObject:SetActive(data.massCount > 0)
    --self.gatherTimeTxt = self.gatherTimeTxt or self.Btn_GatherBtn.transform:Find("Time").GetComponent(typeof(Text))
    local showTime = data.minMassFinishTime - os.server_time()
    --log.Error("联盟战争入口数据", showTime)
    self.gatherTimeTxt.gameObject:SetActive(data.teamIdelCount > 0)
    if data.massCount > 0 then
        if showTime <= 0 and data.isWaiting then
            self.gatherTimeTxt.text = lang.Get(560292)
        else
            if self.unionTimer then
                self:RemoveTimer(self.unionTimer)
                self.unionTimer = nil
            end
            self.unionTimer = self:CreateTimer(1, function()
                if data.minMassFinishTime <= 0 then
                    self.unionTimer = nil
                    return true
                end
                showTime = data.minMassFinishTime - os.server_time()
                if showTime <= 0 then
                    self.unionTimer = nil
                    return true
                end
                self.gatherTimeTxt.text = time_util.GetCountDownString2(showTime)
            end)
        end
    end
end

function UIView:UpdateGatherPop(isShow)
    UIUtil.SetActive(self.Rtsf_InvitingBubbles, isShow)
end

--设置增援按钮
function UIView:SetReinforceBtn(data)
    self.ssw_ReinforceBtn:Switch(data.iconIndex)
    self.Btn_ReinforceBtn.gameObject:SetActive(data.isActive or false)
    if self.warningTimer then
        self:RemoveTimer(self.warningTimer)
        self.warningTimer = nil
    end
    local showTime = data.endTime - os.server_time()
    self:SetActive(self.warningTimeTxt, showTime > 0)
    if showTime > 0 then
        self.warningTimer = self:CreateTimer(1, function()
            if data.endTime <= 0 then
                self.warningTimer = nil
                return true
            end
            showTime = data.endTime - os.server_time()
            if showTime <= 0 then
                self.warningTimer = nil
                return true
            end
            self.warningTimeTxt.text = time_util.GetCountDownString2(showTime)
        end)
    end
end

function UIView:SetTaskData (data)

    if not data then
        Common_Util.SetActive(self.Rtsf_TaskContent.gameObject, false)
        return
    end
    Common_Util.SetActive(self.Rtsf_TaskContent.gameObject, true)
    self.Text_TaskRewardBg.enabled = data.status >= 1
    self.Text_TaskRewardDesc.text = data.content
    local _width = self.Text_TaskRewardDesc.preferredWidth
    if _width >= 400 then
        _width = 400
    end
    _width = _width + 60
    UIUtil.SetSize(self.Rect_TaskContentBg, _width, 0, true)
    UIUtil.SetSize(self.Rect_TaskRewardBg, _width, 0, true)
    if not util.IsObjNull(self.Text_TaskRewardTextScroll) then
        self.Text_TaskRewardTextScroll:StopRunText()
        if _width >= 460 then
            self.Text_TaskRewardTextScroll:InitRunText()
        end
    end
    Common_Util.SetActive(self.Rtsf_TaskRewardRightBg, data.status >= 1)
    Common_Util.SetActive(self.Rtsf_TaskPlayEffect, false)
    Common_Util.SetActive(self.Rtsf_TaskLoopEffect, false)

    if self.taskFrameTimer then
        util.RemoveDelayCall(self.taskFrameTimer)
        self.taskFrameTimer = nil
    end
    if self.taskDelayTimer then
        util.RemoveDelayCall(self.taskDelayTimer)
        self.taskDelayTimer = nil
    end

    local frameCallBackToPlayNewEffect = function()
        Common_Util.SetActive(self.Rtsf_TaskPlayEffect, data.status >= 1)
        local status = data.status
        self.taskDelayTimer = util.DelayCallOnce(1, function()
            if self and self:IsValid() then
                Common_Util.SetActive(self.Rtsf_TaskPlayEffect, false)
                Common_Util.SetActive(self.Rtsf_TaskLoopEffect, status >= 1)
            end
        end)
    end

    self.taskFrameTimer = util.DelayCallOnce(0.1, function()
        if not self or not self:IsValid() then
            return
        end
        local width = self.Text_TaskRewardBg.rectTransform.rect.width;
        local xScale = width / 380
        Common_Util.SetLocalScale(self.Rtsf_TaskLoopEffect.transform, xScale, 1, 1)
        Common_Util.SetLocalScale(self.Rtsf_TaskPlayEffect.transform, xScale, 1, 1)

        if data.status < 1 then
            Common_Util.SetActive(self.Rtsf_TaskLoopEffect, false)
            Common_Util.SetActive(self.Rtsf_TaskPlayEffect, false)
        else
            frameCallBackToPlayNewEffect()
        end
    end)
end

function UIView:DoDonateAnim(_data)
    if _data then
        local gw_common_item_animation_mgr = require "gw_common_item_animation_mgr"
        for k, rewardData in pairs(_data) do
            gw_common_item_animation_mgr.DoItemAnimationTransformToTransform(rewardData.id, rewardData.num, self.Btn_TaskContent.gameObject.transform, nil, nil, { sortingLayerName = "All" })
        end
    end
end

function UIView:RefreshTaskWeakGuide()
    local isShow, time = gw_task_util.IsMainUITaskWeakGuideShow()
    self:StopAllWeakGuide()
    if not isShow then
        return false
    end
    self.mainSlgTaskTimer = util.DelayCallOnce(time, function()
        if not self or not self:IsValid() then
            return
        end
        local unforced_guide_mgr = require "unforced_guide_mgr"
        if unforced_guide_mgr.GetCurGuide() ~= nil then
            return
        end
        Common_Util.SetActive(self.Rtsf_TaskFinger, true)
        Common_Util.SetActive(self.Rtsf_TaskAperture, true)
    end)
end

function UIView:StopTaskWeakGuide()
    if self.mainSlgTaskTimer then
        util.RemoveDelayCall(self.mainSlgTaskTimer)
        self.mainSlgTaskTimer = nil
    end
    --隐藏 任务弱引导
    Common_Util.SetActive(self.Rtsf_TaskFinger, false)
    Common_Util.SetActive(self.Rtsf_TaskAperture, false)
end
---@public 刷新救人弱引导
function UIView:RefreshBomberWeakGuide()
    --hlgtodo 解救人质
    self:StopAllWeakGuide()
    self.mainSlgBomberTimer = util.DelayCallOnce(2, function()
        if not self or not self:IsValid() then
            return
        end
        local unforced_guide_mgr = require "unforced_guide_mgr"
        if unforced_guide_mgr.GetCurGuide() ~= nil then
            return
        end
        local bomberEnterWnd = self:GetBomberManEnter()
        if bomberEnterWnd then
            bomberEnterWnd:ShowWeakGuide()
        end
        --Common_Util.SetActive(self.Rtsf_BomberFinger, true)
        --Common_Util.SetActive(self.Rtsf_BomberAperture, true)
    end)
end
---@public 停止救人弱引导
function UIView:StopBomberWeakGuide()
    if self.mainSlgBomberTimer then
        util.RemoveDelayCall(self.mainSlgBomberTimer)
        self.mainSlgBomberTimer = nil
    end
    --hlgtodo 解救人质
    --隐藏 任务弱引导
    local bomberEnterWnd = self:GetBomberManEnter()
    if bomberEnterWnd then
        bomberEnterWnd:HideWeakGuide()
    end
    --Common_Util.SetActive(self.Rtsf_BomberFinger, false)
    --Common_Util.SetActive(self.Rtsf_BomberAperture, false)
end

function UIView:StopAllWeakGuide()
    self:StopBomberWeakGuide()
    self:StopTaskWeakGuide()
end

function UIView:StartTaskGuideFunc()
    if not self then
        return
    end
    if util.IsObjNull(self.Btn_TaskIconCanvas) then
        return
    end
    self.Btn_TaskIconCanvas.overrideSorting=true
    self.Btn_TaskIconCanvas.sortingLayerName="Top"
end

function UIView:OverTaskGuideFunc()
    if not self then
        return
    end
    if util.IsObjNull(self.Btn_TaskIconCanvas) then
        return
    end
    self.Btn_TaskIconCanvas.sortingLayerName = "Default"
end

----------------------------------------------------------------------------------------
--region 动态按钮区
----------------------------------------------------------------------------------------
---回收重置按钮的状态
function UIView.RecycleItem(component, item, data)
    if data.groupType == main_slg_const.MainButtonGroupType.RightTop or
            data.groupType == main_slg_const.MainButtonGroupType.RightTop2 or
            data.groupType == main_slg_const.MainButtonGroupType.LeftBottom2 or
            data.groupType == main_slg_const.MainButtonGroupType.LeftBottom4
    then
        local bubbleTf = item:Get("BubbleTrans",true)
        if not util.IsObjNull(bubbleTf) then
            UIUtil.SetActive(bubbleTf, false)
        end
        local bubbleButton = item:Get("bubbleButton", true)
        if not util.IsObjNull(bubbleButton) then
            UIUtil.SetActive(bubbleButton, false)
        end
        local lockIcon = item:Get("lock_img", true)
        if not util.IsObjNull(lockIcon) then
            UIUtil.SetActive(lockIcon, false)
        end
        local curStateTxt = item:Get("curState", true)
        if not util.IsObjNull(curStateTxt) then
            UIUtil.SetActive(curStateTxt, false)
        end
        local num = item:Get("num", true)
        if not util.IsObjNull(num) then
            UIUtil.SetActive(num, false)
        end
        local mask = item:Get("mask", true)
        if not util.IsObjNull(mask) then
            UIUtil.SetActive(mask, true)
            mask.enabled = false
        end
        local maskIcon = item:Get("mask_icon", true)
        if not util.IsObjNull(maskIcon) then
            UIUtil.SetActive(maskIcon, false)
        end
    end
end

function UIView.SetItemByData(component, item, data, index)
    --特别注意；如果自定义新增了ui元素，一定要在RecycleItem中重置各种UI表现
    window.RecycleItem(component, item, data)
    if not data.isHide then
        local icon = item:Get("icon", true)
        if not  util.IsObjNull(icon) then
            if  not data.loopIcon then
                main_slg_mgr.LoadMainIcon(icon, data.icon or data.iconFunc())
            end
            if data.groupType == main_slg_const.MainButtonGroupType.RightTop then
                icon:SetNativeSize()
            end
        end
        local bubbleButton = item:Get("bubbleButton", true)
        if bubbleButton then
            local bubbleImg = item:Get("bubbleImg", true)
            if data.bubbleImg then
                if data.loadBubbleIconFunc then
                    data.loadBubbleIconFunc(bubbleImg, data.bubbleImg)
                else
                    main_slg_mgr.LoadMainIcon(bubbleImg, data.bubbleImg)
                end
                bubbleButton.gameObject:SetActive(true)
            else
                bubbleButton.gameObject:SetActive(false)
            end
        end

        local text = item:Get("text", true)

        if not data.getTextTimerFunc then
            --是否显示倒计时 return bool(是否显示),倒计时
            local function IsShowCountDown()
                if data.timerShowFunc then
                    return data.timerShowFunc(),data.countdownTimeCfg and data.countdownTimeCfg.timeStamp() or 0,data.countdownTimeCfg.buttonType
                end
                if data.countdownTimeCfg == nil then
                    return false
                end
                if data.countdownTimeCfg.timeStamp == nil then
                    return false
                end
                local tempTime = data.countdownTimeCfg.timeStamp()
                if tempTime == nil then
                    return false
                end
                if tempTime <= 0 then
                    return false
                end
                return true, tempTime, data.countdownTimeCfg.buttonType
            end
            local isShowTime, timeStamp, buttonType = IsShowCountDown()
            if isShowTime then
                if not util.IsObjNull(text) then
                    --新建一个倒计时
                    Common_Util.SetActive(text,true)
                    window:CreateRightTopBtnTimer(buttonType, timeStamp, function(time)
                        if not util.IsObjNull(text) then
                            if data.timeStr then
                                text.text = string.format(data.timeStr, time_util.FormatTime5(time))
                            else
                                text.text = time_util.FormatTime5(time)
                            end
                        end
                    end)
                end
            else
                if not util.IsObjNull(text) then
                    if data.timerShowFunc and data.timerShowFunc() then
                        Common_Util.SetActive(text,false)
                    end
                    text.text = data.text
                end
            end
        end

        if data.lockState then
            local lockIcon = item:Get("lock_img", true)
            if lockIcon then
                local isShow = data.lockState()
                lockIcon.gameObject:SetActive(isShow)
            end
        else
            local lockIcon = item:Get("lock_img", true)
            if lockIcon then
                lockIcon.gameObject:SetActive(false)
            end
        end

        if data.curIconStateCfg then
            local curState = data.curIconStateCfg.State()
            local curStateName = data.curIconStateCfg.StateName()
            local curNum = data.curIconStateCfg.RewardNum()
            local curStateTxt = item:Get("curState", true)
            curStateTxt.text = curStateName
            curStateTxt.transform:SetActive(curState)
            if not util.IsObjNull(text) then 
                text.transform:SetActive(not curState)
            end
            
            local num = item:Get("num", true)
            if curNum then
                num.transform:SetActive(true)
                num.text = tostring(curNum)
            else
                num.transform:SetActive(false)
            end
        else
            local num = item:Get("num", true)
            local curStateTxt = item:Get("curState", true)
            if curStateTxt then
                curStateTxt.transform:SetActive(false)
            end
            if num then
                num.transform:SetActive(false)
            end
        end
        if window.TimerMgr and window.TimerMgr[item] and window.TimerMgr[item].SwitchTimer then
            util.RemoveDelayCall(window.TimerMgr[item].SwitchTimer)
            window.TimerMgr[item].SwitchTimer = nil
        end

        if window.TimerMgr and window.TimerMgr[item] and window.TimerMgr[item].CountDown then
            util.RemoveDelayCall(window.TimerMgr[item].CountDown)
            window.TimerMgr[item].CountDown = nil
        end
        if data.loopIcon then
            if not window.TimerMgr then
                window.TimerMgr = {}
            end
            if not window.TimerMgr[item] then
                window.TimerMgr[item] = {}
            end
            if data.hasAnimate then
                local Ani = item:Get("Ani")
                Ani.enabled = true
            end
            window:LimitGiftInfoUpdate(data, item)
        end

        if not item.data then
            item.data = {}
            local button = item:Get("button", true)
            button.onClick:AddListener(function()
                if item.data.clickFunc then
                    item.data.clickFunc(data)
                end
            end)

            if bubbleButton then
                bubbleButton.onClick:AddListener(function()
                    if item.data.bubbleClickFunc then
                        item.data.bubbleClickFunc(data)
                    end
                end)
            end
            
            local parent = item.transform
            local redData = data.redData
            --红点
            if redData then
                if redData.redType then
                    if redData.isActivityEntrance then
                        gw_activity_red_mgr.RegisterActivityEntranceRed(redData.funcParam[1], redData.funcParam[2])
                    end
                    window:BindUIRed(parent, redData.redType, redData.funcParam,
                            { redPath = redData.redPathType or red_const.Type.Default,pos = redData.pos or nil })
                end
            end

        end
        item.data.clickFunc = data.clickFunc
        item.data.bubbleClickFunc = data.bubbleClickFunc
        if (data.ShowEffect or data.ShowEffectList) and data.groupType then
            if not window.entryEffList then
                window.entryEffList = {}
            end
            window.entryEffList[data.groupType] = window.entryEffList[data.groupType] or {}
            local effectCheck = true
            if data.ShowEffectCheckFunc then
                effectCheck = data.ShowEffectCheckFunc()
            end
            local needShow = false
            local effectPath = ""
            if data.ShowEffect then
                effectPath = data.ShowEffect
                needShow = not string.IsNullOrEmpty(effectPath) and effectCheck
            elseif data.ShowEffectList then
                effectPath = data.ShowEffectList[effectCheck] or ""
                needShow = not string.IsNullOrEmpty(effectPath)
            end
            if needShow and not window.entryEffList[data.groupType][index] then
                window.entryEffList[data.groupType][index] = {}
                window.entryEffList[data.groupType][index][1] = effect_item.CEffectItem()
                --这里是因为右上的俩列要处理滑动，特效如果被mask挡住必须不显示
                local effectParentNode = item.transform
                if data.ShowEffectParentPath then
                    local node = UIUtil.GetComponent(item.transform, "Transform",data.ShowEffectParentPath)
                    if node then
                        effectParentNode = node
                    end
                end
                
                if data.groupType == main_slg_const.MainButtonGroupType.RightTop or data.groupType == main_slg_const.MainButtonGroupType.RightTop2 then
                    window.entryEffList[data.groupType][index][1]:Init(effectPath, effectParentNode, 100, nil, 1, true, true, 1)
                else
                    window.entryEffList[data.groupType][index][1]:Init(effectPath, effectParentNode, 100)
                end
            elseif not needShow and window.entryEffList[data.groupType][index] then
                --log.Error("No Create")
                if window.entryEffList[data.groupType][index] then
                    for i, v in pairs(window.entryEffList[data.groupType][index]) do
                        window.entryEffList[data.groupType][index][i]:Dispose()
                        window.entryEffList[data.groupType][index][i] = nil
                    end
                    window.entryEffList[data.groupType][index] = nil
                end
            elseif needShow and window.entryEffList[data.groupType][index] and window.entryEffList[data.groupType][index][1] then
                local path = window.entryEffList[data.groupType][index][1]:GetResPath()
                if path ~= effectPath then --路径不等，那么就先释放掉，再重新加载
                    if window.entryEffList[data.groupType][index] then
                        for i, v in pairs(window.entryEffList[data.groupType][index]) do
                            window.entryEffList[data.groupType][index][i]:Dispose()
                            window.entryEffList[data.groupType][index][i] = nil
                        end
                        window.entryEffList[data.groupType][index] = nil
                    end
                    
                    window.entryEffList[data.groupType][index] = {}
                    window.entryEffList[data.groupType][index][1] = effect_item.CEffectItem()
                    --这里是因为右上的俩列要处理滑动，特效如果被mask挡住必须不显示
                    local effectParentNode = item.transform
                    if data.ShowEffectParentPath then
                        local node = UIUtil.GetComponent(item.transform, "Transform",data.ShowEffectParentPath)
                        if node then
                            effectParentNode = node
                        end
                    end

                    if data.groupType == main_slg_const.MainButtonGroupType.RightTop or data.groupType == main_slg_const.MainButtonGroupType.RightTop2 then
                        window.entryEffList[data.groupType][index][1]:Init(effectPath, effectParentNode, 100, nil, 1, true, true, 1)
                    else
                        window.entryEffList[data.groupType][index][1]:Init(effectPath, effectParentNode, 100)
                    end
                end
            end
        end
        if data.isMask then
            local mask = item:Get("mask", true)
            if not util.IsObjNull(mask) then
                mask.enabled = true
            end
        end
        if data.maskIconShowFunc and data.maskIconShowFunc() then
            if data.maskIcon or data.maskIconFunc then
                local maskIcon = item:Get("mask_icon", true)
                UIUtil.SetActive(maskIcon, true)
                main_slg_mgr.LoadMainMaskIcon(maskIcon, data.maskIcon or data.maskIconFunc(), data.maskType)
            end
        end

        if data.checkDataFunc then
            if not window.checkData then
                window.checkData = {}
            end
            window.checkData[data.buttonType] = {
                func = data.checkDataFunc,
                interval = data.checkInterval,
                baseInterval = 0, --初始化
            }
        end
        
        if data.getTextTimerFunc then
            --添加通用定时器事件
            if not window.getTextTimerFuncArr then
                window.getTextTimerFuncArr = {}
            end
            window.getTextTimerFuncArr[data.buttonType] = {
                func = data.getTextTimerFunc,
                textUI = text
            }
        end
    end
    if data.isHide then
        --清楚定时器
        if window.getTextTimerFuncArr then
            --清除定时器置空
            window.getTextTimerFuncArr[data.buttonType] = nil
        end
        if window.bubbleFuncArr then
            window.bubbleFuncArr[data.buttonType] = nil
        end
        if window.checkData then
            window.checkData[data.buttonType] = nil
        end
    else
        if data.onRefreshFunc then
            data.onRefreshFunc(data)
        end
    end
    item.gameObject:SetActive(data.isHide ~= true)
    if data.objName then
        item.gameObject.name = data.objName
    end
    if data.buttonType == "vehicle"then
        window:CheckShowTrucksQiPao(item.transform)
    end   
    if data.buttonType == "allianceTrain"then
        window:CheckShowFeiChuanQiPao(item.transform)
    end     
    
end
function UIView:LimitGiftInfoUpdate(data, item)
    local timerIndex = 1
    local timerMgrGroup = {}
    local changeCD = data.changIconCD()
    if changeCD then
        local info = data.loopIcon()
        if info then
            local MinTime = info.MinTime
            local IconList = info.IconList
            local cacheAniShow = os.server_time()

            window.TimerMgr[item].CountDown = util.IntervalCall(1, function()
                if MinTime then
                    local offset = MinTime - os.server_time()
                    local curTime = time_util.FormatTime5(offset)
                    local text = item:Get("text")
                    text.text = curTime
                    if offset <= 0 then
                        util.RemoveDelayCall(window.TimerMgr[item].CountDown)
                        window.TimerMgr[item].CountDown = nil
                        return true
                    end
                end
            end)
            window.TimerMgr[item].SwitchTimer = util.IntervalCall(changeCD, function()
                if IconList and IconList[timerIndex] and not string.IsNullOrEmpty(IconList[timerIndex]) then
                    local loopIcon = item:Get("icon")
                    --print("Info[timerIndex]",timerIndex,Info[timerIndex].icon)
                    local canShowAni = data.hasAnimate()
                    window:CreateSubSprite("CreateGWActivityCommonAsset", loopIcon, IconList[timerIndex])
                    event.Trigger(event.UPDATE_SHOW_INDEX, timerIndex)
                    util.DelayCallOnce(changeCD - 0.2, function()
                        if canShowAni and not util.IsObjNull(item) then
                            -- 开启显示渐隐动画
                            local Ani = item:Get("Ani")
                            Ani:SetTrigger("ShowOff")
                        end
                    end)
                end
                -- print("data.loopIcon",#Info,timerIndex)
                if IconList and #IconList > 0 then
                    timerIndex = (timerIndex % #IconList) + 1
                else
                    util.RemoveDelayCall(window.TimerMgr[item].SwitchTimer)
                    window.TimerMgr[item].SwitchTimer = nil
                    return true
                end
            end)

        end
    end
end

---@see 刷新右侧侧边栏大小
function UIView:OnRefreshRightTopContent()
    if self.rigTopCount < rightMaxOpenCount then
        UIUtil.SetActive(self.Btn_RightTop_Expand, false)
    else
        UIUtil.SetActive(self.Btn_RightTop_Expand, true)
    end
    if self.rigTopCount == 0 and rightMaxOpenCount == 0 then
        UIUtil.SetActive(self.Btn_RightTop_Expand, false)
    end
end

function UIView:OnUpdateRightBottomBubbleState(value)
    if not self.totalList or not self.totalList[main_slg_const.MainButtonGroupType.RightBottom] then
        return
    end
    for i,v in pairs(self.totalList[main_slg_const.MainButtonGroupType.RightBottom].itemList) do
        local bubbleButton = v:Get("BubbleTrans", true)
        if not util.IsObjNull(bubbleButton) then
            UIUtil.SetActive(bubbleButton, value == true)
        end
    end
end

function UIView:SetBtnListByData(groupType, data)
    --只处理配置了的类型
    local item = self.buttonGroupTypeMap[groupType]
    if not item then
        return
    end
    self.totalList = self.totalList or {}
    self.totalList[groupType] = self.totalList[groupType] or item_list_component.New()
    local itemListComponent = self.totalList[groupType]
    itemListComponent:OriginDispose()
    itemListComponent:Init(item, self.SetItemByData)
    itemListComponent:SetData(data)
    local dic = self.totalList[groupType].itemDic
    --每次对一个组重新设置
    main_slg_mgr.ResetMainButtonTransform(groupType)
    for k, v in pairs(dic) do
        main_slg_mgr.SetMainButtonTransform(groupType, k, v.transform)
    end
    if groupType == main_slg_const.MainButtonGroupType.RightTop then
        self.rigTopCount = #data
        self.rigTopPriorityList = {}
        local totalCount = 0
        for k, v in pairs(rightShrinkShowItemPriority) do
            if dic[v] then
                local trans = dic[v].transform
                table.insert(self.rigTopPriorityList, { trans = trans, curIndex = trans:GetSiblingIndex(), shrinkIndex = totalCount })
                totalCount = totalCount + 1
            end
        end
        table.sort(self.rigTopPriorityList, function(a, b)
            return a.curIndex > b.curIndex
        end)
        rightMaxOpenCount = totalCount
        self.rightBgMinHeight = 350 - (3 - totalCount) * 110
        self:UpdateRightContent()
        --self:OnRefreshRightTopContent()
        --elseif groupType == main_slg_const.MainButtonGroupType.RightTop2 then   
        --    self.rigTop2Count = #data
        --    self:OnRefreshRightTopContent2()
    end
    --local list = self.totalList[item]
    --for k, v in ipairs(list) do
    --    v.gameObject:SetActive(false)
    --end
    --for k, v in ipairs(data) do
    --    list[k] = list[k] or self:NewItem(item)
    --    local tmpItem = list[k] 
    --    self:SetItemByData(tmpItem,v)
    --end
    
end

--设置右上方按钮列表
function UIView:SetRightTopBtnList(data)
    self:SetBtnListByData(main_slg_const.MainButtonGroupType.RightTop, data)
end

--设置左下方按钮列表
function UIView:SetLeftBottomBtnList(data)
    self:SetBtnListByData(main_slg_const.MainButtonGroupType.LeftBottom, data)
end

--设置左下方按钮列表2
function UIView:SetLeftBottomBtnList2(data)
    self:SetBtnListByData(main_slg_const.MainButtonGroupType.LeftBottom2, data)
end

--设置左下方按钮列表4
function UIView:SetLeftBottomBtnList4(data)
    self:SetBtnListByData(main_slg_const.MainButtonGroupType.LeftBottom4, data)
end

--设置右下方按钮列表
function UIView:SetRightBottomBtnList(data)
    self:SetBtnListByData(main_slg_const.MainButtonGroupType.RightBottom, data)
end

--设置左上方按钮列表
function UIView:SetLeftTopBtnList(data)
    self:SetBtnListByData(main_slg_const.MainButtonGroupType.LeftTop, data)
end

---显示/隐藏预警状态
function UIView:OnSandAlertNtf(bool)
    if bool then
        if self.AlertEffect then
            self.AlertEffect:SetVisible(false)
            self.AlertEffect:SetVisible(true)
        else
            local gw_sand_effect_const = require "gw_sand_effect_const"
            local gw_sand_effect_param = require "gw_sand_effect_param"
            local gw_effect_object = require "gw_effect_object"

            local newEffectParams = gw_sand_effect_param.NewEffectParam(gw_sand_effect_const.EffectName.Alert, nil)
            local newEffectBaseParams = gw_sand_effect_param.NewEffectBaseParam(gw_sand_effect_const.EffectName.Alert, nil)
            self.AlertEffect = gw_effect_object.CM("gw_effect_object"):Init(99999, self.UIRoot.transform, newEffectParams, newEffectBaseParams)
        end
    else
        if self.AlertEffect then
            self.AlertEffect:SetVisible(false)
        end
    end
end

--更新解救人质状态
function UIView:UpdateBomberMan(isOpen)
    Common_Util.SetActive(self.parent_BomberMan, isOpen)
    if  isOpen then
        local bomberEnterWnd = self:GetBomberManEnter()
        if not bomberEnterWnd and self.parent_BomberMan then
            self:SetBomberManEnter()
            
        end
    else
        --self:ClearBomberMan()
    end
end

function UIView:GetBomberManEnter()
    if self.bomberEnterWnd then
        local wnd = windowMgr:GetWindowObj("ui_bomberman_enter")
        if not wnd then
            self:ClearBomberMan()
        end
    end
    return self.bomberEnterWnd
end

function UIView:SetBomberManEnter()
    self.bomberEnterWnd = windowMgr:ShowModule("ui_bomberman_enter",nil,nil,{uiParent = self.parent_BomberMan})
end

function UIView:ClearBomberMan()
    --切换账号会自动close all
    if self.bomberEnterWnd then
        windowMgr:UnloadModule("ui_bomberman_enter")
        self.bomberEnterWnd = nil
    end
end

--更新怪物攻城状态
function UIView:UpdateMonsterAttack(isOpen)
    Common_Util.SetActive(self.parent_MonsterApproaching,isOpen) 
end
--修改怪物攻城UI尺寸
function UIView:OnRefreshMonsterUI(isSmall)
    if not self:IsValid() then
        return
    end
    local scale = isSmall and 0.85 or 1
    local height = isSmall and 100 or 180
    local layout = isSmall and -5 or 5
    self.parent_MonsterApproaching.localScale = {x = scale, y = scale}
    local size = self.parent_MonsterApproaching.sizeDelta
    self.parent_MonsterApproaching.sizeDelta = {x = size.x, y = height}
    self.Layout_LeftBottom_Content.spacing = layout
end

--region 活动按钮的倒计时
function UIView:CreateRightTopBtnTimer(buttonType, endTime, setTextFun)
    if not self.rightTopBtnTimer then
        self.rightTopBtnTimer = {}
    end

    if not self.endTimeTable then
        self.endTimeTable = {}
    end

    self.endTimeTable[buttonType] = endTime

    --if buttonType == 1005 then
    --    log.Error("创建倒计时时间：", self.endTime)
    --end

    if self.rightTopBtnTimer[buttonType] then
        util.RemoveDelayCall(self.rightTopBtnTimer[buttonType])
    end
    
    self.rightTopBtnTimer[buttonType] =  util.IntervalCall(1, function()
    --self.rightTopBtnTimer[buttonType] = self.rightTopBtnTimer[buttonType] or util.IntervalCall(1, function()
        if not self.endTimeTable[buttonType] then
            self.rightTopBtnTimer[buttonType] = nil
            return true
        end
        local tempTime = self.endTimeTable[buttonType] - os.server_time()
        if tempTime <= 0 then
            self.rightTopBtnTimer[buttonType] = nil
            --todo 马睿后面把这部分删了，时间到了走统一刷新
            local sand_ui_event_define = require "sand_ui_event_define"
            event.Trigger(sand_ui_event_define.GW_MAIN_BUTTONS_REFRESH, buttonType)
            return true
        end
        if setTextFun then
            setTextFun(tempTime)
        end
    end)
end
--endregion
--region 攻城号角
function UIView:SetWarHornState(state, isReady)
    if state then
        UIUtil.SetActive(self.Btn_WarHorns, true)
        if isReady then
            UIUtil.SetActive(self.Rtsf_WarStart, false)
            UIUtil.SetActive(self.Rtsf_WarReady, true)
        else
            UIUtil.SetActive(self.Rtsf_WarStart, true)
            UIUtil.SetActive(self.Rtsf_WarReady, false)
        end
    else
        UIUtil.SetActive(self.Btn_WarHorns, false)
    end
end

function UIView:SetWarHornsText(timeStamp)
    if timeStamp and timeStamp > 0 then
        self.Text_WarTime.text = time_util.FormatTimeHHMMSS(timeStamp)
    else
        self.Text_WarTime.text = ""
    end
end

function UIView:SetSimpleModelState(state)
    UIUtil.SetActive(self.Rtsf_SimpleModel, state or false)
end
--endregion

function UIView:SetDesertStormChess(state,rate)
    if state then
        local value = math.floor(rate * 100)
        UIUtil.SetActive(self.Btn_RewardChess,true)
        self.Text_RewardChessRate.text = string.format2("{%s1}%",value)
        self.Image_RewardChessRing.fillAmount = rate
        self:OnUpdateRightBottomBubbleState(false)
    else
        UIUtil.SetActive(self.Btn_RewardChess,false)
        self:OnUpdateRightBottomBubbleState(true)
    end
end

function UIView:SetDesertStormChessRate(rate)
    local value = math.floor(rate * 100)
    self.Text_RewardChessRate.text = string.format2("{%s1}%",value)
    self.Image_RewardChessRing.fillAmount = rate
end

function UIView:CheckShowTrucksQiPao(parent)
    if not self.carriageParent then
        self.carriageParent = parent
    end
    local intercity_trucks_data = require "intercity_trucks_data"
    local intercity_trucks_enum = require "intercity_trucks_enum"
    local data = intercity_trucks_data.GetSelfTruckList()
    local total, departCnt = intercity_trucks_data.GetDepartureCount()
    local isHaveDepart = false
    local hasReward = false
    local hasUnDepart = false
    if data and #data > 0 then
        for i = 1,4 do
            local v = data[i]
            if not v then
                log.Error("没有自己货车的初始数据=================truckid = ",i)
            else
                if(v.status == intercity_trucks_enum.CarriageStatus.CARRIAGE_STATUS_REWARD)then
                    hasReward = true
                elseif(v.status == intercity_trucks_enum.CarriageStatus.CARRIAGE_STATUS_DEPART)then
                    isHaveDepart = true
                elseif (v.status == intercity_trucks_enum.CarriageStatus.CARRIAGE_STATUS_UNLOCK)then
                    hasUnDepart = true
                end
            end
        end
    end
    local showBubble = total-departCnt >0 and (hasReward or hasUnDepart) and not isHaveDepart
    ---有发车次数并且无车在行驶中显示气泡
    local path = "ui/prefabs/gw/gw_main/vehicleqipao.prefab"
    local effect_item = require "effect_item"
    if self.carriageParent then
        self.qipao_Trucks = self.qipao_Trucks or effect_item.CEffectItem():Init(path, self.carriageParent,self.curOrder+5,function (obj)
            if window:IsValid() then
                self.OnOpenTrucksScene = function ()
                    if(self.qipao_Trucks)then
                        self.qipao_Trucks:Dispose()
                        self.qipao_Trucks = nil
                    end
                    local intercity_trucks_mgr = require "intercity_trucks_mgr"
                    intercity_trucks_mgr.OpenTrucksScene()
                end
                obj.transform.localPosition = {x = 122,y = 0,y = 0}
                obj.gameObject:SetActive(showBubble)
                local v_btn = obj.transform:GetComponent(typeof(Button))
                v_btn.onClick:RemoveAllListeners()
                v_btn.onClick:AddListener(self.OnOpenTrucksScene)

                local curOrder = self.curOrder > 10000 and self.curOrder or 15000

                local canvas = obj.transform:GetComponent(typeof(Canvas))
                if canvas and not canvas:IsNull() then
                    canvas.sortingOrder = curOrder + 50
                end
            end
        end)
    end
    if  self.qipao_Trucks and  self.qipao_Trucks.gameObject then
        self.qipao_Trucks.gameObject:SetActive(showBubble)
    end
end

function UIView:CheckShowFeiChuanQiPao(parent)
    if not self.feichuanParent then
        self.feichuanParent = parent
    end
    local intercity_alliance_train_mgr = require "intercity_alliance_train_mgr"
    local showBubble = intercity_alliance_train_mgr.CheckMainSlgIsOpenTrain()
    if showBubble then
        local red_system = require "red_system"
        red_system.TriggerRed(red_const.Enum.AllianceTrain)
    end

    ---有发车次数并且无车在行驶中显示气泡
    local path = "ui/prefabs/gw/gw_main/feichuanqipao.prefab"
    local effect_item = require "effect_item"
    if self.feichuanParent then
        self.qipao_feichuan = self.qipao_feichuan or effect_item.CEffectItem():Init(path, self.feichuanParent,self.curOrder+5,function (obj)
            if window:IsValid() then
                self.OnOpenAllianceTrainBuild = function ()
                    local gw_const = require "gw_const"
                    local ui_window_mgr = require "ui_window_mgr"
                    ui_window_mgr:CloseAll(gw_const.EHomeEnterCullLua)
                    local buildTypeId = gw_const.enBuildingType.enBuildingType_AllianceTrainCenter
                    local buildId = GWG.GWHomeMgr.cfg.GetBuildId(buildTypeId)
                    local gw_home_camera_util = require "gw_home_camera_util"
                    gw_home_camera_util.DoCameraToBuildMove(buildId,nil,nil,nil,nil,nil,nil,nil,4,-7)
                end
                obj.transform.localPosition = {x = 45,y = 0,y = 0}
                obj.gameObject:SetActive(showBubble)
                local v_btn = obj.transform:GetComponent(typeof(Button))
                v_btn.onClick:RemoveAllListeners()
                v_btn.onClick:AddListener(self.OnOpenAllianceTrainBuild)

                local curOrder = self.curOrder > 10000 and self.curOrder or 15000

                local canvas = obj.transform:GetComponent(typeof(Canvas))
                if canvas and not canvas:IsNull() then
                    canvas.sortingOrder = curOrder + 50
                end
            end
        end)
    end
    if  self.qipao_feichuan and  self.qipao_feichuan.gameObject then
        self.qipao_feichuan.gameObject:SetActive(showBubble)
    end
end

--region 战区对决-王城争夺
function UIView:SetZoneDuelState(state)
    UIUtil.SetActive(self.Btn_ZoneDuel, state)
end

function UIView:SetZoneDuelText(timeStamp)
    if timeStamp and timeStamp > 0 then
        self.Text_duelZoneTime.text = time_util.FormatTimeHHMMSS(timeStamp)
    else
        self.Text_duelZoneTime.text = ""
    end
end

function UIView:SetZoneDuelCongressData(selfData, enemyData)
    self:UpdateCongressData(self.ScrItem_selfCongressProgress, selfData)
    self:UpdateCongressData(self.ScrItem_enemyCongressProgress, enemyData)
end

function UIView:UpdateCongressData(scrItem, data)
    if not scrItem or not data then
        return
    end

    local mask = scrItem:Get("mask", true)
    if mask then
        mask.fillAmount = data.progress or 0
    end

    local worldID = scrItem:Get("worldID", true)
    if worldID and data.worldID then
        worldID.text = string.format("#%s", ui_util.GetWorldIDToShowWorldID(data.worldID, nil, ui_util.WorldIDRangeType.Normal))
    end

    local progress = scrItem:Get("progress", true)
    if progress then
        if data.progress and data.progress > 1 then
            progress.text = string.format("%.1f%%", data.progress * 100)
        else
            progress.text = "0%"
        end
    end
end

--endregion

--region 后台下载
--检测后台下载UI是否需要刷新
--function CheckGrayLoadUINeedRefresh()
--    if window and window:IsValid() then
--        local self = window
--        if not self.Btn_grayLoad then
--            return
--        end
--
--        return not self.Btn_grayLoad.gameObject.activeInHierarchy or not self.loadTicker
--    end
--end

--后台下载按钮点击
--function UIView:OnGrayLoadBtnClick()
--    --local gray_load_mgr = require "gray_load_mgr"
--    --local param = gray_load_mgr.GetGrayLoadParam()
--    --local grayBtnCallBack = param.grayBtnCallBack
--    --if grayBtnCallBack then
--    --    grayBtnCallBack()
--    --else
--    --    windowMgr:ShowModule("ui_grayload")
--    --end
--end

--后台下载按钮显示
--function ShowGrayLoadBtn()
--    if window and window:IsValid() then
--        local self = window
--        if not self.Btn_grayLoad then
--            return
--        end
--        
--        local gray_load_mgr = require "gray_load_mgr"
--        local loadSize, total, start_or_pause = gray_load_mgr.GetLoadParam()
--        local bFinishLoad = total > 0 and loadSize >= total
--        local preloadType = gray_load_mgr.IsCurGrayLoadPreloadType()
--        local param = gray_load_mgr.GetGrayLoadParam()
--        local grayBtnNotCloseOnFin = param.grayBtnNotCloseOnFin
--        
--        if preloadType then
--            --如果是预下载类型
--            if bFinishLoad then
--                local active = grayBtnNotCloseOnFin and true or false
--                self.Btn_grayLoad:SetActive(active)
--                
--                if active and self.Text_grayLoadPercent then
--                    local rate = math.floor(loadSize * 100 / total)
--                    self.Text_grayLoadPercent.text = rate .. "%"
--                end
--            end
--        end
--    end
--end
--
--function CheckGrayLoad()
--    if window and window:IsValid() then
--
--        local self = window
--
--        -- local version_mgr = require "version_mgr"
--        -- local trunkVersion, branchVersion = version_mgr.GetSvnVersion()
--
--        -- if trunkVersion ~= branchVersion then return end
--
--        if not self.Btn_grayLoad then
--            return
--        end
--        --grayload
--        local gray_load_mgr = require "gray_load_mgr"
--
--        local loadSize, total, start_or_pause = gray_load_mgr.GetLoadParam()
--        local bGrayLoad = total > 0 and loadSize < total
--        self.Btn_grayLoad:SetActive(bGrayLoad)
--        local removeLoadTicker = function()
--            --删除加载显示定时器
--            if self.loadTicker then
--                util.RemoveDelayCall(self.loadTicker)
--                self.loadTicker = nil
--            end
--        end
--        removeLoadTicker()
--        if bGrayLoad then
--            local ui_login_main_mgr = require "ui_login_main_mgr"
--            local enter = ui_login_main_mgr.GetOnceLogined()
--            local delay = 0
--            self.loadTicker = util.DelayCallOnce(delay, function()
--                local loadSize, total, start_or_pause = gray_load_mgr.GetLoadParam()
--                local bGrayLoad = total > 0 and loadSize < total
--                self.Btn_grayLoad:SetActive(bGrayLoad)
--                ShowGrayLoadBtn()
--                if bGrayLoad then
--                    local rate = math.floor(loadSize * 100 / total)
--                    --  --print("tick",rate,total)
--                    self.Text_grayLoadPercent.text = rate .. "%"
--                    return 0.5
--                else
--                    windowMgr:UnloadModule("ui_grayload")
--                    removeLoadTicker()
--                    return
--                end
--            end)
--        end
--        ShowGrayLoadBtn()
--    end
--end --///<<< function

--endregion



----------------------------------------------------------------------------------------
--endregion 动态按钮区
----------------------------------------------------------------------------------------

---********************end功能函数区**********---
--endregion
--region WindowInherited
local CUIView = class(ui_base, nil, UIView)
--endregion 

--region static ModuleFunction 
-- 特别注意，当前并不是由controller层来驱动ui的生命流程的 
-- 当前因为需要view层 也就是ui_base来驱动ui的init  加载完成，show等流程，所以流程仍然保留，而controller层的流程逻辑受view流程影响，
-- view对应的Init/Show 加载完后，当前会通过事件同步调用controller层的Init/Show流程，controller层的流程逻辑才会执行。
--当前仍然保留了静态的Show，Close接口流程
function Show(data)
    if data and type(data) == "table" and data["uipath"] then
        ui_path = data["uipath"];
    end
    if window == nil then
        window = CUIView()
        window._NAME = _NAME
        window.isOpenOrginIsStretch = false
        window:LoadUIResource(ui_path, nil, nil, nil, false, true)
    end
    --这里调用window:Show()  会造成多次调用 但hide后却又需要 uiwindowmgr有问题 TODO
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close()
    end
    if window and not window.recycle_ui then
        window = nil
    end
end
--endregion
