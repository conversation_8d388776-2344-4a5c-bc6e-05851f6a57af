local require = require
local pairs = pairs
local ipairs = ipairs
local print = print
local tonumber = tonumber
local string = string
local tostring = tostring
local table = table
local math = math
local os = os
local Edump = Edump
local event = require "event"
local game_config = require "game_config"
local server_info = require "server_info"
local q1sdk = require "q1sdk"
local game = require "game"
local adjust = require "adjust"
local log = require "log"
local server_data = require "server_data"
local util = require "util"
local http_inst = require "http_inst"
local json = require "dkjson"
local url_operation_mgr = require"url_operation_mgr"

local GetTcpServerInfoData = server_data.GetTcpServerInfo
local GetUdpServerInfoData = server_data.GetUdpServerInfo

local Application = CS.UnityEngine.Application
local PlayerPrefs = CS.UnityEngine.PlayerPrefs
local IsInEditor = CS.War.Script.Utility.IsInEditor
local Utility = CS.War.Script.Utility
local RuntimePlatform = CS.UnityEngine.RuntimePlatform

module("setting_server_data")

-- 获取当前服务器信息
local curServerInfo = nil
-- 当前登录的worldid
local loginWorldID = nil
-- 当前连接的网络类型
local connectType = nil

local accountLists = nil
local reqTimes = 0 -- 请求次数
local startReqTime = nil--智量引擎请求开始时间
local newRecommendServerID = nil--智量引擎推荐id
local reqWaitTime = 4 -- 智量引擎登录阶段等待时间最大值 4s
local rejustServerID = -1 -- 拒绝登录时的服务器设置为-1
--设置所有区角色列表
function SetAllAccountListData(datas)
    if datas then
        accountLists = {}
        local worldid = 0
        local player_mgr = require "player_mgr"
        local myRoleID = player_mgr.GetPlayerRoleID() or 0
        for i = 1, #datas do
            local serverItemSource = datas[i]
            if serverItemSource then
                serverItem = {}
                serverItem.roleid = serverItemSource.roleid
                serverItem.name = serverItemSource.name
                serverItem.level = serverItemSource.level
                serverItem.worldid = serverItemSource.worldid
                local faceStr = serverItemSource.faceid
                if faceStr and not string.IsNullOrEmpty(faceStr) then 
                    faceStr = serverItemSource.faceStr
                end
                serverItem.faceid = faceStr
                serverItem.flagid = serverItemSource.flagid
                serverItem.flagnextsetime = serverItemSource.flagnextsetime
                serverItem.regionid = GetRegionID(serverItemSource.worldid)
                accountLists[serverItem.worldid] = serverItem
                if myRoleID == serverItemSource.roleid then
                    -- 设置国旗id 以及冷却时间
                    player_mgr.SetPlayerNationalFlag(serverItemSource.flagid, serverItemSource.flagnextsetime)
                end
            end
        end
    end
end

---@funtion 根据worldid获取大区ID
function GetRegionID(worldid)
    worldid = worldid or GetLoginWorldID()
    --[[大区ID: 1.外网中国区 2.外网欧美区 3.外网亚太区 ]]
    local regionid = 0
    if game_config.ENABLE_Q1_DEBUG_MODE then
        regionid = 2 --外网欧美区
    end
    local bFindServer = false
    -- 后续逻辑由于包含权重随机逻辑，每次执行结果可能发生变动
    local serverInfo = GetServerInfo(worldid)
    if serverInfo then
        if serverInfo.regionID then
            regionid = serverInfo.regionID
            bFindServer = true
        end
    end

    if not bFindServer and not Application.isEditor then
        log.LoginError("根据worldid未找到服务器:", worldid)
        event.RecodeTrigger("can_not_find_server", {})
    end
    return regionid
end

function SetNAAccountListData(datas)
    if datas then
        if not accountLists then
            accountLists = {}
        end
        local worldid = 0
        for i = 1, #datas do
            worldid = datas[i].worldid
            accountLists[worldid] = datas[i]
        end
    end
end

function SetSEAAccountListData(datas)
    if datas then
        if not accountLists then
            accountLists = {}
        end
        local worldid = 0
        for i = 1, #datas do
            worldid = datas[i].worldid
            accountLists[worldid] = datas[i]
        end
    end
end

--亚太服是否有角色
function HasSEARole()
    if accountLists then
        for i, v in pairs(accountLists) do
            if v.regionid and v.regionid == 3 then
                return true
            end
        end
    end
    -- log.Warning("亚太服没有角色")
    return false
end

--转到lua表
function TurnLuaTable(csList)
    if not csList.Length then
        return csList
    end
    local temTable = {}
    for j = 0, csList.Length - 1 do
        table.insert(temTable, csList[j])
    end
    return temTable
end

function GetAllAccountListData()
    return accountLists
end

---@funtion GetTcpServerListData 目前获得所有服务器列表也是用的这个
function GetTcpServerListData(region_id)
    --region_id = region_id or 1
    local serverList = {}
    local tcpLists = GetTcpServerInfoData()
    local index = 1
    local len = tcpLists.Length or #tcpLists
    local lenAdd = 0
    if tcpLists.Length then
        lenAdd = -1
    end

    local serverInfo = nil
    local tcpInfo = nil
    for i = 1 + lenAdd, len + lenAdd do
        tcpInfo = tcpLists[i]
        if region_id == nil or region_id == tcpInfo.regionId then
            -- 大区过滤
            if tcpInfo.isOpenServer then
                serverInfo = {
                    id = tcpInfo.serverid,
                    --openServerId = tcpInfo.openServerId,
                    name = tcpInfo.servername,
                    isnew = tcpInfo.isnew,
                    isrepaired = tcpInfo.isrepaired,
                    regionID = tcpInfo.regionId,
                    isOpenServer = tcpInfo.isOpenServer,
                    weight = tcpInfo.weight,
                    regionId = tcpInfo.regionId,
                    areaID = tcpInfo.areaID,
                    isws = not not tcpInfo.isws,
                }
                serverList[index] = serverInfo
                serverInfo.ipList = TurnLuaTable(tcpInfo.ipList)
                serverInfo.portList = TurnLuaTable(tcpInfo.portList)

                index = index + 1
            else
                -- 服务器尚未开放，不能被外部可见
            end
        end
    end
    return serverList
end
---@funtion GetTcpServerByServerID 获取某个id的服务器字段
function GetTcpServerByServerID(serverID)
    local tcpLists = GetTcpServerInfoData()
    local len = tcpLists.Length or #tcpLists
    local lenAdd = 0
    if tcpLists.Length then
        lenAdd = -1
    end
    local serverInfo = nil
    local tcpInfo = nil
    for i = 1 + lenAdd, len + lenAdd do
        tcpInfo = tcpLists[i]
        if serverID and serverID == tcpInfo.server_id then
            -- 大区过滤
            if tcpInfo.isOpenServer then
                serverInfo = {
                    id = tcpInfo.serverid,
                    --openServerId = tcpInfo.openServerId,
                    name = tcpInfo.servername,
                    isnew = tcpInfo.isnew,
                    isrepaired = tcpInfo.isrepaired,
                    regionID = tcpInfo.regionId,
                    isOpenServer = tcpInfo.isOpenServer,
                    weight = tcpInfo.weight,
                    regionId = tcpInfo.regionId,
                    areaID = tcpInfo.areaID,
                    isws = not not tcpInfo.isws,
                }
                serverInfo.ipList = TurnLuaTable(tcpInfo.ipList)
                serverInfo.portList = TurnLuaTable(tcpInfo.portList)
                return serverInfo
            else
                -- 服务器尚未开放，不能被外部可见
            end
        end
    end
end
---@funtion 获取udp服务器列表
function GetUdpServerListData()
    local serverList = {}
    local index = 1

    local udpLists = GetUdpServerInfoData()
    local len = udpLists.Length or #udpLists
    local lenAdd = 0
    if udpLists.Length then
        lenAdd = -1
    end

    for i = 1 + lenAdd, len + lenAdd do
        -- for i = 0, udpLists.Length - 1 do
        serverList[index] = {}
        serverList[index].id = udpLists[i].serverid
        serverList[index].ipList = TurnLuaTable(udpLists[i].ipList)
        serverList[index].portList = TurnLuaTable(udpLists[i].portList)
        serverList[index].name = udpLists[i].servername
        serverList[index].isnew = udpLists[i].isnew
        serverList[index].isws = udpLists[i].isws
        serverList[index].isrepaired = udpLists[i].isrepaired
        index = index + 1
    end
    return serverList
end
---@funtion 根据worldid获取tcp和ws服务器ip和端口
function GetTcpIpPortByWorldID(worldid)
    local tcpIpPort = nil
    if worldid then
        local serverInfo = GetServerInfo(worldid)
        if serverInfo then
            tcpIpPort = {}
            tcpIpPort.ipList = serverInfo.ipList
            tcpIpPort.portList = serverInfo.portList
            return tcpIpPort
        end
    end
    return tcpIpPort
end
---@funtion 根据worldid获取udp服务器ip和端口
function GetUdpIpPortByWorldID(worldid)
    local updIpPort = nil
    if worldid then
        local serverInfo = GetServerInfo(worldid, true)
        if serverInfo then
            updIpPort = {}
            updIpPort.ipList = serverInfo.ipList
            updIpPort.portList = serverInfo.portList
            return updIpPort
        end
    end
    return updIpPort
end
---@funtion 编辑器获取服务器列表数据
function GetEditServerListData()
    local serverList = {}
    if IsInEditor and IsInEditor() then
        for i, v in ipairs(server_info.servers) do
            serverList[i] = {}
            serverList[i].id = tonumber(v.worldid)
            serverList[i].ipList = { [1] = tostring(v[1].ip) }
            serverList[i].portList = { [1] = tonumber(v[1].port) }
            serverList[i].name = v.name
            serverList[i].isnew = (not not v.isnew)
            serverList[i].isws = (not not v.isws)
            serverList[i].isrepaired = false
            serverList[i].openServerId = tonumber(v.OpenServerId)
        end
    end
    return serverList
end

function GetEditIpPortByWorldID(worldid)
    local ipPort = nil
    if IsInEditor and IsInEditor() then
        for i, v in ipairs(server_info.servers) do
            if worldid and worldid == v.worldid then
                ipPort = {}
                ipPort.ipList = { [1] = tostring(v[1].ip) }
                ipPort.portList = { [1] = tonumber(v[1].port) }
                return ipPort
            end
        end
    end
    return ipPort
end

---@funtion server_list_data 获取服务器列表数据 
---@param region_id number 大区id
---@return ServerInfo[] server_list 服务器列表数据
function GetServerListData(region_id)
    local server_list_data = nil
    if IsInEditor and IsInEditor() then
        server_list_data = GetEditServerListData()
    else
        server_list_data = GetTcpServerListData(region_id)
    end

    return server_list_data
end
---@funtion 随机一个新区世界id
function GetNewWorldID()
    local worldid = nil
    local newList = {}
    local server_list_data = GetServerListData()
    for i, v in ipairs(server_list_data) do
        if v.isnew == true then
            table.insert(newList, v)
        end
    end

    if table.getn(newList) >= 1 then
        local id = math.random(1, table.getn(newList))
        worldid = newList[id].serverid
    end
    return worldid
end

function SetLoginWorldID(worldId)
    curServerInfo = nil
    loginWorldID = worldId
    PlayerPrefs.SetInt("worldid", worldId)
end

---@funtion GetLoginWorldID 获取登录服务器ID
---@return int id
function GetLoginWorldID()
    return loginWorldID or PlayerPrefs.GetInt("worldid")
end

function SetConnectType(type)
    connectType = type
    PlayerPrefs.SetInt("connecttype", type)
end
---@funtion GetConnectType 获取登录服务器ID
---@return int id
function GetConnectType()
    return connectType or PlayerPrefs.GetInt("connecttype", server_data.CONNECT_TYPE_WEB)
end
---@funtion SetLoginServer 记录登录服务器
function SetLoginServer(serverInfo)
    local isws = not not serverInfo.isws
    local is_ws = isws and util.IsWebSocket()
    local connectType = is_ws and server_data.CONNECT_TYPE_WEB or server_data.CONNECT_TYPE_TCP
    SetConnectType(connectType)
end
---@funtion 获取切换服务器的ip和端口
---@param number worldid 服务器ID
---@param bool is_udp 是否是udp，默认不填
---@return table ipList ip列表
---@return table portList port列表
function GetChangeIpport(worldid, is_udp)
    worldid = worldid or GetLoginWorldID()
    local serverInfo = GetServerInfo(worldid, is_udp)
    if serverInfo then
        return serverInfo.ipList, serverInfo.portList
    end
    return {}, {}
end
---@funtion IsConnectWebSocket 是否连接websocket
function IsConnectWebSocket()
    return GetConnectType() == server_data.CONNECT_TYPE_WEB
end
---@funtion GetServerName 获取服务器名称
function GetServerName(worldid)
    local serverInfo = GetServerInfo(worldid)
    if serverInfo then
        return serverInfo.name
    end
    local log = require "log"
    log.Warning("服务器ID异常:", worldid)
    -- 01-29 尝试重新更新一次服务器列表
    if not hasRetry then
        hasRetry = true
        event.Trigger(event.REQ_SERVER_LIST)
    end
    return worldid % 1000
end

function FilterServersByWorldId(server_list_data, worldId)
    local filtered_list = {}
    for i, v in ipairs(server_list_data) do
        if v.id == worldId then
            table.insert(filtered_list, v)
        end
    end
    return filtered_list
end

---@deprecated 通过worldid获取服务器列表
---@param number worldId 服务器ID
---@return table server_list 服务器信息列表
function GetServerList(worldId)
    local server_list_data = GetServerListData()
    if not server_list_data then
        return nil
    end
    local cur_server_list = {}
    --log.Warning("---GetServerList--worldId:" .. worldId)
    cur_server_list = FilterServersByWorldId(server_list_data, worldId)
    return cur_server_list
end
function GetUdpServerList(worldId)
    local is_editor = IsInEditor and IsInEditor()
    local server_list_data = is_editor and GetServerListData() or GetUdpServerListData()
    --log.Warning("---GetUdpServerList--worldId:" .. worldId)
    local cur_udp_server_list = {}
    cur_udp_server_list = FilterServersByWorldId(server_list_data, worldId)
    return cur_udp_server_list
end
---@deprecated 获取当前服务器信息
---@param number worldId 服务器ID
---@param bool is_udp 是否是udp，默认不填
---@return table server_info 服务器信息
function GetServerInfo(worldId, is_udp)
    local server_list = is_udp and GetUdpServerList(worldId) or GetServerList(worldId)
    if not server_list or #server_list == 0 then
        return nil
    end
    local serverInfo = nil
    local server_count = #server_list
    -- 如果是有多个相同worldid的服务器，则根据网络类型选择一个
    if server_count >= 2 then
        local is_ws = GetConnectType() == server_data.CONNECT_TYPE_WEB and util.IsWebSocket()
        for i = 1, server_count do
            local info = server_list[i]
            if is_ws and info.isws and info.isws == true then
                return info
            end
            serverInfo = info
        end
    else
        -- 如果只有一个，则直接返回
        serverInfo = server_list[1]
    end
    return serverInfo
end

---@funtion GetLoginWorldName 获取登录服务器名称
function GetLoginWorldName()
    local worldid = GetLoginWorldID()
    if worldid == 0 then return "" end
    local server_info = GetCurServerInfo()
    return server_info and server_info.name or worldid % 1000
end

---@funtion GetCurServerInfo 获取当前服务器信息
function GetCurServerInfo()
    if curServerInfo ~= nil then
        return curServerInfo
    end
    local worldid = GetLoginWorldID()
    curServerInfo = GetServerInfo(worldid)
    return curServerInfo
end
---@funtion GetLoginWorldAreaID 从serverinfo获取areaid
function GetLoginWorldAreaID()
    local areaId = nil
    if curServerInfo == nil then
        curServerInfo = GetCurServerInfo()
    end
    if curServerInfo then
        areaId = curServerInfo.areaID
        log.Warning("GetLoginWorldAreaID:", areaId)
    end
    return areaId or nil
end

function ReportSelectServerReport(eventName)
    local pid = game_config.CHANNEL_ID
    local worldID = GetLoginWorldID()
    local userID = ""
    local gameUserID = 0
    if game.actors and #game.actors > 0 then
        local actorInfo = game.actors[1]
        userID = actorInfo.BCUserID or ''
        gameUserID = actorInfo.userID
    end

    if eventName == "user_login" then
        local savedWorldId = PlayerPrefs.GetInt("worldid")
        if savedWorldId ~= worldID then
            log.Error("EventReport eventName:", eventName, ", report serverId:", worldID, ",savedWorldId:", savedWorldId)
        else
            log.Warning("EventReport eventName:", eventName, ", report serverId:", worldID, ",savedWorldId:", savedWorldId)
        end
    elseif eventName == "select_server" then
        q1sdk.trackSelectServer(worldID, userID)
    end

    -- 如果有Adjust用户数据，则上报相关的广告数据
    local adjustAtt = adjust.GetAttribution()

    local reportMsg = {
        pid = game_config.CHANNEL_ID,
        uuid = q1sdk.GetUUID(),
        imei_idfa = q1sdk.GetImeiMD5(),
        radid = q1sdk.GetRadid(),
        rsid = q1sdk.GetRsid(),
        --server_id = worldID,
        userid = gameUserID,
        user = userID,
        adjust_network = adjustAtt.network or "",
        adjust_campaign = adjustAtt.campaign or "",
        adjust_adgroup = adjustAtt.adgroup or "",
        adjust_creative = adjustAtt.creative or "",
    }
    event.EventReport(eventName, reportMsg)

end

local IPLIST_NA = { "*******", "*******" }
local IPLIST_SEA = { "*******", "*******", "*******", "*******", "*******", "*******", "*******" }

function IsNA()
    if game_config.REGION_ID == 1 then
        return true
    end

    local ip = Utility.getIP("pub-login.q1.com")
    for i, v in ipairs(IPLIST_NA) do
        if ip == v then
            return true
        end
    end
    return false
end

function IsSEA()

    if game_config.REGION_ID == 1 then
        return false
    end

    local ip = Utility.getIP("pub-login.q1.com")
    for i, v in ipairs(IPLIST_SEA) do
        if ip == v then
            return true
        end
    end
    return false
end

local newestWorldId = nil
local newestServerInfo = nil
local recommendServerCount = 0
function GetNewestWorldInfo()
    -- startTime = os.server_time()
    if newestWorldId ~= nil and newestWorldId ~= 0 then
        -- 直接返回缓存的最新服务器 id，防止在一次登录过程，由于服务器列表变动等原因导致的选择最新服务器前后不一致，逻辑错乱
        return newestWorldId, newestServerInfo, recommendServerCount
    end
    -- 后续逻辑由于包含权重随机逻辑，每次执行结果可能发生变动
    local isIsInEditor = IsInEditor()
    local serverDatas = {}
    if isIsInEditor then
        serverDatas = GetEditServerListData()
    else
        serverDatas = GetTcpServerListData()                                --这里不需要ip信息
    end

    local isSend = url_operation_mgr.GetConfig("IsRecommendSend")-- 是否开智量引擎推荐服务器
    if isSend == true then
        log.LoginWarning("[zlyq] 智量引擎推荐id:", newRecommendServerID)
        if newRecommendServerID then
            if newRecommendServerID == rejustServerID then
                return newRecommendServerID
            end
            newestWorldId = newRecommendServerID
            newestServerInfo = GetTcpServerByServerID(newestWorldId)
            if newestServerInfo then
                event.Trigger(event.GAME_EVENT_REPORT, "GetZLYQRecommendServerSuccess", {
                    serverId = newRecommendServerID
                })-- 智量引擎推荐区服获取成功打点
                log.LoginWarning("[zlyq] 智量引擎推荐返回:", newestWorldId, Edump(newestServerInfo), recommendServerCount)
                return newestWorldId, newestServerInfo, recommendServerCount
            else
                event.Trigger(event.GAME_EVENT_REPORT, "ZLYQRecommendServerNotFind", {
                    serverId = newRecommendServerID
                })-- 智量引擎推荐的服务器id在服务器列表中找不到打点
                log.Error("[zlyq] 引擎推荐的服务器id在服务器列表中找不到, serverid: ", newestWorldId, ", 开始走保底")
                newestWorldId = nil
            end
        else--检测是否超时
            local notEnterGame = PlayerPrefs.GetInt("notEnterGame", 0) -- 被拒绝过
			log.Warning("[zlyq] 是否被拒绝过 notEnterGame：", notEnterGame)
            if notEnterGame == rejustServerID then
                return rejustServerID
            end
            if startReqTime then
                if os.server_time() - startReqTime > reqWaitTime or reqTimes >= 3 then
                    log.Error("[zlyq] 智量引擎返回超时! 走原来的逻辑 startReqTime = ", startReqTime, " 请求次数reqTimes: ", reqTimes)
                else
                    log.LoginWarning("[zlyq] 智量引擎未超时 等待下次重试~ startReqTime = ", startReqTime)
                    return 0
                end
            else
                log.LoginWarning("[zlyq] 检测是否超时 未开始登录服务器的流程 startReqTime == ", startReqTime)
                return 0
            end
        end
    end

    local isContainNA = false
    local isContainSEA = false
    for i = 1, #serverDatas do
        if serverDatas[i].regionID == 2 then
            isContainNA = true
        elseif serverDatas[i].regionID == 3 then
            isContainSEA = true
        end
        if isContainNA == true and isContainSEA == true then
            break
        end
    end

    local recommendServerData = {}
    if game_config.VERSION_TYPE == 0 then
        recommendServerData = GetTcpServerListData(1)
    elseif isContainNA and not isContainSEA then
        recommendServerData = GetTcpServerListData(2)
    elseif isContainSEA and not isContainNA then
        recommendServerData = GetTcpServerListData(3)
    elseif isContainNA and isContainSEA then
        local regionID = 0
        if IsNA() then
            recommendServerData = GetTcpServerListData(2)
            regionID = 2
        elseif IsSEA() then
            recommendServerData = GetTcpServerListData(3)
            regionID = 3
        end
        --韩国包新用户都走韩国专服，韩国专服regionID：2，亚太服RegionID：3
        local const = require "const"
        if const.IsKoreaChannel() then
            --是否配置韩服，配置了韩服就优先进入韩服，否则还是亚太服
            recommendServerData = GetTcpServerListData(2)
            regionID = 2
            if not recommendServerData or #recommendServerData <= 0 then
                recommendServerData = GetTcpServerListData(3)
                regionID = 3
            end
        end
    else
        log.LoginError("区服配置错误, VERSION_TYPE:", game_config.VERSION_TYPE, ",isContainNA:", isContainNA, ",isContainSEA:", isContainSEA)
    end
    log.LoginWarning("VERSION_TYPE:", game_config.VERSION_TYPE, ",isContainNA:", isContainNA, ",isContainSEA:", isContainSEA)

    recommendServerCount = #recommendServerData
    if recommendServerCount == 0 then
        log.LoginError("GetNewestWorldId 推荐服务器列表为空")
    end

    -- 2020/12/22 不再使用最新新服，需求能支持按权重随机选择服务器，比如可以通过此配置来完成针对服务器对玩家分流
    -- 获取新服务器列表
    local newServerSet = {}
    local newServerCount = 0
    local newServerInfo = nil
    local totalWeight = 0
    for i = 1, recommendServerCount do
        newServerInfo = recommendServerData[i]
        if newServerInfo.isnew then
            newServerCount = newServerCount + 1
            totalWeight = totalWeight + newServerInfo.weight
            newServerSet[newServerCount] = newServerInfo
        end
    end

    -- 从新服务器列表中按权重选取服务器
    local worldId = 0
    if newServerCount > 0 then
        log.LoginWarning("新服务器数量:", newServerCount)
        local serverInfo = GetRandomServerInfo(newServerSet, totalWeight)
        newestServerInfo = serverInfo
        worldId = newestServerInfo.id
    else
        -- 没有新服务器时，取第一个服务器，目前最新的服务器配置在最新
        if recommendServerCount > 0 then
            newestServerInfo = recommendServerData[1]
            worldId = newestServerInfo.id
        end
    end
    -- 上面条件都不满足的情况下， worldId 将为 0

    -- 旧逻辑，使用最后配置的新服作为选择的服务器。使用最新的新服。否则则会变成使用第一个服务器
    -- local worldId = 0
    -- for i = 1, recommendServerCount do
    --     --worldId为0时设置为第一个区，否则更新为标志为新区的区服
    --     if worldId == 0 or recommendServerData[i].isnew then
    --         worldId = recommendServerData[i].id
    --     end
    -- end
    if not isIsInEditor then
        newestWorldId, newestServerInfo = server_data.GetDomainRecommendServer(worldId, newestServerInfo)
    else
        newestWorldId = worldId
    end

    return newestWorldId, newestServerInfo, recommendServerCount
end

function ClearNewestWorldInfo()
    newestWorldId = nil
    newestServerInfo = nil
end

-- 按权重随机服务器
function GetRandomServerInfo(serverList, totalWeight)
    local serverCount = #serverList
    if totalWeight == 0 then
        -- 所有新服务器均未配置权重，使用最后一个新服务器，目前最新的服务器配置在最前
        return serverList[serverCount]
    end

    local randomWeight = util.Random(0, totalWeight)
    local serverInfo = nil
    local accumulateWeight = 0
    local i
    for i = 1, serverCount do
        serverInfo = serverList[i]
        accumulateWeight = accumulateWeight + serverInfo.weight
        if accumulateWeight >= randomWeight then
            return serverInfo
        end
    end

    return serverList[serverCount]

    -- local serverCount = #serverList
    -- local serverIdx = util.Random(1, serverCount)
    -- return serverList[serverIdx]
end

-- 服务器角色信息
function LoadAllRoleServers(token, pid, userID, userName, gameID, sign, gameversion)
    local url_mgr = require "url_mgr"
    local NAurl = url_mgr.SelectUrlByArea(url_mgr.ROLE_SERVERS_URL, 2) .. url_mgr.ROLE_SERVERS_URL_PATH
    local SEAaurl = url_mgr.SelectUrlByArea(url_mgr.ROLE_SERVERS_URL, 3) .. url_mgr.ROLE_SERVERS_URL_PATH
    local NAdataUrl = string.format(NAurl, token, pid, userID, userName, gameID, sign, gameversion)
    local SEAdataUrl = string.format(SEAaurl, token, pid, userID, userName, gameID, sign, gameversion)
    print("北美服务器角色请求url:", NAdataUrl)
    http_inst.Req_Timeout(NAdataUrl, 60, function(jsonData)
        if nil == jsonData then
            return
        end
        print("北美服务器角色信息:", jsonData)
        local dkjson = require "dkjson"
        local serverArr = dkjson.decode(jsonData)
        if nil == serverArr then
            return
        end
        local errorCode = serverArr["code"]
        if errorCode ~= 1 then
            log.Error("Load RoleServers error! code:", errorCode, " message:", serverArr["message"])
            return
        end

        local roleServers = {}
        local serverItemSource
        local serverItem
        local serverDatas = serverArr["data"]
        for i = 1, #serverDatas do
            serverItemSource = serverDatas[i]
            if serverItemSource then
                serverItem = {}
                serverItem.roleid = serverItemSource.actorID
                serverItem.name = serverItemSource.actorName
                serverItem.level = serverItemSource.maxLevel
                serverItem.worldid = serverItemSource.worldId
                serverItem.faceid = serverItemSource.faceID
                serverItem.regionid = 2
                table.insert(roleServers, serverItem)
            end
        end
        --设置北美已有角色服务器列表
        SetNAAccountListData(roleServers)
    end)

    print("东南亚服务器角色请求url:", SEAdataUrl)
    http_inst.Req_Timeout(SEAdataUrl, 60, function(jsonData)
        if nil == jsonData then
            return
        end
        print("东南亚服务器角色信息:", jsonData)
        local dkjson = require "dkjson"
        local serverArr = dkjson.decode(jsonData)
        if nil == serverArr then
            return
        end
        local errorCode = serverArr["code"]
        if errorCode ~= 1 then
            log.Error("Load RoleServers error! code:", errorCode, " message:", serverArr["message"])
            return
        end

        local roleServers = {}
        local serverItemSource
        local serverItem
        local serverDatas = serverArr["data"]
        for i = 1, #serverDatas do
            serverItemSource = serverDatas[i]
            if serverItemSource then
                serverItem = {}
                serverItem.roleid = serverItemSource.actorID
                serverItem.name = serverItemSource.actorName
                serverItem.level = serverItemSource.maxLevel
                serverItem.worldid = serverItemSource.worldId
                serverItem.faceid = serverItemSource.faceID
                serverItem.regionid = 3
                table.insert(roleServers, serverItem)
            end
        end
        --设置东南亚已有角色服务器列表
        SetSEAAccountListData(roleServers)
    end)
end

function LoadRoleServersBy()
    print("开始获取全部大区服务器角色")
    local net_login_module = require "net_login_module"
    local login_main = require "ui_login_main"
    local const = require "const"

    local token = net_login_module.GetLoginSession() or "testSession"
    local userID = login_main.GetAccountName() or 9999
    local pid = tostring(game_config.CHANNEL_ID or 1)
    local uname = net_login_module.GetU8UserInfo().username or 0
    local userName = uname
    local gameID = tostring(const.GAMEID)
    local key = "fD2xF5hS1rX1bX2e" --外网密钥
    if game_config.ENABLE_Q1_DEBUG_MODE then
        key = "********"--内网密钥
    end
    local gameversion = const.GetGameVersion()
    local sign = GetRoleServersSignBy(gameID, gameversion, pid, userID, userName, token, key)
    LoadAllRoleServers(token, pid, userID, userName, gameID, sign, gameversion)
end

--生成请求已有角色的md5
function GetRoleServersSignBy(gameID, gameversion, pid, userID, userName, token, key)
    local sign = Utility.Md5(string.format("%s%s%s%s%s%s%s", gameID, gameversion, pid, userID, userName, token, key))
    print("<color=#fff888>sign:</color>", sign)
    return sign
end

-- 获取推荐区服ID的主函数
function RequestRecommendServerId(uid, callback)
    local sdkToken = "testSession"
    sdkToken = PlayerPrefs.GetString("current_token", "testSession")
    local const = require "const"
    local appid = const.GAMEID
    local pid = game_config.CHANNEL_ID or 40043
    local channel = game_config.ENABLE_Q1SDK_CHANNEL and 1 or 0
    local userID = uid or PlayerPrefs.GetString("userID")
    
    local reqSend
    reqSend = function(userAttrs)
        -- 如果之前拿不到，重试的时候可以重新获取一下session
        if string.empty(sdkToken) or sdkToken == "testSession" then
            sdkToken = PlayerPrefs.GetString("current_token", "testSession")
        end
        -- log.LoginWarning("[zlyq] reqSend sdkToken:", sdkToken)
        RequestRecommendServer_HeaderGET(appid, userID, pid, sdkToken, channel, userAttrs, function(serverId2, error2, code2)
            if code2 == 0 and serverId2 then
                log.LoginWarning("[zlyq] GET方式获取推荐区服成功:", serverId2)
                callback(serverId2, nil)
            else
                log.LoginError("[zlyq] GET方式也失败:", error2, "code:", code2)
                -- 特殊处理code=255的情况
                if code2 == 255 then
                    callback(nil, "user_restricted", code2)
                else
                    if reqTimes < 3 then -- 失败重复请求三次
                        reqTimes = reqTimes + 1
                        log.Warning("[zlyq] 请求次数", reqTimes)
                        event.Trigger(event.GAME_EVENT_REPORT, "SendZLYQReqTimes", {
                            reqTimes = reqTimes,
                            code = code2
                        })-- 智量引擎重试请求次数
                        reqSend(userAttrs)
                    else
                        callback(nil, error2, code2)
                    end
                end
            end
        end)
    end
    -- 收集用户特征数据
    CollectUserAttributes(reqSend)
end

-- GET方式请求 token放头部
function RequestRecommendServer_HeaderGET(appid, uid, pid, token, channel, attrs, callback)
    local url_mgr = require "url_mgr"
    local base_url = url_mgr.SelectUrlByMode(url_mgr.RECOMMEND_SERVER_REQ_URL)
    -- 构建GET参数（不再包含 token）
    local params = {
        "appid=" .. appid,
        "uid=" .. uid,
        "pid=" .. pid,
        "channel=" .. channel
    }
    -- 添加用户特征参数
    if attrs then
        for key, value in pairs(attrs) do
        	table.insert(params, "attr-" .. key .. "=" .. tostring(value))
        end
    end
    
    local url = base_url .. "?" .. table.concat(params, "&")
    log.LoginWarning("[zlyq] GET请求推荐区服(Authorization Header): " .. tostring(url))
    event.Trigger(event.GAME_EVENT_REPORT, "SendZLYQReq", attrs) -- 发送智量引擎请求的打点

    -- 组织请求头
    local headers = {
        ["Authorization"] = "Bearer " .. tostring(token)
    }

    http_inst.Request_Timeout_WithHeaders(url, 2, headers, function(respText, hasError, _, _, request)
        local statusCode = 0
        if request ~= nil then
            statusCode = request.responseCode or 0
        end
        HandleRecommendServerResponse(respText or "", hasError, statusCode, "GET", callback)
    end, true)
end

-- 统一处理响应
function HandleRecommendServerResponse(response, hasError, statusCode, method, callback)
    log.LoginWarning(method .. "[zlyq] 推荐区服响应:", "hasError:", hasError, "statusCode:", statusCode, "response:", response)
    
    -- 网络错误
    if hasError then
        local errorMsg = hasError and "network_error" or ("http_error_" .. statusCode)
        log.LoginError("[zlyq] 推荐区服请求失败:", errorMsg, statusCode)
        event.Trigger(event.GAME_EVENT_REPORT, "RecvZLYQFail", {
            statusCode = statusCode,
            errorMsg = errorMsg
        })-- 智量引擎返回失败打点
        if callback then
            callback(nil, errorMsg, statusCode)
        end
        return
    end
    
    -- 解析JSON响应
    local result = json.decode(response)
    if not result then
        log.LoginError("[zlyq] 推荐区服响应JSON解析失败:", response)
        if callback then
            callback(nil, "json_parse_error")
        end
        event.Trigger(event.GAME_EVENT_REPORT, "RecvZLYQFail", {
            errorMsg = "json_parse_error"
        })-- 智量引擎推荐区服响应JSON解析失败打点
        return
    end
    
    event.Trigger(event.GAME_EVENT_REPORT, "RecvZLYQSuccess", {
        code = result.code,
        serverId = result.data
    })-- 智量引擎推荐区服解析后返回业务状态码打点
    -- 检查业务状态码
    if result.code == 0 then
        -- 成功
        local serverId = result.data
        if serverId and serverId > 0 then
            log.LoginWarning("[zlyq] 获取推荐区服成功:", serverId)
            local userID = PlayerPrefs.GetString("userID")
            -- 记录已请求标记，避免重复请求
            MarkRecommendServerRequested(userID)
            callback(serverId, nil, 0)
        else
            log.LoginError("[zlyq] 推荐区服返回数据无效:", result.data)
            if callback then
                callback(nil, "invalid_server_id", result.code)
            end
        end
    else
        -- 业务异常
        local errorMsg = result.message or "unknown_business_error"
        log.LoginError("[zlyq] 推荐区服业务异常:", "code:", result.code, "message:", errorMsg)
    
        if callback then
            callback(nil, errorMsg, result.code)
        end
        -- -- 特殊处理各种错误码
        -- if result.code == 255 then
        --     -- 用户被限制，不做保底，拒绝创角
        --     log.LoginError("[zlyq] 用户请求被限制，拒绝创角")
        --     if callback then
        --         callback(nil, "user_restricted", result.code)
        --     end
        -- else
        -- end
    end
end

-- 收集用户特征数据
function CollectUserAttributes(callBack)
    local attrs = {}
    
    -- 操作系统
    if Application.platform == RuntimePlatform.Android then
        attrs.os = "android"
    elseif Application.platform == RuntimePlatform.IPhonePlayer then
        attrs.os = "ios"
    else
        attrs.os = "other"
    end
    
    GetUserCountry(function (isoCode)
        if isoCode then
            attrs.country = isoCode
        end
        if callBack then
            callBack(attrs)
        end
    end)
end

-- 标记已请求推荐区服
function MarkRecommendServerRequested(userID)
    if userID and userID ~= "" then
        local key = "recommend_server_requested_" .. userID
        PlayerPrefs.SetInt(key, 1)
        log.LoginWarning("标记用户已请求推荐区服:", userID)
    end
end

-- 获取用户国家/地区信息
function GetUserCountry(callBack)
    util.GetISOCodeByLua(function (isoCode, isSucceed)
        log.Warning("isoCode", isoCode)
        if callBack then
            callBack(isoCode)
        end
    end)
end

function SetStartReqTime()
    if not startReqTime or startReqTime <= 0 then
        startReqTime = os.server_time()
    end
end

-- 推荐服务器请求发送
function RecommendServerSend(uid)
    -- startReqTime = os.server_time()
    local isSend = url_operation_mgr.GetConfig("IsRecommendSend")
    log.LoginWarning("[zlyq] RequestRecommendServerId isSend: ", isSend)
    event.Trigger(event.GAME_EVENT_REPORT, "GetDomainOpenZLYQ", {
        isSend = isSend
    })-- 智量引擎domain配置获取打点
    if isSend == true then
        RequestRecommendServerId(uid, function (serverID, msg, code)
            if serverID then
                newRecommendServerID = serverID
            elseif code == 255 then
                newRecommendServerID = rejustServerID
                PlayerPrefs.SetInt("notEnterGame", newRecommendServerID)
            end
        end)
    end
end

function OnSceneDestroy()
    accountLists = nil
    curServerInfo = nil
end
event.Register(event.SCENE_DESTROY, OnSceneDestroy)