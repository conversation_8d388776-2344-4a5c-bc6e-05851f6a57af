---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by du<PERSON><PERSON>.
--- DateTime: 2025/4/1 17:00
---
local require = require
local ZoneBattleDuel_pb = require "ZoneBattleDuel_pb"
---@class war_zone_duel_define
local M = {}
--region Evt定义
M.Evt_MSG_ZONEBATTLEDUEL_GETINFO_RSP_Handler = "MSG_ZONEBATTLEDUEL_GETINFO_RSP_Handler"---战区对决总信息回复
M.Evt_MSG_ZONEBATTLEDUEL_GET_ROUNDWIN_REWARD_RSP_Handler = "MSG_ZONEBATTLEDUEL_GET_ROUNDWIN_REWARD_RSP_Handler"---领取战区对决胜利奖励（连胜因素） 
M.Evt_MSG_ZONEBATTLEDUEL_NOMATCH_REWARD_RSP_Handler = "Evt_MSG_ZONEBATTLEDUEL_NOMATCH_REWA<PERSON>_RSP_Handler"---轮空奖励领取回复（连胜因素）
M.Evt_MSG_ZONEBATTLEDUEL_GET_VSINFO_RSP_Handler = "Evt_MSG_ZONEBATTLEDUEL_GET_VSINFO_RSP_Handler"---战区1vs1信息回复 (积分详情item变动)
M.Evt_MSG_ZONEBATTLEDUEL_BIGGUN_ATTACK_NTF_Handler = "Evt_MSG_ZONEBATTLEDUEL_BIGGUN_ATTACK_NTF_Handler"---巨炮攻击广播（播放动画表现，特效等入口）
M.Evt_MSG_ZONEBATTLEDUEL_Get_DEFTEAMLIST_RSP_Handler = "Evt_MSG_ZONEBATTLEDUEL_Get_DEFTEAMLIST_RSP_Handler"---请求查看临时占领列表回复
M.Evt_ALLIANCE_BATTLE_DUEL_CHANGE_NTF_Handler = "ALLIANCE_BATTLE_DUEL_CHANGE_NTF_Handler"---活动状态改变时的推送
M.Evt_MSG_ZONEBATTLEDUEL_GET_CONGRESS_POS_RSP_Handler = "Evt_MSG_ZONEBATTLEDUEL_GET_CONGRESS_POS_RSP_Handler"---跨服信息回复
M.Evt_MSG_ZONEBATTLEDUEL_PRESIDENT_GET_INFO_RSP_Handler = "Evt_MSG_ZONEBATTLEDUEL_PRESIDENT_GET_INFO_RSP_Handler"---回复获取worlds对应的总统信息
M.Evt_MSG_ZONEBATTLEDUEL_GET_COUNTRY_SCORE_RSP_Handler = "Evt_MSG_ZONEBATTLEDUEL_GET_COUNTRY_SCORE_RSP_Handler"---王城占领积分回复
--endregion

--region 枚举定义

--区服比拼积分类型，大类型
M.enZoneBattleDuelScoreType = {
    AllianceDuel = ZoneBattleDuel_pb.enZoneBattleDuelScoreType_AllianceDuel, --同盟对决胜利
    AllianceDuel_Mvp = ZoneBattleDuel_pb.enZoneBattleDuelScoreType_AllianceDuel_Mvp, --同盟对决MVP第一名
    WorldBoss = ZoneBattleDuel_pb.enZoneBattleDuelScoreType_WorldBoss, --世界BOSS伤害第一
    ArmyRace = ZoneBattleDuel_pb.enZoneBattleDuelScoreType_ArmyRace, --军备竞赛第一名
    LootTruck = ZoneBattleDuel_pb.enZoneBattleDuelScoreType_LootTruck, --掠夺货车
    Max = ZoneBattleDuel_pb.enZoneBattleDuelScoreType_Max, --最大值
}

--各时间节点定义
M.enZoneBattleDuelTimeType = {
    ScoreTimeBegin = ZoneBattleDuel_pb.enZoneBattleDuelTimeType_ScoreTimeBegin, --比拼积分开始时间 配置周一~周五 1#0#0#0#6#0#4#0
    ScoreTimeEnd = ZoneBattleDuel_pb.enZoneBattleDuelTimeType_ScoreTimeEnd, --比拼积分结束时间 配置周一~周五（决定进攻方）
    CrossMoveBegin = ZoneBattleDuel_pb.enZoneBattleDuelTimeType_CrossMoveBegin, --跨服迁城开始时间 配置周六 10:00:00 6#10#0#0#7#12#0#0
    CrossMoveEnd = ZoneBattleDuel_pb.enZoneBattleDuelTimeType_CrossMoveEnd, --跨服迁城结束时间 配置周日 12:10:00
    CongressAtkBegin = ZoneBattleDuel_pb.enZoneBattleDuelTimeType_CongressAtkBegin, --国会争夺开始时间 配置周六 12:00:00 6#12#0#0#6#20#0#0
    CongressAtkEnd = ZoneBattleDuel_pb.enZoneBattleDuelTimeType_CongressAtkEnd, --国会争夺结束时间 配置周六 20:00:00
    RoundTimeEnd = ZoneBattleDuel_pb.enZoneBattleDuelTimeType_RoundTimeEnd, --本轮结束时间点（7#23#59#59 排行榜奖励、补发积分奖励、积分进度奖励；没有下一轮则发分组内各名次奖励）
    Max = ZoneBattleDuel_pb.enZoneBattleDuelTimeType_Max, --最大值
}

--活动阶段
M.enZoneBattleDuelStage = {
    None = ZoneBattleDuel_pb.ZoneBattleDuel_Type_UnKnow, --未定义状态(活动未开启)
    AtyStart = ZoneBattleDuel_pb.ZoneBattleDuel_Type_AtyStart, --积分比拼开始推送
    BattleEnd = ZoneBattleDuel_pb.ZoneBattleDuel_Type_BattleEnd, --积分比拼结束推送
    CrossStart = ZoneBattleDuel_pb.ZoneBattleDuel_Type_CrossStart, --跨服开始推送
     CongressBattleStart = ZoneBattleDuel_pb.ZoneBattleDuel_Type_CongressBattleStart, --国会争夺开始推送
    CongressBattleSettlement=ZoneBattleDuel_pb.ZoneBattleDuel_Type_CongressBattleSettlement, --国会争夺结算推送（用于提前结算）
    CongressBattleEnd = ZoneBattleDuel_pb.ZoneBattleDuel_Type_CongressBattleEnd, --国会争夺结束推送
    CrossEnd = ZoneBattleDuel_pb.ZoneBattleDuel_Type_CrossEnd, --跨服结束推送
    CrossTotalEnd = ZoneBattleDuel_pb.ZoneBattleDuel_Type_CrossTotalEnd, --跨服结束推送(赛季结束)
    AtyEnd = ZoneBattleDuel_pb.ZoneBattleDuel_Type_AtyEnd, --活动结束推送

    PreNotice = 99, --预告阶段
}
--endregion

--region langID定义
M.lang_test=1000001 --测试用

--endregion
return M