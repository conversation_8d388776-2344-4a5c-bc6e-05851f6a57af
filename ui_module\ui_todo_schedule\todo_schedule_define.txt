--待办日程事件

local require   = require
local ipairs    = ipairs
local pairs     = pairs
local table     = table
local string    = string
local tonumber  = tonumber
local os = os
local game_scheme = require "game_scheme"
local lang = require "lang"
local player_prefs = require "player_prefs"
local ui_util = require "ui_util"
local log = require "log"
local allianceboss_pb = require "allianceboss_pb"
local festival_activity_cfg = require "festival_activity_cfg"
local activity_pb = require "activity_pb"

module("todo_schedule_define")

--跨天请求数据协议
CrossDayRequestData = {
    [festival_activity_cfg.ActivityCodeType.AllianceBoss] = function()
        local net_allianceboss_module = require "net_allianceboss_module"
        net_allianceboss_module.MSG_ALLIANCEBOSS_GET_INFO_REQ()
    end,
    [festival_activity_cfg.ActivityCodeType.WorldBoss] = function()
        local net_activity_module = require "net_activity_module"
        net_activity_module.MSG_WORDBOSS_INFO_REQ()
    end,
    [festival_activity_cfg.ActivityCodeType.CitySiege] = function()
        local net_sandbox_module = require "net_sandbox_module"
        net_sandbox_module.MSG_SANDBOX_NC_LIST_REQ()
    end,
    [festival_activity_cfg.ActivityCodeType.GeneralTrail] = function()
        local ui_general_trials_mgr = require "ui_general_trials_mgr"
        ui_general_trials_mgr.MSG_GENERALTRIAL_PERSONALDATA_REQ()
    end,
    [festival_activity_cfg.ActivityCodeType.ZombieStorm] = function()
        local gw_zombie_storm_mgr = require "gw_zombie_storm_mgr"
        gw_zombie_storm_mgr.OnGetActivityDataReq()
    end,
}


--region 常量
--活动状态
ActivityStateType = {
    notStart = 1,
    doing = 2,
    finish = 3,
}

--活动明日状态
NextActivityType = {
    none = -1,
    willOPen = 1,        --活动即将开启
    heading = 2,       --活动持续
    over = 3,           --活动已结束
}

--活动id枚举
ActivityIdType = {
    StrongRoad = 128,       --强者之路
    StrongRoad2 = 129,       --强者之路正式
}


DayTime = 86400       --一天的时间

--活动状态对于文本颜色
StateColor = {
    [ActivityStateType.notStart] = "#26668A",
    [ActivityStateType.doing] = "#8B5126",
    [ActivityStateType.finish] = "#317734",
}

--当日活动状态对应图标文本
CurStateIcon = {
    [ActivityStateType.notStart] = {
        langID = 1004201,       --未开启
        color = "#0E458F",
    },
    [ActivityStateType.doing] = {
        langID = 1004202,       --进行中
        color = "#9C0700",
    },
}

--次日活动对应状态
NextStateIcon = {
    [NextActivityType.heading] = {
        langID = 1004229,       --持续进行
        color = "#9C0700",
    },
    [NextActivityType.willOPen] = {
        langID = 1004230,       --即将开启
        color = "#0E458F",
    },
}

--任务进度
TaskStateType = {
    doing = 1,      --进行中
    canGet = 2,     --可领取
    hasGet = 3,     --已领取
}

--任务进度显示
TaskStateShow = {
    [TaskStateType.doing] = {
        textColor = "#2A6384",
        textOutlineColor = "#28652E",
        btnLang = 1007425,      --前往
    },
    [TaskStateType.canGet] = {
        textColor = "#795B27",
        textOutlineColor = "#8C3415",
        btnLang = 1007424	,      --领取
    },
    [TaskStateType.hasGet] = {
        textColor = "#26668A",
        textOutlineColor = "#1D5194",
    },
}

--服务器任务状态
ServerTaskStateType = {
    doing = 0,      --进行中
    canGet = 1,     --可领取
    cannotGet = 2,     --不可领取
}

--界面显示类型
ShowListType = {
    none = 0,
    R4R5Task = 1,
    todayActivity = 2,
    nextDayActivity = 3,
}

--公告lang读取类型
NoticeLangType = {
    none = 0,
    ByWeekDay = 1,             --按照周几读取
}

--跳转公告固定配置id
TaskJumpNoticeID = 297


--客户端本地缓存字段
--待办日程活动今日是否已查看
SCHEDULE_ACTIVITY_TODAY_IS_LOOK = "SCHEDULE_ACTIVITY_TODAY_IS_LOOK"
--待办日程打开时间
SCHEDULE_OPEN_TIME = "SCHEDULE_OPEN_TIME"
--联盟公告语言设置
ALLIANCE_NOTICE_LANG = "ALLIANCE_NOTICE_LANG"
--联盟公告内容记录
ALLIANCE_NOTICE_CONTENT = "ALLIANCE_NOTICE_CONTENT"



--endregion

function JumpActivity(data, param)
    local festival_activity_mgr = require "festival_activity_mgr"
    if data.headingCode == festival_activity_cfg.ActivityCodeType.Universal then
        festival_activity_mgr.TryOpenActivityUIByActivityID(data.cfg.atyId, param)
    else
        festival_activity_mgr.OpenActivityUIByActivityCodeType(data.headingCode, param)
    end
    
    local ui_window_mgr = require "ui_window_mgr"
    ui_window_mgr:UnloadModule("ui_todo_schedule")
end

function JumpMonsterPos(data)
    local gw_common_util = require "gw_common_util"
    gw_common_util.JumpToGrid(data.curStateData.monsterPos)
    local ui_window_mgr = require "ui_window_mgr"
    ui_window_mgr:UnloadModule("ui_todo_schedule")
end

--region 获取当前活动状态
---@param headingCode number 活动的headingCode(必须实现）
---@param SetSerData function 设置服务器数据(必须实现）
---@param GetCommonData function 获取每个状态都需要的通用数据(必须实现）
---@param GetNotStartState function 获取未开始状态
---@param GetDoingState function 获取进行中状态
---@param GetFinishState function 获取已完成状态
---@param GetCanShow function 获取是否可以显示

CurActivityState = {
    [festival_activity_cfg.ActivityCodeType.WorldBoss] = {
        --世界boss
        headingCode = festival_activity_cfg.ActivityCodeType.WorldBoss,
        SetSerData = function(serData)
            return serData
        end,
        GetCommonData = function(cfg, serData)
            local gw_world_activity_data = require "gw_world_activity_data"
            local data = {
                state = gw_world_activity_data.GetCurActivityState(),
                bossDeathStamp = gw_world_activity_data.GetBossDeathStamp(),
                bossBornStamp = gw_world_activity_data.GetBossNextRefreshStamp(),
                monsterPos = gw_world_activity_data.GetBossPosition(),
                cfg = cfg,
                serData = serData
            }
            return data
        end,
        GetNotStartState = function(data)
            if not data then
                return
            end
            if data.state ~= ActivityStateType.notStart then
                return
            end
            local cfg = data.cfg
            local stateInfo = {
                stateLang = cfg.notStartDes.data[0],
                stateBtnLang = cfg.notStartBtnDes.data[0],
                stateEndTime = data.bossBornStamp,
                --[[                allAttackCount = data.allAttackCount,
                                curAttackCout = data.curAttackCout,]]
                clickFun = JumpActivity,
            }
            if data.bossBornStamp == -1 then
                local net_login_module = require "net_login_module"
                stateInfo.stateEndTime = net_login_module.GetServerNextZeroTime()
            end
            return stateInfo
        end,
        GetDoingState = function(data)
            if not data then
                return
            end
            if data.state ~= ActivityStateType.doing then
                return
            end
            local cfg = data.cfg
            local stateInfo = {
                stateLang = cfg.doingDes.data[0],
                stateBtnLang = cfg.doingBtnDes.data[0],
                stateEndTime = data.bossDeathStamp,
                monsterPos = data.monsterPos,
                clickFun = JumpMonsterPos,
            }
            return stateInfo
        end,
        GetFinishState = function(data)
            if not data then
                return
            end
            if data.state ~= ActivityStateType.finish then
                return
            end
            local cfg = data.cfg
            local stateInfo = {
                stateLang = cfg.overDes.data[0],
                isFinish = true,
            }
            return stateInfo
        end,
    },
    [festival_activity_cfg.ActivityCodeType.AllianceBoss] = {
        --联盟boss
        headingCode = festival_activity_cfg.ActivityCodeType.AllianceBoss,
        SetSerData = function(serData)
            return serData
        end,
        GetCanShow = function()
            local alliance_mgr = require "alliance_mgr"
            local isJoin = alliance_mgr.GetIsJoinAlliance()
            return isJoin
        end,
        GetCommonData = function(cfg, serData)
            local gw_alliance_boss_activity_data = require "gw_alliance_boss_activity_data"
            local curState, stateTime = gw_alliance_boss_activity_data.GetCurActivityStateAndTime()
            local activityInfo = gw_alliance_boss_activity_data.GetCurActivityNftInfo()
            if not activityInfo then
                --log.Error("AllianceBossInfo from Server is error, check rsp")
                return
            end
            local endTime = gw_alliance_boss_activity_data.GetShowStampByState(1,curState, stateTime)
            local data = {
                curState = curState,
                stateTime = endTime,
                monsterPos = activityInfo.pos,
                cfg = cfg,
                serData = serData,
                personalDamage = activityInfo.personalDamage,
            }
            return data
        end,
        GetNotStartState = function(data)
            if not data then
                return
            end
            local alliance_const = require "alliance_const"
            local alliance_mgr = require "alliance_mgr"
            local hasSetAlliancePower = alliance_mgr.GetUserHasTypePermission(alliance_const.PERMISSIONS.OPEN_ALLIANCE_BOSS)
            if data.curState ~= allianceboss_pb.enAllianceBossState_NotReady then
                return
            end
            if not hasSetAlliancePower then
                return
            end
            local cfg = data.cfg
            local stateInfo = {
                stateLang = cfg.notStartDes.data[0],
                stateBtnLang = cfg.notStartBtnDes.data[0],
                clickFun = JumpActivity,
            }
            return stateInfo
        end,
        GetDoingState = function(data)
            if not data then
                return
            end
            local stateInfo = {}
            local index = -1
            if data.curState == allianceboss_pb.enAllianceBossState_Fighting then
                stateInfo.monsterPos = data.monsterPos
                stateInfo.clickFun = JumpMonsterPos
                index = 2
            elseif data.curState == allianceboss_pb.enAllianceBossState_GetReady then
                index = 0
                stateInfo.clickFun = JumpActivity
            elseif data.curState == 5 then
                index = 1
                stateInfo.clickFun = JumpActivity
            end
            --log.Error("联盟boss状态:", index)
            if index == -1 then
                return
            end
            local cfg = data.cfg
            stateInfo.stateLang = cfg.doingDes.data[index]
            stateInfo.stateBtnLang = cfg.doingBtnDes.data[index]
            stateInfo.stateEndTime = data.stateTime
            stateInfo.curState = data.curState
            return stateInfo
        end,
        GetFinishState = function(data)
            if not data then
                return
            end
            if data.curState ~= allianceboss_pb.enAllianceBossState_End then
                return
            end
            local cfg = data.cfg
            local isFinish = data.personalDamage > 0
            local index = isFinish and 0 or 1
            local stateInfo = {
                stateLang = cfg.overDes.data[index],
                isFinish = isFinish,
            }
            return stateInfo
        end,
    },
    [festival_activity_cfg.ActivityCodeType.WarRally] = {
        --集结大作战
        headingCode = festival_activity_cfg.ActivityCodeType.WarRally,
        SetSerData = function(serData)
            return serData
        end,
        GetCommonData = function(cfg, serData)
            local data = {
                cfg = cfg,
                serData = serData,
            }
            return data
        end,
        GetDoingState = function(data)
            if not data then
                return
            end
            local curArmsRaceThemeTimeID = 0
            local cfg = data.cfg
            local gw_arm_competition_activity_data = require "gw_arm_competition_activity_data"
            local armActivityInfo = gw_arm_competition_activity_data.GetArmCompetitionData()
            local armTitle,armTime, curArmTime
            if armActivityInfo then
                curArmsRaceThemeTimeID = armActivityInfo.ArmsRaceThemeTimeID
                armTitle,armTime = gw_arm_competition_activity_data.GetArmTimeAndTitle(cfg.ctnID1, curArmsRaceThemeTimeID)
                if armTime then
                    local net_login_module = require"net_login_module"
                    curArmTime = net_login_module.GetServerNextZeroTime() - DayTime + armTime.data[0] * 3600
                end
            end

            local stateInfo = {
                stateLang = cfg.doingDes.data[0],
                stateBtnLang = cfg.doingBtnDes.data[0],
                stateEndTime = data.serData.endTime,
                curArmTime = curArmTime,
                armTitle = armTitle,
                clickFun = JumpActivity,
                curArmsRaceThemeTimeID = curArmsRaceThemeTimeID
            }
            return stateInfo
        end,
    },
    [festival_activity_cfg.ActivityCodeType.CitySiege] = {
        --城市竞赛
        headingCode = festival_activity_cfg.ActivityCodeType.CitySiege,
        SetSerData = function(serData)
            return serData
        end,
        GetCommonData = function(cfg, serData)
            local city_siege_activity_data = require "city_siege_activity_data"
            local data = city_siege_activity_data.GetCityActivityStateInfo(os.server_time())
            if not data then return end
            data.cfg = cfg
            data.serData = serData
            return data
        end,
        GetNotStartState = function(data)
            if not data then
                return
            end
            if data.state ~= ActivityStateType.notStart then
                return
            end
            local cfg = data.cfg
            local stateInfo = {
                stateLang = cfg.notStartDes.data[0],
                stateBtnLang = cfg.notStartBtnDes.data[0],
                stateEndTime = data.endTime,
                clickFun = JumpActivity,
            }
            return stateInfo
        end,
        GetDoingState = function(data)
            if not data then
                return
            end
            if data.state ~= ActivityStateType.doing then
                return
            end
            local doingIndex = data.isLoop and 2 or 0
            local mgr_nc_pops_data = require "mgr_nc_pops_data"
            local warHornsData = mgr_nc_pops_data.GetWarHornData()
            if warHornsData then
                doingIndex = os.server_time() >= warHornsData.data.nStartTime and 2 or 1
            end
            local cfg = data.cfg
            local stateInfo = {
                stateLang = cfg.doingDes.data[doingIndex],
                stateBtnLang = cfg.doingBtnDes.data[0],
                stateEndTime = data.endTime,
                monsterPos = data.monsterPos,
                clickFun = data.monsterPos and JumpMonsterPos or JumpActivity,
            }
            return stateInfo
        end,
        GetFinishState = function(data)
            if not data then
                return
            end
            if data.state ~= ActivityStateType.finish then
                return
            end
            local cfg = data.cfg
            local city_siege_activity_data = require "city_siege_activity_data"
            local stateInfo = {
                stateLang = cfg.overDes.data[0],
                isFinish = city_siege_activity_data.GetIsJoinCityActivity(),
            }
            return stateInfo
        end,
    },
    [festival_activity_cfg.ActivityCodeType.GeneralTrail] = {
        --将军试炼
        headingCode = festival_activity_cfg.ActivityCodeType.GeneralTrail,
        SetSerData = function(serData)
            return serData
        end,
        GetCommonData = function(cfg, serData)
            local data = {}
            local ui_general_trials_mgr = require "ui_general_trials_mgr"
            data.isFinish = ui_general_trials_mgr.CheckPersionalChangeLvMax()
            data.cfg = cfg
            data.serData = serData
            return data
        end,
        GetDoingState = function(data)
            if not data then
                return
            end
            if data.isFinish then
                return
            end
            local cfg = data.cfg
            local stateInfo = {
                stateLang = cfg.doingDes.data[0],
                stateBtnLang = cfg.doingBtnDes.data[0],
                stateEndTime = data.serData.endTime,
                clickFun = JumpActivity,
            }
            return stateInfo
        end,
        GetFinishState = function(data)
            if not data then
                return
            end
            local cfg = data.cfg
            local stateInfo = {
                stateLang = cfg.overDes.data[0],
            }
            return stateInfo
        end,
    },
    [festival_activity_cfg.ActivityCodeType.ZombiesAttack] = {
        --丧尸来袭(宝藏亡灵）
        headingCode = festival_activity_cfg.ActivityCodeType.ZombiesAttack,
        SetSerData = function(serData)
            return serData
        end,
        GetCommonData = function(cfg, serData)
            local zombies_attacking_data = require "zombies_attacking_data"
            local state, endTime = zombies_attacking_data.GetCurrentStage()
            local data = {
                state = state,
                endTime = endTime,
                cfg = cfg,
                serData = serData
            }
            return data
        end,
        GetNotStartState = function(data)
            if not data then
                return
            end
            local zombies_attacking_define = require "zombies_attacking_define"
            local ActivityStage = zombies_attacking_define.ActivityStage
            if data.state ~= ActivityStage.PREPARE then
                --不是预热阶段
                return
            end
            local cfg = data.cfg
            local stateInfo = {
                stateLang = cfg.notStartDes.data[0],
                stateBtnLang = cfg.notStartBtnDes.data[0],
                stateEndTime = data.endTime,
                clickFun = JumpActivity,
            }
            return stateInfo
        end,
        GetDoingState = function(data)
            if not data then
                return
            end
            local zombies_attacking_define = require "zombies_attacking_define"
            local ActivityStage = zombies_attacking_define.ActivityStage
            if data.state ~= ActivityStage.NORMAL then
                --不是正式阶段
                return
            end
            local cfg = data.cfg
            local stateInfo = {
                stateLang = cfg.doingDes.data[0],
                stateBtnLang = cfg.doingBtnDes.data[0],
                stateEndTime = data.endTime,
                clickFun = JumpActivity,
            }
            return stateInfo
        end,
        GetFinishState = function(data)
            if not data then
                return
            end
            local zombies_attacking_define = require "zombies_attacking_define"
            local ActivityStage = zombies_attacking_define.ActivityStage
            if data.state ~= ActivityStage.END then
                --不是结束阶段
                return
            end
            local cfg = data.cfg
            local stateInfo = {
                stateLang = cfg.overDes.data[0],
                stateBtnLang = cfg.overBtnDes.data[0],
                stateEndTime = data.endTime,
                clickFun = JumpActivity,
            }
            return stateInfo
        end,
    },
    [festival_activity_cfg.ActivityCodeType.ZombieStorm] = {
        --丧尸灾变
        headingCode = festival_activity_cfg.ActivityCodeType.ZombieStorm,
        SetSerData = function(serData, data)
            return serData
        end,
        GetCanShow = function()
            local alliance_mgr = require "alliance_mgr"
            local isJoin = alliance_mgr.GetIsJoinAlliance()
            return isJoin
        end,
        GetCommonData = function(cfg, serData)
            local gw_zombie_storm_mgr = require "gw_zombie_storm_mgr"
            local activityData = gw_zombie_storm_mgr.GetActivityData()
            if not activityData then
                return
            end
            local data = {
                cfg = cfg,
                serData = serData,
                activityData = activityData
            }
            return data
        end,
        GetNotStartState = function(data)
            if not data then
                return
            end
            local activityData = data.activityData
            local net_login_module = require "net_login_module"
            if activityData.appointmentTime == 0 or activityData.appointmentTime >= net_login_module.GetServerNextZeroTime() then
                --无预约时间，预约的是明日
                return
            end
            local gw_const = require "gw_const"
            if activityData.activityState ~= gw_const.EnZombieApocalypseState.enZombieApocalypseState_Open or activityData.maxUnlockDifficulty == 0 then
                --有波数或者是不是开启的状态
                return
            end
            
            local cfg = data.cfg
            local stateInfo = {
                stateLang = cfg.notStartDes.data[0],
                stateBtnLang = cfg.notStartBtnDes.data[0],
                stateEndTime = activityData.appointmentTime,
                clickFun = JumpActivity,
            }
            return stateInfo
        end,
        GetDoingState = function(data)
            if not data then
                return
            end
            local activityData = data.activityData
            local gw_const = require "gw_const"
            if activityData.activityState ~= gw_const.EnZombieApocalypseState.enZombieApocalypseState_Running then
                --不是开启中阶段
                return
            end
            local cfg = data.cfg
            local stateInfo = {
                stateLang = cfg.doingDes.data[activityData.activityRunningState - 1],
                stateBtnLang = cfg.doingBtnDes.data[0],
                stateEndTime = data.endTime,
                clickFun = JumpActivity,
                stateLangString = activityData.wave,
            }
            return stateInfo
        end,
        GetFinishState = function(data)
            if not data then
                return
            end
            local activityData = data.activityData
            local gw_const = require "gw_const"
            if activityData.activityState ~= gw_const.EnZombieApocalypseState.enZombieApocalypseState_Cool then
                --不是结束阶段
                return
            end
--[[            local net_login_module = require "net_login_module"
            local NextZeroTime = net_login_module.GetServerNextZeroTime()
            local todayZeroTime = NextZeroTime - 86400
            if activityData.appointmentTime > NextZeroTime or activityData.appointmentTime < todayZeroTime then
                --预约不是今日
                return
            end]]
            local cfg = data.cfg
            local stateInfo = {
                stateLang = cfg.overDes.data[0],
                isFinish = true,
            }
            return stateInfo
        end,
    },
    [festival_activity_cfg.ActivityCodeType.Universal] = {
        --通用活动类型
        headingCode = festival_activity_cfg.ActivityCodeType.Universal,
        SetSerData = function(serData, data)
            local specialDefine = CurActivityStateByActId[serData.actId]
            if specialDefine then
                return specialDefine.SetSerData(serData, data)
            end
            return serData
        end,
        GetCommonData = function(cfg, serData)
            local specialDefine = CurActivityStateByActId[serData.actId]
            if specialDefine then
                return specialDefine.GetCommonData(serData, data)
            end
            local data = {
                cfg = cfg,
                serData = serData,
            }
            return data
        end,
        GetNotStartState = function(data)
            if not data then
                return
            end
            local specialDefine = CurActivityStateByActId[data.serData.atyID]
            if specialDefine and specialDefine.GetNotStartState then
                return specialDefine.GetNotStartState(data)
            end
        end,
        GetDoingState = function(data)
            if not data then
                return
            end
            local specialDefine = CurActivityStateByActId[data.serData.atyID]
            if specialDefine and specialDefine.GetDoingState then
                return specialDefine.GetDoingState(data)
            end
        end,
        GetFinishState = function(data)
            if not data then
                return
            end
            local specialDefine = CurActivityStateByActId[data.serData.atyID]
            if specialDefine and specialDefine.GetFinishState then
                return specialDefine.GetFinishState(data)
            end
        end,
    },
    [festival_activity_cfg.ActivityCodeType.DesertStorm] = {
        --沙漠风暴
        headingCode = festival_activity_cfg.ActivityCodeType.DesertStorm,
        SetSerData = function(serData, data)
            return serData
        end,
        GetCommonData = function(cfg, serData)
            local gw_storm_data_activity = require "gw_storm_data_activity"
            local data = {
                cfg = cfg,
                serData = serData,
                activityState = gw_storm_data_activity.GetActivityState(),
                endTime = gw_storm_data_activity.GetActivityTimeData().endTime,
                isSignUp = gw_storm_data_activity.OnCheckSignUpState(),
                battleTimeList = gw_storm_data_activity.OnGetBattleTimeList(),
            }
            return data
        end,
        GetNotStartState = function(data)
            if not data then
                return
            end
            local alliance_pb = require "alliance_pb"
            local alliance_mgr = require "alliance_mgr"
            local player_mgr = require "player_mgr"
            local temp, authority = alliance_mgr.GetRoleAuthority(player_mgr.GetPlayerRoleID())
            if not authority or authority < alliance_pb.emAllianceAuthority_R4 then
                return
            end

            if data.isSignUp then
                --已报名
                return
            end
            local cfg = data.cfg
            local stateInfo = {
                stateLang = cfg.notStartDes.data[0],
                stateBtnLang = cfg.notStartBtnDes.data[0],
                clickFun = JumpActivity,
            }
            return stateInfo
        end,
        GetDoingState = function(data)
            if not data then
                return
            end
            if not data.isSignUp then
                --未报名
                return
            end
            local event_DesertStrom_define = require "event_DesertStrom_define"
            if data.activityState == event_DesertStrom_define.EnDesertStrom_ActivityType.enDesertStrom_ActivityType_None then
                return
            end
            local laneIndex = 0
            local endTime = 0
            if data.activityState == event_DesertStrom_define.EnDesertStrom_ActivityType.enDesertStrom_ActivityType_SignUp then
                --报名阶段
                laneIndex = 0
                endTime = data.endTime
            elseif data.activityState == event_DesertStrom_define.EnDesertStrom_ActivityType.enDesertStrom_ActivityType_Match then
                --匹配阶段
                laneIndex = 1
                endTime = data.endTime
            elseif data.activityState == event_DesertStrom_define.EnDesertStrom_ActivityType.enDesertStrom_ActivityType_Battle then
                --战斗阶段
                local curTime = os.server_time()
                local gw_storm_data_activity = require "gw_storm_data_activity"
                local teamData = gw_storm_data_activity.OnGetSignUpTeamData(event_DesertStrom_define.EnDesertStrom_Team.enDesertStrom_Team_A)
                if not teamData then
                    return
                end
                local battleTimeIndex = teamData.battleTimeIndex
                local battleTimeList = data.battleTimeList[battleTimeIndex]
                if curTime > battleTimeList.endTime then
                    --战斗已结束
                    return
                end
                if curTime < battleTimeList.battleTime then
                    --战斗前
                    laneIndex = 2
                    endTime = battleTimeList.battleTime
                elseif curTime < battleTimeList.endTime then
                    --战斗中
                    laneIndex = 3
                    endTime = battleTimeList.endTime
                end
            end
            local cfg = data.cfg
            local stateInfo = {
                stateLang = cfg.doingDes.data[laneIndex],
                stateBtnLang = cfg.doingBtnDes.data[0],
                stateEndTime = endTime,
                clickFun = JumpActivity,
            }
            return stateInfo
        end,
        GetFinishState = function(data)
            if not data then
                return
            end
            if not data.isSignUp then
                --未报名
                return
            end
            local event_DesertStrom_define = require "event_DesertStrom_define"
            if data.activityState ~= event_DesertStrom_define.EnDesertStrom_ActivityType.enDesertStrom_ActivityType_Battle then
                return
            end
            local gw_storm_data_activity = require "gw_storm_data_activity"
            local teamData = gw_storm_data_activity.OnGetSignUpTeamData(event_DesertStrom_define.EnDesertStrom_Team.enDesertStrom_Team_A)
            if not teamData then
                return
            end
            local battleTimeIndex = teamData.battleTimeIndex
            local battleTimeList = data.battleTimeList[battleTimeIndex]
            local curTime = os.server_time()
            if curTime < battleTimeList.endTime then
                --战斗未结束
                return
            end
            local cfg = data.cfg
            local stateInfo = {
                stateLang = cfg.overDes.data[0],
                isFinish = gw_storm_data_activity.OnCheckMySignInfo(),
            }
            return stateInfo
        end,
    },
}

CurActivityStateByActId = {
    [ActivityIdType.StrongRoad] = {
        --强者之路
        SetSerData = function(serData)
            return serData
        end,
        GetCommonData = function(cfg, serData)
            local data = {
                cfg = cfg,
                serData = serData,
            }
            return data
        end,
        GetDoingState = function(data)
            if not data then
                return
            end
            local cfg = data.cfg
            local stateInfo = {
                stateLang = cfg.doingDes.data[0],
                stateBtnLang = cfg.doingBtnDes.data[0],
                stateEndTime = data.serData.endTime,
                clickFun = JumpActivity,
            }
            return stateInfo
        end,
    },
    [ActivityIdType.StrongRoad2] = {
        --强者之路
        SetSerData = function(serData)
            return serData
        end,
        GetCommonData = function(cfg, serData)
            local data = {
                cfg = cfg,
                serData = serData,
            }
            return data
        end,
        GetDoingState = function(data)
            if not data then
                return
            end
            local cfg = data.cfg
            local stateInfo = {
                stateLang = cfg.doingDes.data[0],
                stateBtnLang = cfg.doingBtnDes.data[0],
                stateEndTime = data.serData.endTime,
                clickFun = JumpActivity,
            }
            return stateInfo
        end,
    },
}

--endregion

--region 获取明日活动状态

---@param GetState function 获取活动状态

NextActivityState = {
    [festival_activity_cfg.ActivityCodeType.WorldBoss] = {
        --世界boss
        GetState = function()
            return NextActivityType.heading
        end,
    },
    [festival_activity_cfg.ActivityCodeType.AllianceBoss] = {
        --联盟boss
        GetState = function(curStateData, todayInfo)
            if not todayInfo.stateType then
                return
            end
            if todayInfo.stateType == ActivityStateType.finish then
                local alliance_const = require "alliance_const"
                local alliance_mgr = require "alliance_mgr"
                local hasSetAlliancePower = alliance_mgr.GetUserHasTypePermission(alliance_const.PERMISSIONS.OPEN_ALLIANCE_BOSS)
                if not hasSetAlliancePower then
                    return
                end
            end
            return NextActivityType.heading
        end,
    },
    [festival_activity_cfg.ActivityCodeType.WarRally] = {
        --集结大作战
        GetState = function()
            return NextActivityType.heading
        end,
    },
    [festival_activity_cfg.ActivityCodeType.CitySiege] = {
        --城市竞赛
        --特殊的获取明日状态
        GetNextDayType = function(cfg, state)
            if state == NextActivityType.heading or state == NextActivityType.willOPen then
                return state
            end
            local nextDayTime = os.server_time() +DayTime
            local city_siege_activity_data = require "city_siege_activity_data"
            local nextSateData = city_siege_activity_data.GetCityActivityStateInfo(nextDayTime)
            if not nextSateData then
                return NextActivityType.over
            end
            nextSateData.cfg = cfg
            local stateInfo = CurActivityState[festival_activity_cfg.ActivityCodeType.CitySiege].GetNotStartState(nextSateData)
            if not stateInfo then
                return NextActivityType.heading
            else
                return NextActivityType.willOPen, stateInfo.stateEndTime
            end
        end,

        GetState = function(curStateData, todayInfo)
            if curStateData and curStateData.stateEndTime and curStateData.stateEndTime > (os.server_time() + DayTime) and todayInfo.stateType == ActivityStateType.notStart then
                return NextActivityType.willOPen, curStateData.stateEndTime
            end
            return NextActivityType.heading
        end,
    },
    [festival_activity_cfg.ActivityCodeType.GeneralTrail] = {
        --将军试炼
        GetState = function()
            return NextActivityType.heading
        end,
    },
    [festival_activity_cfg.ActivityCodeType.ZombiesAttack] = {
        --丧尸来袭
        GetState = function(curStateData, todayInfo)
            if curStateData and curStateData.stateEndTime and curStateData.stateEndTime > (os.server_time() + DayTime)and todayInfo.stateType == ActivityStateType.notStart then
                return NextActivityType.willOPen
            end
            return NextActivityType.heading
        end,
    },
    [festival_activity_cfg.ActivityCodeType.ZombieStorm] = {
        --丧尸灾变
        GetState = function(curStateData, todayInfo, serverData)
            local gw_zombie_storm_mgr = require "gw_zombie_storm_mgr"
            local net_login_module = require "net_login_module"
            local activityData = gw_zombie_storm_mgr.GetActivityData()
            if not activityData then
                return
            end
            local gw_const = require "gw_const"
            if activityData.activityState ~= gw_const.EnZombieApocalypseState.enZombieApocalypseState_Open or activityData.maxUnlockDifficulty == 0 then
                --有波数或者是不是开启的状态
                return
            end
            local nextDayTime = net_login_module.GetServerNextZeroTime()
            if activityData.appointmentTime >= nextDayTime and activityData.appointmentTime < (nextDayTime + DayTime) then
                --无预约时间，预约的是明日
                return NextActivityType.willOPen, activityData.appointmentTime
            end
        end,
    },
    [festival_activity_cfg.ActivityCodeType.Universal] = {
        --通用活动类型
        GetState = function(curStateData, todayInfo, serverData)
            local specialDefine = NextActivityStateByActId[serverData.atyID]
            if specialDefine and specialDefine.GetState then
                return specialDefine.GetState(curStateData, todayInfo, serverData)
            end
            return NextActivityType.heading
        end,
    },
    [festival_activity_cfg.ActivityCodeType.DesertStorm] = {
        --沙漠风暴
        GetState = function()
            local alliance_pb = require "alliance_pb"
            local alliance_mgr = require "alliance_mgr"
            local player_mgr = require "player_mgr"
            local temp, authority = alliance_mgr.GetRoleAuthority(player_mgr.GetPlayerRoleID())
            if not authority or authority < alliance_pb.emAllianceAuthority_R4 then
                return
            end
            return NextActivityType.heading
        end,
    },
}

NextActivityStateByActId = {
    [ActivityIdType.StrongRoad] = {
        --强者之路
        GetState = function(curStateData, todayInfo)
            return NextActivityType.heading
        end,
    },
    [ActivityIdType.StrongRoad2] = {
        --强者之路
        GetState = function(curStateData, todayInfo)
            return NextActivityType.heading
        end,
    },
}

local willOpenLang = 1004232        --	%s1开启活动
local headingLang = 1004231         --	活动持续进行中
--获取下一天的状态
function GetNextDayStateInfo(cfg, startTime, nextDayState, todayInfo, serverData)
    if nextDayState == NextActivityType.none or nextDayState == NextActivityType.over then
        --活动未开启/已结束
        return
    end
    if not cfg then
        return
    end
    if not NextActivityState[cfg.headingCode] then
        return
    end
    if nextDayState == NextActivityType.heading and NextActivityState[cfg.headingCode] then
        nextDayState, stateTime = NextActivityState[cfg.headingCode].GetState(todayInfo.curStateData, todayInfo, serverData)
        startTime = stateTime or startTime
    end
    if not nextDayState then
        return
    end
    local nextDayInfo = {
        headingCode = cfg.headingCode,
        cfg = cfg,
        isNextDay = true,
        curStateData = {
            stateLang = nextDayState == NextActivityType.heading and headingLang or willOpenLang,
            stateEndTime = startTime,
        },
        stateType = nextDayState,
    }
    return nextDayInfo
end

--endregion

--获取公告内容
GetNoticeContent = {
    [NoticeLangType.none] = function(cfg, language, allianceID)
        local key = string.format("%s_%d_%s_%d",ALLIANCE_NOTICE_CONTENT,allianceID, language, cfg.ID)     --公告内容const+联盟id+公告语言const+任务index
        local content = player_prefs.GetCacheData(key, "")
        if not string.IsNullOrEmpty(content) then
            return content
        end
        return ui_util.GetTextByLanguage(cfg.DefaultAnnouncement.data[0], language)
    end,
    [NoticeLangType.ByWeekDay] = function(cfg, language, allianceID)
        local list = cfg.DefaultAnnouncement.data
        local time_util = require "time_util"
        local weekDay = time_util.GetWeekDay()
        local key = string.format("%s_%d_%s_%d_%d",ALLIANCE_NOTICE_CONTENT, allianceID, language, weekDay, cfg.ID)     --公告内容const+联盟id+公告语言const+天数+任务index
        local content = player_prefs.GetCacheData(key, "")
        if not string.IsNullOrEmpty(content) then
            return content
        end
        return ui_util.GetTextByLanguage(list[weekDay - 1], language)
    end
}

--设置公告
SetNoticeContent = {
    [NoticeLangType.none] = function(data, allianceID)
        local key = string.format("%s_%d_%s_%d",ALLIANCE_NOTICE_CONTENT, allianceID, data.language, data.indexId)     --公告内容const+公告语言const+任务index
        player_prefs.SetCacheData(key, data.content)
    end,
    [NoticeLangType.ByWeekDay] = function(data, allianceID)
        if not data.weekDay then
            return
        end
        local key = string.format("%s_%d_%s_%d_%d",ALLIANCE_NOTICE_CONTENT, allianceID, data.language, data.weekDay, data.indexId)     --公告内容const+公告语言const+天数+任务index
        player_prefs.SetCacheData(key, data.content)
    end
}



--刷新r4/r5任务列表
UPDATE_SCHEDULE_R4R5_TASK = "UPDATE_SCHEDULE_R4R5_TASK"



