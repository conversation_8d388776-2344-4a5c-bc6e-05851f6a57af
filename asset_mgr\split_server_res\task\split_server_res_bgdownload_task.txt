local require = require
local setmetatable = setmetatable
local util = require "util"
local SplitServerHashChecker = CS.War.Res.SplitServerHashChecker
local split_server_res_base_task = require "split_server_res_base_task"
local split_define = require "split_server_res_define"
local Warning = split_define.logger.Warning

local ClassTable = {}
setmetatable(ClassTable, {
    __index = split_server_res_base_task
})

local VerTaskType = split_define.VerTaskType
local VerTaskTypeName = split_define.VerTaskTypeName
local DownloadErrorCode = split_define.DownloadErrorCode
local VerTaskState = split_define.VerTaskState
local TaskState = split_define.TaskState
local reportData = split_define.reportData

function ClassTable.New(...)
    local newObj = {}
    setmetatable(newObj, {
        __index = ClassTable
    })
    newObj:ctor(...)
    return newObj
end

function ClassTable:ctor(version, serverId)
    self.verTaskType = VerTaskType.BgDownload
    --self.needWifi = true
    split_server_res_base_task.ctor(self, version, serverId)
end

function ClassTable:GetVersionTask(hashRemote)
    if self.curVerTaskState == VerTaskState.RequestFiles2Finish then
        if self.isPauseTask then
            return
        end
        self.curVerTaskState = VerTaskState.GetVersionTask
        
        Warning(2,"version task step:", self.curVerTaskState, self.version, addLangRes)
        --BgDownload当前使用版本是否存在需要下载资源，addLangRes设为false
        local addLangRes = false
        SplitServerHashChecker:GetVersionTaskAsync(hashRemote, self.version, addLangRes, function(versionTask)
            if self.curVerTaskState == VerTaskState.GetVersionTask then
                if not util.IsObjNull(versionTask) and versionTask.totalSize > 0 then
                    self.versionTaskData = versionTask
                    self:InitDownloadTaskList(versionTask)
                    Warning(2,"version task step:", self.curVerTaskState, self.version)
                else
                    self.curVerTaskState = VerTaskState.Finish
                    Warning(2,"version task step:", self.curVerTaskState, self.version)
                end 
            end
        end) 
    end
end

function ClassTable:InitDownloadTaskList(versionTask)
    --可控制下载列表，暂时没有差异直接用基类的
    split_server_res_base_task.InitDownloadTaskList(self, versionTask)
end

function ClassTable:GetTaskName()
    return VerTaskTypeName.BgDownload
end

return ClassTable