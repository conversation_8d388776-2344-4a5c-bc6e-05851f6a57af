--- Created by par.
--- DateTime: 2025/7/8 15:30
--- Des:游戏重启

local require = require
local table = table
local ipairs = ipairs
local pairs = pairs
local math = math
local string = string
local type = type
local log = require "log"
local Edump = Edump
local AssetBundleManager = CS.War.Base.AssetBundleManager
local gameReStart = CS.GameReStart.Instance
local IOSystem = CS.War.Script.IOSystem
local GameObject = CS.UnityEngine.GameObject
local LuaManager = CS.War.Script.LuaManager

local M = {}

local ignoreMoudles = {
    pb_new = true,
    debug = true,
    string = true,
    bit = true,
    pb = true,
    table = true,
    math = true,
    coroutine = true,
    os = true,
    log = true,
    package = true,
    io = true,
    dkjson = true,
}


---@see 初始化
function M.Init()
end

function M.Dispose()
    gameReStart:gameReStart()
end

local isOpenLog = true

function M.Log(...)
    if isOpenLog then
        log.Warning(...)
    end
end

function M.LogError(...)
    if isOpenLog then
        log.Error(...)
    end
end

function M.AppRestart() 
    M.ReturnLogin()
    M.Init()
    gameReStart:ExecuteCallbacks(function()
        --调用lua缓存的卸载后重新启动场景
        M.ClearGloablData() 
        M.ClearluaModule()
        gameReStart:LoadScene("update")
    end)
end

function M.ClearGloablData() 
    local const = require "const"   
    const.isInitLuaOver = false
    const.LangInitFinish = false
    g_LuaBehaviourModule = nil
    g_LuaScriptInitFinished = nil
end

--返回登陆状态
function M.ReturnLogin()
    M.LogError("ReturnLogin")
    local bLogout = nil
	local main_slg_mgr = require "main_slg_mgr"
    local event = require "event"
    local net = require "net"
    local q1sdk = require "q1sdk"
    local game = require "game"

	main_slg_mgr.NetCloseReturnLogin()
	event.Trigger(event.SET_FLOW_STATE_CACHE,"return_to_login","1")

	local net_reconnect_module = require "net_reconnect_module"
	net_reconnect_module.DisconnectActivity()
	net.Disconnect()

	local ui_window_mgr = require "ui_window_mgr"
	local const = require "const"
	ui_window_mgr:CloseAllUIAndScene(nil, true)

	--if bLogout ~= false then
	--	q1sdk.Logout()
	--end
	q1sdk.LogoutLogic()

    if bLogout ~= false then
        event.Trigger(event.SCENE_DESTROY)
    end
	game.NotifySceneDestroy()
	if bLogout ~= false then
		event.Trigger(event.ACCOUNT_LOGOUT, false)
	end
	return true
end

function M.get_loaded_modules()
    local modules = {}
    local loaded = package.loaded
    -- 使用pairs遍历package.loaded表
    for key, value in pairs(loaded) do
        if type(key) == "string" and value ~= nil then
            table.insert(modules, key)
        end
    end
    return modules
end

function M.ClearluaModule(prefix)
    local LuaManagerInst = LuaManager.Instance
    if LuaManagerInst then
        local clearModules = M.get_loaded_modules()
        if type(clearModules) == "userdata" then
            -- 使用C# List的Count属性和索引器
            for i = 0, clearModules.Count - 1 do
                local moduleName = clearModules[i]
                if type(moduleName) == "string" then
                    -- 如果有前缀参数，只清除匹配前缀的模块
                    if not ignoreMoudles[moduleName] and not prefix or (prefix and string.find(moduleName, "^" .. prefix)) then
                        local moduleTable = package.loaded[moduleName]
                        if type(moduleTable) == "table" then
                            -- 重置优化状态
                                if moduleTable.__optimize_init ~= nil then
                                    moduleTable.__optimize_init = false
                                end
                            end
                        package.loaded[moduleName] = nil
                    end
                end
            end
        else
            -- 如果返回的是Lua表，使用原来的逻辑
            for _, moduleName in pairs(clearModules) do
                if type(moduleName) == "string" then
                    -- 如果有前缀参数，只清除匹配前缀的模块
                    if not ignoreMoudles[moduleName] and not prefix or (prefix and string.find(moduleName, "^" .. prefix)) then
                        local moduleTable = package.loaded[moduleName]
                        if type(moduleTable) == "table" then
                        -- 重置优化状态
                            if moduleTable.__optimize_init ~= nil then
                                moduleTable.__optimize_init = false
                            end
                        end
                        package.loaded[moduleName] = nil
                    end
                end
            end
        end
    end
end

return M