local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type

local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"
local log = require "log"
local os = require "os"

local festival_activity_mgr = require "festival_activity_mgr"
local gw_task_data = require "gw_task_data"
local game_scheme = require "game_scheme"
local gw_task_const = require "gw_task_const"
local gw_task_mgr = require "gw_task_mgr"
local event_taskpart_define = require "event_taskpart_define"
local event_task_define = require "event_task_define"
local time_util = require "time_util"
local net_login_module = require "net_login_module"
local util = require "util"

local receiveBtnTimer
local activeTimer

--region Controller Life
module("ui_halloween_sign_in_controller")
local controller = nil
---@class ui_halloween_sign_in_controller:ControllerBase
local UIController = newClass("ui_halloween_sign_in_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.__base.Init(self, view_name, controller_name)    

    self.activityMainId = data.activityID
    self:GetActivityTime()
    self:RefreshRewardItem()
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close(data)   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents()
    self:RegisterEvent(event_taskpart_define.TMSG_ACT_TASK_GETREWARD_RSP,function() self:RefreshRewardItem() end)
    self:RegisterEvent(event_taskpart_define.TMSG_ACT_TASK_DATA_NTF,function() self:RefreshRewardItem() end )
    self:RegisterEvent(event_task_define.REFRESH_TASK, function() self:RefreshRewardItem() end)
end

function UIController:AutoUnsubscribeEvents() 
end
--endregion

--region Controller Logic


function UIController:OnBtnReceiveClickedProxy()
    self:ReceiveAll()
end

function UIController:GetActivityTaskList()
    local festivalCfgData = festival_activity_mgr.GetActivityCfgByActivityID(self.activityMainId)
    local activityData = festival_activity_mgr.GetActivityDataByActivityID(self.activityMainId)
    local taskListFromZero = festivalCfgData.ctnID1[1]
    local taskList = {}
    for i = 0, #taskListFromZero do
        table.insert(taskList, taskListFromZero[i])
    end
    return taskList
end

-- 一键领取
function UIController:ReceiveAll()
    local receiveTable = self:CheckReceiveList()
    gw_task_mgr.ReceiveTaskListReward(receiveTable,self.activityMainId,gw_task_const.TaskModuleType.HalloweenSignIn)
end

-- 获取活动结束时间
function UIController:GetActivityTime()
    return festival_activity_mgr.GetActivityDataByActivityID(self.activityMainId).endTimeStamp
end

-- 刷新奖励列表
function UIController:RefreshRewardItem()
    local taskIDTable = self:GetActivityTaskList()
    -- 处理奖励列表
    for i, v in ipairs(taskIDTable) do
        self:TriggerUIEvent("RenderItem", v, i)
    end
    -- 处理领取按钮
    local receiveList = self:CheckReceiveList()
    if util.get_len(receiveList) == 0 then
        -- 没有可领取的了
        self:TriggerUIEvent("RenderReceiveBtn", false)
    else
        -- 有未领取的
        self:TriggerUIEvent("RenderReceiveBtn", true)
    end
    -- 处理全部领取
    if self:CheckIsAllGet() then
        self:TriggerUIEvent("HideReceiveBtn")
    end
    -- 处理领取按钮的倒计时
    if receiveBtnTimer then
        self:RemoveTimer(receiveBtnTimer)
        receiveBtnTimer = nil
    end
    local todayEndTime = net_login_module.GetServerNextZeroTime()
    local timer = self:CreateTimer(1, function()
        local currTime = os.server_time()
        local time = time_util.FormatTime5(todayEndTime - currTime)
        self:TriggerUIEvent("RenderTimeTxt", time)
    end)
    receiveBtnTimer = timer
    -- 活动倒计时
    if activeTimer then
        self:RemoveTimer(activeTimer)
        activeTimer = nil
    end
    local activeEndTime = self:GetActivityTime()
    local activeTimerTemp = self:CreateTimer(1, function()
        local curTime = os.server_time()
        local time = time_util.FormatTime5(activeEndTime - curTime)
        self:TriggerUIEvent("RenderActivityCountDownTime", time)
    end)
    activeTimer = activeTimerTemp
end

-- 检测可领取任务列表
function UIController:CheckReceiveList()
    local taskIDTable = self:GetActivityTaskList()
    local taskIDListReceive = {}
    for i, v in ipairs(taskIDTable) do
        local taskDataServer = gw_task_data.GetTaskData(v)
        local taskData = game_scheme:TaskMain_0(v)
        if not taskDataServer then
            log.Warning("[ui_halloween_sign_in_controller:CheckReceiveList] taskDataServer is nil, taskID = %d", v)
        end
        if taskDataServer and taskDataServer.rate >= taskData.ConditionValue1 then
            if not taskDataServer.status then
                taskIDListReceive[i] = v
            end
        end
    end
    return taskIDListReceive
end

-- 检测是否全部领取
function UIController:CheckIsAllGet()
    local taskIDTable = self:GetActivityTaskList()
    local lastID = taskIDTable[#taskIDTable]
    local taskDataServer = gw_task_data.GetTaskData(lastID)
    local taskData = game_scheme:TaskMain_0(lastID)
    if not taskDataServer then
        log.Warning("[ui_halloween_sign_in_controller:CheckIsAllGet] taskDataServer is nil, taskID = %d", lastID)
    end
    if taskDataServer and taskDataServer.rate >= taskData.ConditionValue1 then
        if taskDataServer.status then
            return true
        end
    end

    return false
end

--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
