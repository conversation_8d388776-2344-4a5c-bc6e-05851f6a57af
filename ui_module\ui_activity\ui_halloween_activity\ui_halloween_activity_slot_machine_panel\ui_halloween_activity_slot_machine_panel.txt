local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local UIUtil = CS.Common_Util.UIUtil

local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_halloween_activity_slot_machine_panel_binding"

local reward_mgr = require "reward_mgr"

local gw_asset_mgr = require "gw_asset_mgr"

local slot_machine_ani = require "slot_machine_ani_helper"

local halloween_activity_slot_machine_const = require "halloween_activity_slot_machine_const"

--region View Life
module("ui_halloween_activity_slot_machine_panel")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.VData = {
        ---@type SlotMachine
        slot_machine_ani = slot_machine_ani:new()
    }
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.VData = nil    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic

-- 刷新活动倒计时
function UIView:RenderActivityCountDownTime(timeStr)
    --self["txt_ActivityCountDownTime"].text = timeStr
    self["txt_ActivityCountDownTime"].text = lang.Get(656002) .. timeStr
end

function UIView:RenderCoinItem(id, count)
    self["txt_item_count"].text = string.format("%d", count)
    gw_asset_mgr:LoadGoodsIcon(id, function(sprite)
        if not self:IsValid() then
            return
        end
        self["img_item_icon"].sprite = sprite
    end)
    
end

function UIView:RenderRewardItem(nextReward,CurDrawCount, targetCount)
    local goodsItem = reward_mgr.GetRewardItemData(nextReward,self["rtf_goal_item"],true,0.75)
    self["txt_progress"].text = string.format("%d/%d", CurDrawCount, targetCount)
    --self["txt_item_goal_count"].text = string.format("%d", nextReward.count)
    -- gw_asset_mgr:LoadGoodsIcon(nextReward.id, function(sprite)
    --     if not self:IsValid() then
    --         return
    --     end
    --     self["img_item_goal_icon"].sprite = sprite
    -- end)
end

function UIView:RenderDrawTimesBtnInfo(count)
    if count == 1 then
        self["txt_btn_times_info"].text = lang.Get(halloween_activity_slot_machine_const.lang_key.times_info_1)
    end
    if count == 5 then
        self["txt_btn_times_info"].text = lang.Get(halloween_activity_slot_machine_const.lang_key.times_info_5)
    end
end

function UIView:RenderRemainTimesInfo(count)
    self["txt_remain_times"].text = lang.Get(halloween_activity_slot_machine_const.lang_key.today_remain_times) .. ": " .. count
end

function UIView:RenderSlotMachine(idIconList, patternIdList, startPattern)
    startPattern = startPattern or {}
    self.VData.slot_machine_ani:Init(idIconList, patternIdList, {
        self["item_slot_col1"],self["item_slot_col2"],self["item_slot_col3"],
    }, self["btn_draw"])
    self.VData.slot_machine_ani:SetStartState(startPattern.pattern_group_randomed)
end

function UIView:RenderSkipState(boolSkip)
    self.tog_speed:SetIsOnWithoutNotify(boolSkip)
end
function UIView:RunSlotMachine(iconIdList, endcb)
    self.VData.slot_machine_ani:OnSpin(iconIdList, endcb)
end

--endregion




--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, nil, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil,nil, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
