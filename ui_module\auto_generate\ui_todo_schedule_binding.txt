local require = require
local typeof = typeof

local Toggle = CS.UnityEngine.UI.Toggle
local Text = CS.UnityEngine.UI.Text
local Button = CS.UnityEngine.UI.Button
local RectTransform = CS.UnityEngine.RectTransform
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable


module("ui_todo_schedule_binding")

UIPath = "ui/prefabs/gw/gw_todoschedule/uitodoschedule.prefab"

WidgetTable ={
	tog_TaskList = { path = "Main/DayTypeToggleList/tog_TaskList", type = Toggle, value_changed_event = "OnTogTaskListValueChange"},
	tog_TodayList = { path = "Main/DayTypeToggleList/tog_TodayList", type = Toggle, value_changed_event = "OnTogTodayListValueChange"},
	tog_NextDayList = { path = "Main/DayTypeToggleList/tog_NextDayList", type = Toggle, value_changed_event = "OnTogNextDayListValueChange"},
	txt_curTimeText = { path = "Main/CurTimeContent/txt_curTimeText", type = Text, },
	btn_changeTimeType = { path = "Main/CurTimeContent/btn_changeTimeType", type = Button, event_name = "OnBtnChangeTimeTypeClickedProxy"},
	txt_ChangeTimeText = { path = "Main/CurTimeContent/txt_ChangeTimeText", type = Text, },
	rtf_nothingShow = { path = "Main/rtf_nothingShow", type = RectTransform, },
	rtf_todoActivityShow = { path = "Main/rtf_todoActivityShow", type = RectTransform, },
	srt_ScheduleList = { path = "Main/rtf_todoActivityShow/Scroll View/Viewport/srt_ScheduleList", type = ScrollRectTable, },
	rtf_todoTaskShow = { path = "Main/rtf_todoTaskShow", type = RectTransform, },
	btn_tipBtn = { path = "Main/rtf_todoTaskShow/btn_tipBtn", type = Button, event_name = "OnBtnTipBtnClickedProxy"},
	srt_taskList = { path = "Main/rtf_todoTaskShow/Scroll View/Viewport/srt_taskList", type = ScrollRectTable, },
	btn_closeBtn = { path = "btn_closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},

}
