--- Des:建筑-城际货运中心
local require = require
local pairs     = pairs
local ipairs    = ipairs
local string    = string
local math = math
local tostring = tostring
local util = require "util"
local UIUtil = UIUtil
local os = os
local gw_ed = require"gw_ed"

local buildingBaseClass = require "gw_home_comp_building_base"
local union_slg_scene_object = require "union_slg_scene_object"
local intercity_trucks_data = require "intercity_trucks_data"
local intercity_trucks_enum = require "intercity_trucks_enum"
local gw_sand_animator_helper = require "gw_sand_animator_helper"
local ui_window_mgr = require "ui_window_mgr"

local flow_text = require "flow_text"
local lang = require "lang"
local event = require "event"
local event_carriage_define = require "event_carriage_define"
local CarriageStatus = intercity_trucks_enum.CarriageStatus
local newclass = newclass
local GWG = GWG
local GWConst = GWConst
local math = math
local tostring = tostring
local log = require "log"
local activity_open = require "activity_open"
local function_open_mgr = require "function_open_mgr"
local string = string
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

module("gw_home_comp_building_carriage_center")
---@class gw_home_comp_building_carriage_center: gw_home_comp_building_base
--当前统一都用GWHomeCompBuilding
local GWHomeCompBuilding = newclass("gw_home_comp_building_carriage_center", buildingBaseClass)
--常见的重载函数，按自己需要重载

local AB = {
    --白
    "art/greatworld/home/<USER>/carriage/mache_baivariant.prefab",
    --绿
    "art/greatworld/home/<USER>/carriage/mache_lvvariant.prefab",
    --蓝
    "art/greatworld/home/<USER>/carriage/mache_lanvariant.prefab",
    --紫
    "art/greatworld/home/<USER>/carriage/mache_zivariant.prefab",
    --橙
    "art/greatworld/home/<USER>/carriage/mache_chengvariant.prefab",
    --驯鹿车
    "art/greatworld/home/<USER>/carriage/mache_youlingvariant.prefab",
}

--- 构造器
---@see  override
function GWHomeCompBuilding:ctor()
    buildingBaseClass.ctor(self)
end
--- 初始化数据 
---@see  override
function GWHomeCompBuilding:Init()
    buildingBaseClass.Init(self)
    --不需要气泡排序
    self.noSortBubbleOrder = true
    self.animatorHelper = self.animatorHelper or  gw_sand_animator_helper.new()
    --self.animatorHelper.enabled = false
end

---@public 设置建筑贴图表现
---@see  override
function GWHomeCompBuilding:SetBuildingTextureInfo()
    buildingBaseClass.SetBuildingTextureInfo(self)
    local tempOffsetX = self.sizeX / 2 - 0.5
    local tempOffsetZ = -(self.sizeY / 2 - 0.5)
    self.truckEntity = self.truckEntity or {}
    self.qualityState = self.qualityState or {}
    self.bubbleParent = self.bubbleParent or {}
    for i = 1,5 do
        local boxTf = self.box_root:GetChild(i-1)
        local box = UIUtil.GetComponent(boxTf, "BoxCollider")
        if not util.IsObjNull(box) then
            box.name = "build_" .. self.compId.. "_" .. i-1
            if i ~= 1 then
                UIUtil.SetBoxColliderSize(box, self.sizeX-1, self.sizeY*1.2/4, 0.1)
                UIUtil.SetLocalPos(box, self.sizeX -2, 1, -(self.sizeY*(i-1)*1.4)/4+0.8)
            else
                UIUtil.SetBoxColliderSize(box, self.sizeX, self.sizeY*2, 0.1)
            end
        end
        self.bubbleParent[i-1] = box
    end
    self.carriage_root = UIUtil.GetTrans(self.transform, "node/carriage_root")
    self:CheckIsServerOpen()
    self:RefreshCarriageModel()
end

function GWHomeCompBuilding:RefreshCarriageModel()
    local data = intercity_trucks_data.GetTrucksCenterData()
    local total,current = intercity_trucks_data.GetDepartureCount()
    --self.animatorHelper.animatorState = self.animatorHelper.Stand
    if data and data.trucks then
        for k,v in ipairs(data.trucks) do
           self:RefreshOneCarriage(v,current >= total)
        end
    end
    --self.animatorHelper:EnabledAnimator(false)
end

function GWHomeCompBuilding:CheckIsServerOpen()
    if not self.bubbleParent then
        return
    end
    local open,des,param = activity_open.CheckFunctionIsOpen(activity_open.OpenIdEnum.TradeCaravan)
    open = function_open_mgr.CheckFunctionIsOpen(function_open_mgr.OpenIdEnum.TradeCaravan)
    if(self.serData)then
        local posId = GWG.GWHomeMgr.gridData.GetGridPosIdByGridXY(self.serData.x, self.serData.y)
        open = GWG.GWHomeMgr.gridData.IsFogGrid(posId)
    end
    if not open and param and param.OpenType == activity_open.OpenType.SerVerCrossDay then
        --未达开服天数 城建倒计时气泡
        local game_scheme = require "game_scheme"
        local cfg = game_scheme:ActivityMain_0(activity_open.OpenIdEnum.TradeCaravan)
        if cfg == nil then
            log.Error("没有配置功能模块id为" , activity_open.OpenIdEnum.TradeCaravan , "的功能模块")
            return
        end
        if not cfg.SeverTime or cfg.SeverTime == 0 then
            log.Error(activity_open.OpenIdEnum.TradeCaravan,"功能模块id为到达开服时间：",cfg.SeverTime)
            return
        end

        local player_mgr = require "player_mgr"
        local openSerTime = player_mgr.GetRoleOpenSvrTime()
        --已过时间
        local curTime = os.server_time()
        local data = {}
        data.remainTime = (cfg.SeverTime * 24 * 60 * 60) - (curTime - openSerTime)
        data.callback = function ()
        --去除城建倒计时气泡
        self:DisposeBubbleEntity(GWConst.EHomeBubbleEntityType.TruckTimer,self.bubbleParent[2])
        end
        self:CreateBubbleEntity(GWConst.EHomeBubbleEntityType.TruckTimer,function()
        
    end,nil,data,self.bubbleParent[2])
    else
        --去除城建倒计时气泡
        self:DisposeBubbleEntity(GWConst.EHomeBubbleEntityType.TruckTimer,self.bubbleParent[2])
    end
end

function GWHomeCompBuilding:CheckHasDepartTime()
    local total,current = intercity_trucks_data.GetDepartureCount()
    if current >= total then
        local data = intercity_trucks_data.GetTrucksCenterData()
        --self.animatorHelper.animatorState = self.animatorHelper.Dead
        if data and data.trucks then
            for k,v in ipairs(data.trucks) do
                if v.status ~=  CarriageStatus.CARRIAGE_STATUS_REWARD and v.truckid and v.truckid > 0 then
                    self:DisposeBubbleEntity(GWConst.EHomeBubbleEntityType.CarriageDepart,self.bubbleParent[v.truckid])
                end
            end
        end
    end
end

function GWHomeCompBuilding:RefreshOneCarriage(v,noDepartTime)
    if not v  or not v.status or not v.truckid then
        return
    end
    --服务器有时候连truckid都不下发
    if not (v.truckid >0) then
        return
    end
    --log.Error("RefreshOneCarriage",v.truckid,v.status,noDepartTime)
    if v and v.status ~= CarriageStatus.CARRIAGE_STATUS_LOCKED and v.status ~= CarriageStatus.CARRIAGE_STATUS_FINISH then
        if not self.truckEntity[v.truckid] and v.status ~= CarriageStatus.CARRIAGE_STATUS_DEPART then
            self.qualityState[v.truckid] = v.quality
            self.truckEntity[v.truckid] = union_slg_scene_object.CM("gw_home_comp_building_carriage_center"):Init(AB[v.quality], self.carriage_root:GetChild(v.truckid-1), function(base_obj)
                if not self.gameObject then
                   
                    --self.animatorHelper:SetAnimatorState(self.animatorHelper.Dead)

                    return
                end
                local animator = UIUtil.GetTrans(base_obj.transform, "center/mache_Simple")
                self.animatorHelper:AddAnimator(animator)
                self.animatorHelper:SetAnimatorSpeed(0)
            end)
        else
            if  v.status == CarriageStatus.CARRIAGE_STATUS_DEPART then
                if self.truckEntity[v.truckid] then
                    self.truckEntity[v.truckid]:Dispose()
                    self.truckEntity[v.truckid] = nil
                end
            else
                if self.qualityState[v.truckid] ~= v.quality  then
                    self.qualityState[v.truckid] = v.quality
                    self.truckEntity[v.truckid]:Dispose()
                    self.truckEntity[v.truckid] = union_slg_scene_object.CM():Init(AB[v.quality], self.carriage_root:GetChild(v.truckid-1), function(base_obj)
                        if not self.gameObject then
                            log.Error("重复实例化 path = " .. path .. " InstantiateModelAsync = " )
                         
                            self:Dispose()
                            return
                        end
                        local animator = UIUtil.GetTrans(base_obj.transform, "center/mache_Simple")
                        self.animatorHelper:AddAnimator(animator)
                    end)
                end
            end
            
        end

        local open,des,param = activity_open.CheckFunctionIsOpen(activity_open.OpenIdEnum.TradeCaravan)
        open = function_open_mgr.CheckFunctionIsOpen(function_open_mgr.OpenIdEnum.TradeCaravan)
        if not open and param and param.OpenType == activity_open.OpenType.SerVerCrossDay then
            --未达开服天数
            --flow_text.Add(string.formatL(658095,param.iServerDays))
            return
        end
        --待发车
        if v.status  == CarriageStatus.CARRIAGE_STATUS_UNLOCK and not self:GetBubbleEntity(GWConst.EHomeBubbleEntityType.CarriageDepart,self.bubbleParent[v.truckid])  then
            self:DisposeBubbleEntity(GWConst.EHomeBubbleEntityType.CarriageReward,self.bubbleParent[v.truckid])
            self:DisposeBubbleEntity(GWConst.EHomeBubbleEntityType.COUNTDOWN,self.bubbleParent[v.truckid])
            if not noDepartTime then
                local truckData = intercity_trucks_data.GetSelfTruckList()
                if not truckData or not truckData[v.truckid] then
                    --log.Error("未找到车辆数据 " .. v.truckid)
                    return
                end
                local selfData
                self:CreateBubbleEntity(GWConst.EHomeBubbleEntityType.CarriageDepart,function()
                    truckData = intercity_trucks_data.GetSelfTruckList()
                    local data =
                    {
                        curSelectTruckId = v.truckid,
                        truckData = truckData[v.truckid]
                    }
                    ui_window_mgr:ShowModule("ui_intercity_trucks_departure",nil,nil,data)
                end,nil,nil,self.bubbleParent[v.truckid])
            end
            --已发车    
        elseif v.status  == CarriageStatus.CARRIAGE_STATUS_DEPART and not self:GetBubbleEntity(GWConst.EHomeBubbleEntityType.COUNTDOWN,self.bubbleParent[v.truckid]) then
            self:DisposeBubbleEntity(GWConst.EHomeBubbleEntityType.CarriageDepart,self.bubbleParent[v.truckid])
            self:CreateBubbleEntity(GWConst.EHomeBubbleEntityType.COUNTDOWN,function()
                local intercity_trucks_mgr = require "intercity_trucks_mgr"
                intercity_trucks_mgr.OpenTrucksScene(2,v.truckid)
                --GWG.GWHomeMgr.HideHomeScene()
            end,nil,{
                uDoneTime = v.finishtm,
                uSid = self.serData.uSid,
                callback = function()
                    self.isClockDown = false
                    self:DisposeBubbleEntity(GWConst.EHomeBubbleEntityType.COUNTDOWN)
                end,
            },  self.bubbleParent[v.truckid])

            --待领奖
        elseif v.status  == CarriageStatus.CARRIAGE_STATUS_REWARD and not self:GetBubbleEntity(GWConst.EHomeBubbleEntityType.CarriageReward,self.bubbleParent[v.truckid]) then
            self:CreateBubbleEntity(GWConst.EHomeBubbleEntityType.CarriageReward,function()
                local net_carriage_module =require "net_carriage_module"
                local truckData = intercity_trucks_data.GetSelfTruckList()
                if truckData[v.truckid] then
                    ui_window_mgr:ShowModule("uintercity_trucks_detail",nil,nil,truckData[v.truckid])
                end
                net_carriage_module.MSG_CARRIAGE_TRADE_REWARD_REQ(v.truckid)
            end,nil,nil, self.bubbleParent[v.truckid])
            self:DisposeBubbleEntity(GWConst.EHomeBubbleEntityType.COUNTDOWN,self.bubbleParent[v.truckid])
        end
    else
        if self.truckEntity[v.truckid] then
            self.truckEntity[v.truckid]:Dispose()
            self.truckEntity[v.truckid] = nil
        end
        self:DisposeBubbleEntity(GWConst.EHomeBubbleEntityType.CarriageReward,self.bubbleParent[v.truckid])
        self:DisposeBubbleEntity(GWConst.EHomeBubbleEntityType.COUNTDOWN,self.bubbleParent[v.truckid])
        self:DisposeBubbleEntity(GWConst.EHomeBubbleEntityType.CarriageDepart,self.bubbleParent[v.truckid])
    end
end


---@see 设置能否升级
---@see override
function GWHomeCompBuilding:SetCanUpgrade(canUpgrade, needCheck)
    buildingBaseClass.SetCanUpgrade(self,canUpgrade,needCheck)
end
function GWHomeCompBuilding:RegisterListener()
    buildingBaseClass.RegisterListener(self)
    self.OnRefreshModel = function()
        self:RefreshCarriageModel()
    end
    self.OnRefreshOneModel = function(name,msg)
        local total,current = intercity_trucks_data.GetDepartureCount()
        self:RefreshOneCarriage(msg.truck,current>=total)
    end
    event.Register(event_carriage_define.TMSG_CARRIAGE_MINE_DATA_RSP, self.OnRefreshModel)
    event.Register(event_carriage_define.TMSG_CARRIAGE_CNETER_DATA_NTF, self.OnRefreshModel)
    event.Register(event_carriage_define.TMSG_CARRIAGE_REPART_RSP, self.OnRefreshOneModel)
    event.Register(event_carriage_define.TMSG_CARRIAGE_PRE_TRADE_RSP, self.OnRefreshOneModel)

    self.CheckIsServerTimeOpen = function(name,areaId)
        self:CheckIsServerOpen()
    end
    event.Register(gw_ed.GW_HOME_GRID_DATA_UPDATE, self.CheckIsServerTimeOpen)

end

function GWHomeCompBuilding:UnregisterListener()
    buildingBaseClass.UnregisterListener(self)
    event.Unregister(event_carriage_define.TMSG_CARRIAGE_MINE_DATA_RSP, self.OnRefreshModel)
    event.Unregister(event_carriage_define.TMSG_CARRIAGE_CNETER_DATA_NTF, self.OnRefreshModel)
    event.Unregister(event_carriage_define.TMSG_CARRIAGE_REPART_RSP, self.OnRefreshOneModel)
    event.Unregister(event_carriage_define.TMSG_CARRIAGE_PRE_TRADE_RSP, self.OnRefreshOneModel)
end
--- 回收
---@see  override
function GWHomeCompBuilding:Recycle()
    buildingBaseClass.Recycle(self)
end

function GWHomeCompBuilding:OnClickGrid(names, gridX, gridY,addId)
    if self.gameObject and addId then
        local open,des,param = activity_open.CheckFunctionIsOpen(activity_open.OpenIdEnum.TradeCaravan)
        open = function_open_mgr.CheckFunctionIsOpen(function_open_mgr.OpenIdEnum.TradeCaravan)

        if not open and param and param.OpenType == activity_open.OpenType.SerVerCrossDay then
            --未达开服天数
            flow_text.Add(string.formatL(658095,param.iServerDays))
            return
        end
        if addId == 0 and open then
            local intercity_trucks_mgr = require "intercity_trucks_mgr"
            intercity_trucks_mgr.OpenTrucksScene()
        end
        local truckData = intercity_trucks_data.GetSelfTruckList()
        if truckData and truckData[addId] then
            local v = truckData[addId]
            if v.status  == CarriageStatus.CARRIAGE_STATUS_UNLOCK then
                local total,current = intercity_trucks_data.GetDepartureCount()
                if current >= total then
                    flow_text.Add(lang.Get(658089))
                    return
                end
                local data =
                {
                    curSelectTruckId = addId,
                    truckData = truckData[addId]
                }
                ui_window_mgr:ShowModule("ui_intercity_trucks_departure",nil,nil,data)
                --已发车    
            elseif v.status  == CarriageStatus.CARRIAGE_STATUS_DEPART then
                local intercity_trucks_mgr = require "intercity_trucks_mgr"
                intercity_trucks_mgr.OpenTrucksScene(2,addId)
                --GWG.GWHomeMgr.HideHomeScene()
                --待领奖
            elseif v.status  == CarriageStatus.CARRIAGE_STATUS_REWARD then
                local net_carriage_module =require "net_carriage_module"
                ui_window_mgr:ShowModule("uintercity_trucks_detail",nil,nil,v)
                net_carriage_module.MSG_CARRIAGE_TRADE_REWARD_REQ(addId)
            elseif v.status  == CarriageStatus.CARRIAGE_STATUS_LOCKED then
             
                flow_text.Add(lang.Get(658087))
            end
        else 
            log.Error("没有货车数据",addId)
        end
    end
end


--- 弃用
---@see  override
function GWHomeCompBuilding:Dispose()
    if self.animatorHelper then
        self.animatorHelper:SetAnimatorSpeed(1)
        self.animatorHelper:Dispose()
        self.animatorHelper = nil
    end
    if self.truckEntity then
        for k,v in pairs(self.truckEntity) do
            v:Dispose()
        end
        self.truckEntity = nil
    end
    self.qualityState = nil
    self.bubbleParent = nil
   
    buildingBaseClass.Dispose(self)
end
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

return GWHomeCompBuilding