local print = print
local require = require
local pairs = pairs
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local UIUtil = CS.Common_Util.UIUtil

local game_scheme = require "game_scheme"
local log = require "log"
local alliance_const = require "alliance_const"
local alliance_pb = require "alliance_pb"
local e_handler_mgr = require "e_handler_mgr"
local time_util = require "time_util"
local alliance_data = require "alliance_data"
local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_alliance_record_binding"
local card_sprite_asset = require "card_sprite_asset"
local gw_common_util = require "gw_common_util"
local windowMgr = require "ui_window_mgr"
--region View Life
module("ui_alliance_record")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)
    self.selfColor = "#3A6AA9"
    self.enemyColor = "#FF4D2A"
    self:InitScrollTable()
    self.VData = {}
    self:InitToggleList()
    self.recordIconAsset = self.recordIconAsset or card_sprite_asset.CreateGWAllianceRecordIconAsset()
    --lang 特殊处理映射表
    self.needLangGetMap = {
        [3]  = { [3] = true },
        [4]  = { [3] = true },
        [24] = { [3] = true },
        [25] = { [5] = true },
        [26] = { [3] = true },
        [27] = { [2] = true },
        [28] = { [2] = true },
        [29] = { [4] = true },
        [30] = { [2] = true },
    }
    self.jumpXYGetMap = {
        [11] = { xIndex = 2, yIndex = 3 },
        [24] = { xIndex = 4, yIndex = 5 },
        [25] = { xIndex = 6, yIndex = 7 },
        [26] = { xIndex = 4, yIndex = 5 },
        [27] = { xIndex = 3, yIndex = 4 },
        [28] = { xIndex = 3, yIndex = 4 },
        [29] = { xIndex = 5, yIndex = 6 },
        [30] = { xIndex = 3, yIndex = 4 },
    }
end

function UIView:OnShow()
    self.__base.OnShow(self)
    self.toggleList[alliance_const.AllianceRecordType.War]:SetToggleState(true)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    if self.toggleList then
        for i, v in pairs(self.toggleList) do
            v:Dispose()
        end
        self.toggleList = nil
    end
    
    if self.recordIconAsset then
        self.recordIconAsset:Dispose()
        self.recordIconAsset = nil
    end

    if self.srt_Content then
        self.srt_Content:OnItemsDispose()
    end
    
    self.VData = nil
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic

---@public InitToggleList 初始化Toggle列表
function UIView:InitToggleList()

    for i = 1, 4 do
        local itemGo = UIUtil.AddChild(self.rtf_togParent.gameObject, self.item_upToggle)
        local item_up_toggle = require "item_up_toggle"
        self.toggleList = self.toggleList or {}

        local togText = ""
        if i == alliance_const.AllianceRecordType.War then
            togText = lang.Get(1005401)
        elseif i == alliance_const.AllianceRecordType.Member then
            togText = lang.Get(1005402)
        elseif i == alliance_const.AllianceRecordType.Info then
            togText = lang.Get(1005403)
        elseif i == alliance_const.AllianceRecordType.Activity then
            togText = lang.Get(1005404)
        end

        self.toggleList[i] = item_up_toggle.NewItem(itemGo)
        self.toggleList[i]:UpdateData(togText,function(state)
            if state then
                self:SwitchPanel(i)
            end
        end,self.rtf_togParent)
    end
end

---@public function 初始化列表
function UIView:InitScrollTable()
    self.srt_Content.onItemRender = function(scroll_rect_item, index, dataItem)
        self:DanOnItemRender(scroll_rect_item, index, dataItem)
    end
    self.srt_Content.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item and scroll_rect_item.data then

        end
    end
end

---记录列表刷新方法
function UIView:DanOnItemRender(scroll_rect_item, index, dataItem)
    scroll_rect_item.data = scroll_rect_item.data or { index, dataItem }
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem
    
    if not dataItem then
        return
    end
    local warIcon = scroll_rect_item:Get("warIcon")
    local recordIcon = scroll_rect_item:Get("recordIcon")

    self:SetActive(warIcon, self.recordType == alliance_const.AllianceRecordType.War)
    self:SetActive(recordIcon, self.recordType ~= alliance_const.AllianceRecordType.War)

    if self.recordType ~= alliance_const.AllianceRecordType.War then
        self:OtherRecordItemRefresh(scroll_rect_item, index, dataItem)
    else
        self:WarRecordItemRefresh(scroll_rect_item, index, dataItem)
    end
end

---@public RefreshRecordTable 刷新记录列表数据
function UIView:RefreshRecordTable(data)
    local length = #data
    if not data or length == 0 then
        self:SetActive(self.rtf_notRecord, true)
        return
    end

    self:InitScrollTable()
    self:SetActive(self.rtf_notRecord, false)
    self.srt_Content:SetData(data, length)
    self.srt_Content:Refresh(0, -1)
end

---@public SwitchPanel 切换面板
function UIView:SwitchPanel(recordType)
    if self.recordType ~= recordType then --防止重复切换
        self.srt_Content:OnItemsDispose()
        e_handler_mgr.TriggerHandler(window.controller_name, "ChangeRecordData", recordType)
        self.recordType = recordType
    end
end

---@public function 刷新战争记录
function UIView:WarRecordItemRefresh(scroll_rect_item,index, dataItem)
    local leftBg = scroll_rect_item:Get("leftBg")
    local typeIcon = scroll_rect_item:Get("typeIcon")
    local resultText = scroll_rect_item:Get("resultText")
    local recordInfoText = scroll_rect_item:Get("recordInfoText")
    local recordTimeText = scroll_rect_item:Get("recordTimeText")

    recordTimeText.text = time_util.FormatTime(dataItem.recordId / 1000)
    leftBg:Switch(dataItem.isWin and 0 or 1)
    --自己是否是攻击方法
    local isAtkSelf = alliance_data.GetUserAllianceId() == dataItem.AtkInfo.allianceId
    typeIcon:Switch(isAtkSelf and 0 or 1)
    local atkShort = ""
    if not string.IsNullOrEmpty(dataItem.AtkInfo.shortName) then
        atkShort = string.format("<color=%s>[%s]</color>", isAtkSelf and self.selfColor or self.enemyColor, dataItem.AtkInfo.shortName)
    end
    local atkRoleName = string.format("<color=%s>%s</color>", isAtkSelf and self.selfColor or self.enemyColor, dataItem.AtkInfo.roleName)
    local defShort = ""
    if not string.IsNullOrEmpty(dataItem.DefInfo.shortName) then
        defShort = string.format("<color=%s>[%s]</color>", isAtkSelf and self.enemyColor or self.selfColor, dataItem.DefInfo.shortName)
    end
    local defRoleName = string.format("<color=%s>%s</color>", isAtkSelf and self.enemyColor or self.selfColor, dataItem.DefInfo.roleName)
    if isAtkSelf then
        if dataItem.isWin == 1 then
            --大获全胜 600129
            recordInfoText.text = string.format2(lang.Get(600129), atkShort, atkRoleName, defShort, defRoleName)
        else
            --铩羽而归 600130
            recordInfoText.text = string.format2(lang.Get(600130), atkShort, atkRoleName, defShort, defRoleName)
        end
        resultText.text = dataItem.isWin == 1 and lang.Get(675020) or lang.Get(675021)
    else
        if dataItem.isWin == 0 then
            --固入金汤 600131
            recordInfoText.text = string.format2(lang.Get(600131), atkShort, atkRoleName, defShort, defRoleName)
        else
            --遗憾失守 600132
            recordInfoText.text = string.format2(lang.Get(600132), atkShort, atkRoleName, defShort, defRoleName)
        end
        resultText.text = dataItem.isWin == 0 and lang.Get(675020) or lang.Get(675021)
    end
    e_handler_mgr.TriggerHandler(window.controller_name, "OnScrollRectItemRender", index, dataItem)
end

function UIView:OtherRecordItemRefresh(scroll_rect_item, index, dataItem)
    local leftBg = scroll_rect_item:Get("leftBg")
    local recordIcon = scroll_rect_item:Get("recordIcon")
    local recordInfoText = scroll_rect_item:Get("recordInfoText")
    local recordInfoJumpText = scroll_rect_item:Get("recordInfoJumpText")
    local recordTimeText = scroll_rect_item:Get("recordTimeText")
    local posBtn = scroll_rect_item:Get("posBtn")
    recordTimeText.text = time_util.FormatTime(dataItem.time)
    leftBg:Switch(2)
    
    local cfg = game_scheme:LeagueLog_0(dataItem.msgType)
    if cfg then
        --通过读表显示图标
        local iconStr = cfg.LogIcon
        self.recordIconAsset:GetSprite(iconStr, function(sprite)
            if not util.IsObjNull(recordIcon) then
                recordIcon.sprite = sprite
            end
        end)
    end

    local paramArr = {}
    if dataItem.arrInfo then
        for i, v in ipairs(dataItem.arrInfo) do
            if v.type == 1 then
                table.insert(paramArr, v.logStr)
            else
                local logNum = v.logNum
                local shouldGetLang = false
                -- 提前处理通用情况
                local indexMap = self.needLangGetMap[dataItem.msgType]
                if indexMap and indexMap[v.indexId] then
                    shouldGetLang = true
                end
                if shouldGetLang then
                    logNum = lang.Get(logNum)
                end
                table.insert(paramArr, logNum)
            end
        end
    end

    if cfg.Logjump == 1 then -- 配置的跳转标记
        self:SetActive(recordInfoText, false)
        self:SetActive(recordInfoJumpText, true)
        recordInfoJumpText.text = string.format3(lang.Get(cfg.LogLang), paramArr)

        --找到要跳转的坐标
        local i = self.jumpXYGetMap[dataItem.msgType]
        local targetPosX = paramArr[i.xIndex]
        local targetPosY = paramArr[i.yIndex]

        --注册跳转事件
        recordInfoJumpText.onHrefClick:RemoveAllListeners()
        recordInfoJumpText.onHrefClick:AddListener(function(href)
            --<a href=Logjump>
            --log.Log(href) 输出：Logjump
            --log.Log(dataItem.msgType .. "======X:" .. targetPosX .. "Y:" .. targetPosY)
            gw_common_util.JumpToGrid({ x = targetPosX, y = targetPosY },nil, gw_common_util.GetSandZoneSandBoxSid())
            windowMgr:UnloadModule("ui_alliance_record") --跳转后需要及时关闭打开的窗口才能实现连续跳转
            windowMgr:UnloadModule("ui_alliance_main")
        end)
    else
        recordInfoText.text = string.format3(lang.Get(cfg.LogLang),paramArr)
    end
end

--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME
        window.isBlurBg = true

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
            window:LoadUIResource(tempPath, nil, tempParent, nil, true, false)
        else
            window:LoadUIResource(ui_path, nil, nil, nil, true, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
