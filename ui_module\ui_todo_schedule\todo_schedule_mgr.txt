-- todo_schedule_mgr.txt ------------------------------------------
-- author:  hym
-- date:    2025/08/12
-- ver:     1.0
-- desc:    待办日程mgr
--------------------------------------------------------------

local require 	= require
local pairs 	= pairs
local ipairs 	= ipairs
local table 	= table
local event_alliance_define = require "event_alliance_define"
local player_prefs = require "player_prefs"
local game_scheme = require "game_scheme"
local event_DesertStrom_define = require "event_DesertStrom_define"
local sand_ui_event_define = require "sand_ui_event_define"
local red_const = require "red_const"
local gw_sand_event_define = require "gw_sand_event_define"
local gw_event_activity_define = require "gw_event_activity_define"
local red_system = require "red_system"
local event = require "event"
local todo_schedule_define = require "todo_schedule_define"
local log = require "log"
local todo_schedule_data = require "todo_schedule_data"
local festival_activity_cfg = require "festival_activity_cfg"
module("todo_schedule_mgr")


function Init()
    event.Register(gw_event_activity_define.GW_SCHEDULE_DATA_UPDATE, UpdateBubbleAndRed)
    event.Register(event.FIRST_LOGIN_CREATE_DATA_FINISH, ReqSerData)
    event.Register(event.SERVER_CROSS_DAY,RefreshSerData)
    event.Register(gw_sand_event_define.GW_SAND_UPDATE_SELF_DATA,SetMainUIShowOverBySand)
    red_system.RegisterRedFunc(red_const.Enum.ScheduleEntrance, GetMainRedPoint)  --注册主界面入口红点
    event.Register(event.LOGIN_UI_POPUP_END,UpdateBubbleAndRed)

    --联盟boss特殊处理状态切换
    event.Register(gw_event_activity_define.GW_ACTIVITY_ALLIANCE_BOSS_STATE_CHANGE, RefreshAllianceBossState)
    --沙漠风暴切换状态特殊处理
    event.Register(event_DesertStrom_define.DESERTSTROM_SIGNUP_INFO_UPDATE, RefreshDesertStormState)
    
    --活动开放刷新列表
    event.Register(gw_event_activity_define.GW_ONE_ACTIVITY_UPDATE, UpdateTaskRedPoint)
    event.Register(event_alliance_define.UPDATE_ALLIANCE_ROLE_DATA, UpdateTaskRedByAlliance)
end

function UpdateBubbleAndRed()
    todo_schedule_data.IsAllDataAndUIReady()
end

--特殊化处理联盟boss状态切换
function RefreshAllianceBossState(_, curState, lastState)
    local allianceboss_pb = require "allianceboss_pb"
    if curState == allianceboss_pb.enAllianceBossState_GetReady and lastState == allianceboss_pb.enAllianceBossState_NotReady then
        UpdateSingleRed(festival_activity_cfg.ActivityCodeType.AllianceBoss)
    end
end

--特殊化处理沙漠风暴
function RefreshDesertStormState(isChange)
    if not isChange then
        return
    end
    local data = todo_schedule_data.GetSerDataByHeadingCode(festival_activity_cfg.ActivityCodeType.DesertStorm)
    if data and todo_schedule_data.GetIsDoingActivity(data) then
        UpdateSingleRed(festival_activity_cfg.ActivityCodeType.DesertStorm)
    end
end

--刷新单个红点和气泡
function UpdateSingleRed(headingCode)
    local cfgData = todo_schedule_data.GetCfgData()
    local serData = todo_schedule_data.GetSerDataByHeadingCode(headingCode)
    if not serData then
        return
    end
    local atyId = serData.atyID
    todo_schedule_data.SetMainRedBubbleData(cfgData[atyId])
    red_system.TriggerRed(red_const.Enum.ScheduleEntrance)
    local main_slg_tips_mgr = require "main_slg_tips_mgr"
    main_slg_tips_mgr.ShowTip(main_slg_tips_mgr.EnumTipType.ScheduleTip)
    
end

--请求待办日程服务器数据
function ReqSerData()
    local net_todo_schedule = require "net_todo_schedule"
    net_todo_schedule.MSG_SCHEDULE_LIST_REQ()
end

--跨天刷新数据
function RefreshSerData()
    todo_schedule_data.ClearDay()
    ReqSerData()
    todo_schedule_data.SetIsCrossDaya()
end

--沙盘跨服刷新主界面入口
function SetMainUIShowOverBySand()
    local main_slg_const = require "main_slg_const"
    event.Trigger(sand_ui_event_define.GW_MAIN_REFRESH_BUTTON, main_slg_const.MainButtonType.schedule)
end

--刷新任务列表红点
function UpdateTaskRedPoint(_, activityID, isOpen, _,oldOpen, headingCode)
    if not isOpen or isOpen == oldOpen then
        return
    end
    local list = todo_schedule_data.GetAllianceR4R5TodoHeadingCodeList()
    if not list[headingCode] then
        return
    end
    if not GetTaskRedPoint() then
        return
    end
    local main_slg_tips_mgr = require "main_slg_tips_mgr"
    main_slg_tips_mgr.ShowTip(main_slg_tips_mgr.EnumTipType.ScheduleRedTip)
end

--联盟权限变更刷新红点
function UpdateTaskRedByAlliance()
    local main_slg_tips_mgr = require "main_slg_tips_mgr"
    if not GetTaskRedPoint() then
        main_slg_tips_mgr.HideTip(main_slg_tips_mgr.EnumTipType.ScheduleRedTip)
    else
        main_slg_tips_mgr.ShowTip(main_slg_tips_mgr.EnumTipType.ScheduleRedTip)
    end
    
end


--获取主界面红点
function GetMainRedPoint()
    if GetTaskRedPoint() then
        local main_slg_tips_mgr = require "main_slg_tips_mgr"
        main_slg_tips_mgr.ShowTip(main_slg_tips_mgr.EnumTipType.ScheduleRedTip)
        return false
    end
    local main_slg_tips_mgr = require "main_slg_tips_mgr"
    main_slg_tips_mgr.HideTip(main_slg_tips_mgr.EnumTipType.ScheduleRedTip)
    return todo_schedule_data.GetMainRedPoint()
end

--获取任务红点(任务不走红点需要特殊显示）
function GetTaskRedPoint()
    if not GetIsShowR4R5Page() then
        return false
    end
    local lastTime = player_prefs.GetCacheData(todo_schedule_define.SCHEDULE_OPEN_TIME,0)
    local net_login_module = require "net_login_module"
    local zeroTime = net_login_module.GetServerNextZeroTime() - todo_schedule_define.DayTime
    if lastTime == 0 or lastTime <= zeroTime then
        return true
    end
    local serTaskList = todo_schedule_data.GetAllianceR4R5TodoList()
    local festival_activity_mgr = require "festival_activity_mgr"
    for k, v in pairs(serTaskList) do
        if v then
            local cfg = todo_schedule_data.GetTaskCfgData(k)
            local isOpen = true
            if cfg and cfg.cfg and cfg.cfg.ActiveConditionType ~= 0 and cfg.cfg.ActiveConditionType ~= 1 then
                isOpen = festival_activity_mgr.GetIsOpenByHeadingCode(cfg.cfg.ActiveConditionType, true)
            end
            isHasRed = isOpen and ((v.playerCanGet and v.serData.state == todo_schedule_define.ServerTaskStateType.canGet) or v.serData.state == todo_schedule_define.ServerTaskStateType.doing)
            if isHasRed then
                return true
            end 
        end
    end
    return false
end


--领取任务奖励
function GetTaskReward(taskId)
    local net_todo_schedule = require "net_todo_schedule"
    net_todo_schedule.MSG_ALLIANCE_R4R5TODO_REWARD_REQ(taskId)
end

--任务奖励领取成功弹窗
function ShowGetRewardTip(indexId)
    local reward_mgr = require "reward_mgr"
    local cfg = todo_schedule_data.GetTaskCfgData(indexId)
    if cfg and cfg.cfg and cfg.cfg.Reward then
        local dataList = reward_mgr.GetRewardGoodsList2(cfg.cfg.Reward)
        reward_mgr.ShowReward(dataList, nil, nil, true)
    end
end

--设置r4/r5任务
function SetAllianceR4R5TodoList(msg)
    todo_schedule_data.SetAllianceR4R5TodoList(msg)
end 


--获取是否显示r4/r5页签
function GetIsShowR4R5Page()
    local net_module_open = require "net_module_open"
    local moduleOpenPro_pb = require "moduleOpenPro_pb"
    local isOpen = net_module_open.CheckModuleOpen(moduleOpenPro_pb.emModuleID_AllianceR4R5Todo)
    if not isOpen then
        return false
    end
    local alliance_mgr = require "alliance_mgr"
    local isJoin = alliance_mgr.GetIsJoinAlliance()
    if not isJoin then
        return false
    end
    local alliance_pb = require "alliance_pb"
    local player_mgr = require "player_mgr"
    local temp, authority = alliance_mgr.GetRoleAuthority(player_mgr.GetPlayerRoleID())
    if authority < alliance_pb.emAllianceAuthority_R4 then
        return false
    end
    return true
end

local function TaskBtnClick(data)
    if not data.serData then
        return
    end
    if data.serData.state == todo_schedule_define.ServerTaskStateType.cannotGet then
        local flow_text = require "flow_text"
        local lang = require "lang"
        flow_text.Add(lang.Get(1008619))
    elseif data.serData.state == todo_schedule_define.ServerTaskStateType.doing then
        local module_jumping = require "module_jumping"
        local taskMainCfg = data.taskCfg
        if not taskMainCfg then
            return
        end
        if taskMainCfg.JumpType == 0 then
            return
        end
        local systemSourceCfg = game_scheme:SystemSource_0(taskMainCfg.JumpType)
        if not systemSourceCfg then
            return
        end
        module_jumping.Jump(systemSourceCfg.flag, data)
    elseif data.serData.state == todo_schedule_define.ServerTaskStateType.canGet then
        local net_todo_schedule = require "net_todo_schedule"
        net_todo_schedule.MSG_ALLIANCE_R4R5TODO_REWARD_REQ(data.serData.indexId, data.serData.taskSid)
    end
end

--获取日程显示任务
function GetShowTaskList()
    local festival_activity_mgr = require "festival_activity_mgr"
    local serTaskList = todo_schedule_data.GetAllianceR4R5TodoList()
    local taskList = {}
    local alliance_mgr = require "alliance_mgr"
    local player_mgr = require "player_mgr"
    local temp, authority = alliance_mgr.GetRoleAuthority(player_mgr.GetPlayerRoleID())
    local bit = require "bit"
    for k, v in pairs(serTaskList) do
        if v then
           local canShowTask = bit.band(v.serData.authority, bit.lshift(1, authority - 1)) ~= 0         --是否显示任务
           local cfgData = todo_schedule_data.GetTaskCfgData(v.serData.indexId)
            if cfgData and canShowTask then
                local data = {
                    cfg = cfgData.cfg,
                    taskCfg = cfgData.taskCfg,
                    playerCanGet = v.playerCanGet,
                    serData = v.serData,
                    jumpTaskFunc = function(data)
                        TaskBtnClick(data)
                    end
                }
                if not v.playerCanGet then
                    data.taskState = todo_schedule_define.TaskStateType.hasGet
                elseif v.serData.state == todo_schedule_define.ServerTaskStateType.doing then
                    data.taskState = todo_schedule_define.TaskStateType.doing
                elseif v.serData.state == todo_schedule_define.ServerTaskStateType.canGet or v.serData.state == todo_schedule_define.ServerTaskStateType.cannotGet then
                    data.taskState = todo_schedule_define.TaskStateType.canGet
                end
                data.isSetNotice = data.taskCfg.JumpType and data.taskCfg.JumpType == todo_schedule_define.TaskJumpNoticeID
                local isOpen = true
                if cfgData.cfg.ActiveConditionType ~= 0 and cfgData.cfg.ActiveConditionType ~= 1 then
                    isOpen = festival_activity_mgr.GetIsOpenByHeadingCode(cfgData.cfg.ActiveConditionType, true)
                end
                local previousTaskOver = true       --前置任务是否完成
                local index = cfgData.cfg.TaskPrevious.data[0]
                if index and index ~= 0 then
                    local taskData = serTaskList[index]
                    previousTaskOver = not taskData or taskData.serData.state ~= todo_schedule_define.ServerTaskStateType.doing
                end
                if isOpen and previousTaskOver then
                    table.insert(taskList, data)
                end
            end
        end
    end
    local sortFunc = function(a, b)
        if a.cfg.Priority ~= b.cfg.Priority then
            return a.cfg.Priority < b.cfg.Priority
        end
        return a.serData.indexId < b.serData.indexId
    end
    if #taskList > 1 then
        table.sort(taskList, sortFunc)
    end
    return taskList
end 

--获取日程显示活动
function GetShowActivityList()
    return todo_schedule_data.GetSerDataList()
end

--判断是否需要优先显示任务列表
function GetIsShowTaskListFirst(serTaskList)
    local lastTime = player_prefs.GetCacheData(todo_schedule_define.SCHEDULE_OPEN_TIME,0)
    local net_login_module = require "net_login_module"
    local zeroTime = net_login_module.GetServerNextZeroTime() - todo_schedule_define.DayTime
    if lastTime == 0 or lastTime <= zeroTime then
        return true
    end

    if serTaskList then
        for k, v in ipairs(serTaskList) do
            if v.taskState == todo_schedule_define.TaskStateType.canGet  or v.taskState == todo_schedule_define.TaskStateType.doing then
                return true
            end
        end
    end
    return false
end



function GetCurShowList(listType)
    local taskList = GetShowTaskList()
    local activityList = GetShowActivityList()
    if not taskList or not activityList then
        return
    end
    if listType then
        if listType == todo_schedule_define.ShowListType.R4R5Task then
            return todo_schedule_define.ShowListType.R4R5Task, taskList
        elseif listType == todo_schedule_define.ShowListType.todayActivity then
            return todo_schedule_define.ShowListType.todayActivity, activityList.todayList
        elseif listType == todo_schedule_define.ShowListType.nextDayActivity then
            return todo_schedule_define.ShowListType.nextDayActivity, activityList.nextDayList
        end
    end
    if GetIsShowR4R5Page() then
        --需要显示任务页签
        if GetIsShowTaskListFirst(taskList) then
            return todo_schedule_define.ShowListType.R4R5Task, taskList
        end
    end
    if #activityList.todayList > 0 or #activityList.nextDayList <= 0 then
        return todo_schedule_define.ShowListType.todayActivity, activityList.todayList
    else
        return todo_schedule_define.ShowListType.nextDayActivity, activityList.nextDayList
    end
end


--获取公告语言
function GetNoticeLang()
    local langId = player_prefs.GetCacheData(todo_schedule_define.ALLIANCE_NOTICE_LANG, 0)
    if langId ~= 0 then
        return langId
    end
    local alliance_data = require "alliance_data"
    return alliance_data.GetUserAllianceLanguage()
end

--设置公告语言
function SetNoticeLang(lang)
    player_prefs.SetCacheData(todo_schedule_define.ALLIANCE_NOTICE_LANG, lang)
end

--获取公告内容
function GetNoticeContent(indexId, curLanguage)
    local cfgData = todo_schedule_data.GetTaskCfgData(indexId)
    if not cfgData or not cfgData.cfg.DefaultAnnouncement or not cfgData.cfg.AnnouncementReadingType then
        return
    end
    local alliance_mgr = require "alliance_mgr"
    if not curLanguage then
        curLanguage = GetNoticeLang()
    end
    local language = alliance_mgr.GetAllianceLanguage(curLanguage)
    local allianceID = alliance_mgr.GetUserAllianceId()
    if todo_schedule_define.GetNoticeContent[cfgData.cfg.AnnouncementReadingType] then
        return todo_schedule_define.GetNoticeContent[cfgData.cfg.AnnouncementReadingType](cfgData.cfg, language, allianceID) 
    end
end

--记录已发布的公告内容
function SetNoticeContent(data)
    local cfgData = todo_schedule_data.GetTaskCfgData(data.indexId)
    if not cfgData or not cfgData.cfg.DefaultAnnouncement or not cfgData.cfg.AnnouncementReadingType then
        return
    end
    local alliance_mgr = require "alliance_mgr"
    local allianceID = alliance_mgr.GetUserAllianceId()
    data.language = alliance_mgr.GetAllianceLanguage(data.langId)
    if todo_schedule_define.SetNoticeContent[cfgData.cfg.AnnouncementReadingType] then
        todo_schedule_define.SetNoticeContent[cfgData.cfg.AnnouncementReadingType](data, allianceID)
    end
end

--打开发布公告界面
function OpenNoticePage(data)
    local time_util = require "time_util"
    local ui_window_mgr = require "ui_window_mgr"
    local weekDay = time_util.GetWeekDay()
    local noticeData = {
        indexId = data.serData.indexId,
        weekDay = weekDay,
    }
    ui_window_mgr:ShowModule("ui_alliance_notice_schedule",nil, nil, noticeData)
end

