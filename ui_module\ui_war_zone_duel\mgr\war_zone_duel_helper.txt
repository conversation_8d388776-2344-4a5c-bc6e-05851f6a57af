---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by du<PERSON><PERSON>.
--- DateTime: 2025/4/22 16:46
---

local require = require
local war_zone_duel_data = require "war_zone_duel_data"
local war_zone_duel_define = require "war_zone_duel_define"
local gw_sand_data = require "gw_sand_data"

---@class war_zone_duel_helper
local M = {}

M.enZoneBattleDuelStage = war_zone_duel_define.enZoneBattleDuelStage

--判断是否在战斗的双方，且处于某个阶段
function M.IsSelfZoneDuelActiveInStage(stage)
    local isPass = false
    local isInStage = war_zone_duel_data.IsInStage(stage)
    if isInStage and M.IsSelfZoneDuel() then
        isPass = true
    end
    return isPass
end

--判断是否在战斗的双方，且大于等于某个阶段
function M.IsSelfZoneDuelActiveOverStage(stage)
    local isPass = false
    local isInStage = war_zone_duel_data.IsOverStage(stage)
    if isInStage and M.IsSelfZoneDuel() then
        isPass = true
    end
    return isPass
end

--判断是否在战斗的双方，且小于等于某个阶段
function M.IsSelfZoneDuelActiveLessStage(stage)
    local isPass = false
    local isInStage = war_zone_duel_data.IsLessStage(stage)
    if isInStage and M.IsSelfZoneDuel() then
        isPass = true
    end
    return isPass
end

--判断是否在战斗的双方，且处于国会争夺阶段
function M.IsSelfZoneDuelActiveInStage_CongressBattleStart()
    return M.IsSelfZoneDuelActiveInStage(M.enZoneBattleDuelStage.CongressBattleStart)
end

--判断是否在战斗的双方，且处于国会争夺结束阶段
function M.IsSelfZoneDuelActiveOverStage_CongressBattleSettlement()
    return M.IsSelfZoneDuelActiveOverStage(M.enZoneBattleDuelStage.CongressBattleSettlement)
end

---判断自己是否处于某个阶段
function M.IsInStage_CongressBattleStart()
    return war_zone_duel_data.IsInStage(M.enZoneBattleDuelStage.CongressBattleStart)
end

---是否可以战区对决时操作沙盘建筑按钮
function M.IsCanOperate()
    return M.IsSelfZoneDuel() and M.IsSelfAtk()
end

---是否为自己服参与的战区对决战斗
function M.IsSelfZoneDuel()
    --判断自己战区对决战斗的双方的sid是否包含视野范围内的沙盘sid
    local selfDuelWorldIDs = war_zone_duel_data.GetSelfZoneDuelWorldIDs()
    if not selfDuelWorldIDs then
        return false
    end
    local viewSandSid = gw_sand_data.selfData.GetSandBoxSid()
    if not viewSandSid then
        return false
    end
    
    return viewSandSid == selfDuelWorldIDs.worldIDA or viewSandSid == selfDuelWorldIDs.worldIDB
end

--判断自己是攻击方
function M.IsSelfAtk()
    local selfSandBoxSid = gw_sand_data.selfData.GetZoneSandBoxSid()
    return war_zone_duel_data.GetSelfZoneDuelAtkWorldID() == selfSandBoxSid
end

--判断传入的服务器ID是否自己服
function M.IsNotSame2SelfWorldId(worldId)
    if M.IsSelfZoneDuelActiveInStage(M.enZoneBattleDuelStage.CrossStart)  then
        local selfSandBoxSid = gw_sand_data.selfData.GetZoneSandBoxSid()
        return selfSandBoxSid and selfSandBoxSid ~= worldId
    end
    return false
end

--国会争夺阶段(若结算则不算争夺阶段)
function M.IsCongressAtk()
    return M.IsSelfZoneDuelActiveInStage_CongressBattleStart() and
            (not war_zone_duel_data.IsInStage(M.enZoneBattleDuelStage.CongressBattleSettlement)
                    and not war_zone_duel_data.IsInStage(M.enZoneBattleDuelStage.CongressBattleEnd))
end

function M.IsCongressAtkEnd()
    return not M.IsSelfZoneDuelActiveInStage_CongressBattleStart() and
            (war_zone_duel_data.IsInStage(M.enZoneBattleDuelStage.CongressBattleSettlement)
                    or war_zone_duel_data.IsInStage(M.enZoneBattleDuelStage.CongressBattleEnd))
end

return M