local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type
local os = os

local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"
local log = require "log"

local event = require "event"



local event_define = require "event_define"

local halloween_activity_slot_machine_history_data = require("halloween_activity_slot_machine_history_data")
local halloween_activity_slot_machine_setting_data = require "halloween_activity_slot_machine_setting_data"
local halloween_activity_slot_machine_const = require "halloween_activity_slot_machine_const"
local time_util = require "time_util"

local skep_mgr = require "skep_mgr"
local halloween_slot_machine_mgr = require "halloween_slot_machine_mgr"

local festival_activity_mgr = require "festival_activity_mgr"
local game_scheme = require("game_scheme")
local net_halloween_slot_machine_panel = require "net_halloween_slot_machine_panel"

local slot_machine_ani_helper = require "slot_machine_ani_helper"

local reward_mgr = require "reward_mgr"
local util = require "util"


local activeTimer = nil

--region Controller Life
module("ui_halloween_activity_slot_machine_panel_controller")
local controller = nil
---@class ui_halloween_activity_slot_machine_panel_controller : ControllerBase
local UIController = newClass("ui_halloween_activity_slot_machine_panel_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.__base.Init(self, view_name, controller_name)

    self.activityMainId = halloween_slot_machine_mgr:GetActivityID()
    self:GetActivityTime()

    halloween_activity_slot_machine_setting_data:Init()
    halloween_activity_slot_machine_history_data:Init()

    self:RegisterEvent(event_define.EVENT_HALLOWEEN_SLOT_MECHINE_DRAW_RESULT, function(event_name, data)

            -- result_index = msg.nIndex,
            -- count = msg.nDrawsCnt,
        local result_index = data.result_index
        local cfg = game_scheme:SlotGame_0(result_index, self.activityMainId)
        local patternGroup = string.split(cfg.TaskID, "#")
        local patternGroupRandomed = slot_machine_ani_helper.RollPattern(patternGroup, self.slotPatterIconIDList)
        local iconIDList = {}
        for i, v in ipairs(patternGroupRandomed) do
            local iconID = self.slotPatternsDic[v]
            if iconID then
                table.insert(iconIDList, iconID)
            else
                log.Error("halloween_activity_slot_machine_panel_controller", "iconID is nil, patternID = " .. v)
            end
        end
        halloween_activity_slot_machine_history_data:AddHistory(os.server_time(), cfg.ID, self:GetDrawCountSetting(), patternGroupRandomed)

        local selectRewardData = reward_mgr.GetRewardGoodsList2(cfg.RewardID) or {}
        local is_skip = halloween_activity_slot_machine_setting_data:GetValue("speed_up") == 1
        if is_skip then
            self:TriggerUIEvent("RenderSlotMachine", self.slotPatterIconIDList, self.slotPatternsList,patternGroupRandomed)
            reward_mgr.ShowReward(selectRewardData, nil, nil, false)
            self:RefreshUI()
        else
            self:TriggerUIEvent("RunSlotMachine", iconIDList, function ()
                --TODO 先用默认弹窗奖励
                reward_mgr.ShowReward(selectRewardData, nil, nil, false)
                self:RefreshUI()
            end)
        end
        
    end)

    -- 数据
    self.CData.SettingData = halloween_activity_slot_machine_setting_data
    self.slotPatterns = {}
    for i = 0, game_scheme:SlotPattern_nums() - 1 do
        local slotPattern = game_scheme:SlotPattern(i)
        if slotPattern.AtyID == self.activityMainId then
            table.insert(self.slotPatterns, slotPattern)
        end
    end
    self.slotPatternsDic = {}
    for i, v in ipairs(self.slotPatterns) do
        self.slotPatternsDic[v.PatternID] = v.cardID
    end
    self.slotPatternsList = {}
    for i, v in ipairs(self.slotPatterns) do
        table.insert(self.slotPatternsList, v.PatternID)
    end
    self.slotPatterIconIDList = {}
    for i, v in ipairs(self.slotPatterns) do
        table.insert(self.slotPatterIconIDList, v.cardID)
    end
end

function UIController:OnShow()
    self.__base.OnShow(self)

    self:RefreshUI()
    
    -- 老虎机
    self:TriggerUIEvent("RenderSlotMachine", self.slotPatterIconIDList, self.slotPatternsList,halloween_activity_slot_machine_history_data:GetLastPattern())
end

function UIController:Close(data)   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents() 
end

function UIController:AutoUnsubscribeEvents() 
end
--endregion

--region Controller Logic


function UIController:RefreshUI()
    local festivalCfgData = festival_activity_mgr.GetActivityCfgByActivityID(halloween_slot_machine_mgr.GetActivityID())
 
    
    -- 活动倒计时
    if activeTimer then
        self:RemoveTimer(activeTimer)
        activeTimer = nil
    end
    local activeEndTime = self:GetActivityTime()
    local activeTimerTemp = self:CreateTimer(1, function()
        local curTime = os.server_time()
        local time = time_util.FormatTime5(activeEndTime - curTime)
        self:TriggerUIEvent("RenderActivityCountDownTime", time)
    end)
    activeTimer = activeTimerTemp
    
    -- 代币
    --local slot_coin_id = halloween_activity_slot_machine_const.slot_coin_id
    local slot_coin_id = halloween_slot_machine_mgr.GetCoinID()
    self:TriggerUIEvent("RenderCoinItem", slot_coin_id, skep_mgr.GetGoodsNum(slot_coin_id))
    
    -- -- 奖励道具图标
    -- local next_task = halloween_slot_machine_mgr.GetNextProgressTask()
    -- local next_reward = halloween_slot_machine_mgr.GetTaskReward(next_task)
    -- self:TriggerUIEvent("RenderRewardItem", next_reward, 
    --         halloween_slot_machine_mgr.GetCurDrawCount(), next_task.ConditionValue1)
    
    -- 每次抽奖次数设置
    self:TriggerUIEvent("RenderDrawTimesBtnInfo", halloween_activity_slot_machine_setting_data:GetValue("draw_count_per_click"))

    self:TriggerUIEvent("RenderRemainTimesInfo", 0 )

    self:TriggerUIEvent("RenderSkipState", halloween_activity_slot_machine_setting_data:GetValue("speed_up") == 1)
end

function UIController:OnBtnDrawClickedProxy()
    if halloween_activity_slot_machine_const.use_local_test then
        util.DelayCallOnce(0.5, function()
            event.Trigger(event_define.EVENT_HALLOWEEN_SLOT_MECHINE_DRAW_RESULT, {
                result_index = 1,
                count = 1,
            })
        end)
        return
    end
    --点击抽奖，发送请求
    net_halloween_slot_machine_panel.MSG_SLOT_MACHINE_DRAW_REQ(self.activityMainId, self:GetDrawCountSetting())
end
function UIController:OnBtnTimesClickedProxy()
end
function UIController:OnTogSpeedValueChange(state)
    halloween_activity_slot_machine_setting_data:SetValue("speed_up", state)
    self:TriggerUIEvent("RenderSkipState", state)
end
function UIController:OnBtnItem_addClickedProxy()
    -- TODO 弹礼包页面
end


function UIController:GetDrawCountSetting()
    return halloween_activity_slot_machine_setting_data:GetValue("draw_count_per_click")
end




-- 获取活动结束时间
function UIController:GetActivityTime()
    local actData = festival_activity_mgr.GetActivityDataByActivityID(self.activityMainId)
    return actData and actData.endTimeStamp or 0
end


--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
