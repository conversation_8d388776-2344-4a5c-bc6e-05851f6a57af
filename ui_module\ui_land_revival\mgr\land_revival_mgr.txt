local print = print
local require = require
local pairs = pairs
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type

local gw_event_activity_define = require "gw_event_activity_define"
local event_task_define = require "event_task_define"
local gw_task_data = require "gw_task_data"
local red_const = require "red_const"
local red_system = require "red_system"
local util = require "util"
local os = require "os"
local player_mgr = require "player_mgr"
local game_scheme = require "game_scheme"
local log = require "log"
local land_revival_data = require "land_revival_data"
local event = require "event"
local PlayerPrefs = CS.UnityEngine.PlayerPrefs
module("land_revival_mgr")


function Init()
    event.Register(event.LOGIN_UI_POPUP_END, LoginPopEnd)
    event.Register(event_task_define.REFRESH_TASK, TaskPointUpdate) --任务刷新,红点注册
    event.Register(event.USER_DATA_RESET, Clear)


    red_system.RegisterRedFunc(red_const.Enum.LandRevivalMain, LandRevivalMainRed)
    red_system.RegisterRedFunc(red_const.Enum.LandMainDayItem, LandMainDayItemRed)
    
    red_system.RegisterRedFunc(red_const.Enum.LandRevivalBox, LandRevivalBoxRed)
    red_system.RegisterRedFunc(red_const.Enum.LandRevivalRewardReceive, LandRevivalRewardReceiveRed)
    
    red_system.RegisterRedFunc(red_const.Enum.LandRevival7DayPage, LandRevival7DayPageRed)
    red_system.RegisterRedFunc(red_const.Enum.LandRevivalDayToggle, LandRevivalDayToggleRed)
    red_system.RegisterRedFunc(red_const.Enum.LandRevivalHammerToggle, LandRevivalHammerToggleRed)
    
end

---@public function 登录弹窗结束时
function LoginPopEnd()
    local initCfg = game_scheme:InitBattleProp_0(8450)
    if initCfg then
        local cdTime = initCfg.szParam[0] --360分钟
    end

    local clickTime = GetEnterActivityTime()
    if util.IsAfterDay(clickTime, os.server_time()) then
        local main_slg_tips_mgr = require "main_slg_tips_mgr"
        main_slg_tips_mgr.ShowTip(main_slg_tips_mgr.EnumTipType.LandRevivalTip)
    end
end

---@public function 进入活动记录时间
function RecordEnterActivityTime()

    local main_slg_tips_mgr = require "main_slg_tips_mgr"
    main_slg_tips_mgr.HideTip(main_slg_tips_mgr.EnumTipType.LandRevivalTip)
    
    local roleID = player_mgr.GetPlayerRoleID()
    local key = string.format("%s_%s", "EnterLandRevival_", roleID)
    local time = PlayerPrefs.SetInt(key, os.server_time())
    return time
end

---@public function 进入活动记录时间
function GetEnterActivityTime()
    local roleID = player_mgr.GetPlayerRoleID()
    local key = string.format("%s_%s", "EnterLandRevival_", roleID)
    local time = PlayerPrefs.GetInt(key, 0)
    return time
end

--region 领地复兴红点
function LandRevivalMainRed()
    local red = 0
    for i = 1, 7 do
        red = red + LandMainDayItemRed(i)
    end
    return red
end

function LandMainDayItemRed(dayIndex)
    local red = 0
    red = red + LandRevivalDayToggleRed(dayIndex)
    return red
end

function LandRevival7DayPageRed()
    local red = 0
    for i = 1, 7 do
        red = red + LandRevivalDayToggleRed(i)
    end
    return red
end

function LandRevivalDayToggleRed(dayIndex)
    local red = 0
    --遍历这一天的任务
    for i = 1, 3 do
        red = red + LandRevivalHammerToggleRed(dayIndex, i)
    end
    return red
end

function LandRevivalHammerToggleRed(dayIndex, pageIndex)
    local red = 0
    --如果有可领取的奖励
    local allDayList = land_revival_data.GetAllDayTaskListData()
    if not allDayList then
        return red
    end
    local curDay = land_revival_data.GetCurDayNew()

    if curDay < dayIndex then
        return red
    end
    
    local dayData = allDayList[dayIndex]
    if dayData then
        local pageData = dayData[pageIndex]
        for i, v in ipairs(pageData.taskIDList) do
            local taskCfg = game_scheme:TaskMain_0(v)
            local taskData = gw_task_data.GetTaskData(v)
            if taskData and taskCfg then
                if not taskData.status and taskData.isGet then
                    red = red + 1
                end
            end
        end
    end
    return red
end

function LandRevivalBoxRed()
    local red = 0
    red = red + LandRevivalRewardReceiveRed()
    return red
end

function LandRevivalRewardReceiveRed()
    local red = 0
    local isCanReceive = land_revival_data.IsJudgeAllTaskCanReceive()
    if isCanReceive then
        red = red + 1
    end
    return red
end

---@public function 任务刷新，更新红点
function TaskPointUpdate(eventName, taskData, moduleId, moduleList)
    local gw_task_const = require "gw_task_const"
    if moduleList[gw_task_const.TaskModuleType.pioneer_target] then
        red_system.TriggerRed(red_const.Enum.LandRevivalHammerToggle)
        red_system.TriggerRed(red_const.Enum.LandRevivalRewardReceive)
        event.Trigger(gw_event_activity_define.GW_ACTIVITY_RED_NEED_UPDATE,land_revival_data.GetActivityID())
    end
end

--endregion

function Clear()
    land_revival_data.Clear()
end