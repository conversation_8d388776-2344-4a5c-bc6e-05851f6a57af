local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local UIUtil = CS.Common_Util.UIUtil

local red_const = require "red_const"
local time_util = require "time_util"
local os = require "os"
local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_land_revival_main_binding"
local item_land_task = require "item_land_task"
--region View Life
module("ui_land_revival_main")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.VData = {}
    self:BindUIRed(self.btn_BoxIcon.transform, red_const.Enum.LandRevivalBox,nil,{pos ={ x = 92 ,y = 85 }})
    self:InitMainEffect()
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.VData = nil
    if self.endTimer then
        util.RemoveDelayCall(self.endTimer)
        self.endTimer = nil
    end

    if self.itemLandTaskArr then
        for i, v in pairs(self.itemLandTaskArr) do
            v:Dispose()
        end
        self.itemLandTaskArr = nil
    end

    if self.itemBoxBubble then
        self.itemBoxBubble:Dispose()
        self.itemBoxBubble = nil
    end
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic
---@public function 初始化7天任务展示
function UIView:InitDayItem(viewData)
    if not viewData then
        return
    end
    self.itemLandTaskArr = self.itemLandTaskArr or {}
    for i, v in ipairs(viewData) do
        local itemGo = UIUtil.AddChild(v.isUp and self.rtf_topDayParent.gameObject or self.rtf_bottomDayParent.gameObject, self.rtf_landTask.gameObject)
        local item = item_land_task.NewItem(itemGo)
        item:UpdateData(v)
        self.itemLandTaskArr[i] = item

        self:BindUIRed(itemGo.transform, red_const.Enum.LandMainDayItem,{i})
    end
end

---@public function 设置活动结束时间
function UIView:SetActivityTimer(endTime)
    if endTime then
        self.endTimer = self.endTimer or util.IntervalCall(1, function()
            local tempTime = endTime - os.server_time()
            if not util.IsObjNull(self.txt_time) then
                self.txt_time.text = string.format(lang.Get(1008005), time_util.FormatTime5(tempTime))
            end
            if tempTime <= 0 then
                if not util.IsObjNull(self.txt_time) then
                    self.txt_time.text = lang.Get(102401) --"活动已结束"
                end
                self.endTimer = nil
                return true
            end
        end)
    end
end

---@public function 设置vip任务进度
function UIView:SetVipTaskProgress(curProgress, totalProgressRate)
    if totalProgressRate > 0 then
        self.sld_leftSlider.value = curProgress/totalProgressRate
    end
    if curProgress >= totalProgressRate then
        self.txt_sldNum.text = lang.Get(36085)
    else
        self.txt_sldNum.text = string.format("%s/%s", curProgress, totalProgressRate)
    end
end

---@public function 初始化宝箱奖励展示
function UIView:InitBoxRewardShow(rewardData)
    if rewardData then
        local item_box_bubble = require "item_box_bubble"
        self.itemBoxBubble = self.itemBoxBubble or item_box_bubble.NewItem(self.item_boxBubble)
        self.itemBoxBubble:UpdateData(rewardData)
        self:SetActive(self.item_boxBubble, true)
    else
        self.ss_BoxIcon:Switch(1)
        self:SetActive(self.item_boxBubble, false)
        self.ator_BoxIcon.enabled = false
    end
end

function UIView:InitMainEffect()
    local ui_module_item = require "ui_module_item"
    self.roleModelRes = "animations/characters/ldlingzhu/edit_ldlingzhu.prefab"
    if not self.roleShow then
        self.roleShow = ui_module_item:new()
        self.roleShow:Init(self.rImg_effectRawImage.gameObject, nil)
    end
    self.roleShow:SetChangeModel(self.roleModelRes)
    self.roleShow:SetNodePosition(0, 0, -30)
    self.roleShow:SetNodeRotation(0, 180, 0)
    table.insert(self.VData,self.roleShow)
end

function UIView:PlayBoxAnim(isPlay)
    self.ator_BoxIcon.enabled = isPlay
end
    
--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME
		window.isBlurBg = true

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, true, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil, true, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
