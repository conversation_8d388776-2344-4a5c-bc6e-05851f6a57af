---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by <PERSON><PERSON><PERSON>.
--- DateTime: 2025/4/1 15:27
---

local require = require
local net_sandbox_module = require "net_sandbox_module"
local TransmitLuaFuncReq = net_sandbox_module.TransmitLuaFuncReq
local gw_common_util = require "gw_common_util"
local ZoneBattleDuel_pb = require "ZoneBattleDuel_pb"
local xManMsg_pb = require "xManMsg_pb"
local net_route = require "net_route"
local net = require "net"
local lua_pb = require "lua_pb"
local flow_text = require "flow_text"
local event = require "event"
local pairs = pairs
local war_zone_duel_define = require "war_zone_duel_define"
local war_zone_duel_data = require "war_zone_duel_data"
local error_code_pb= require "error_code_pb"

module("net_war_zone_duel_module")

---战区对决总信息请求
function MSG_ZONEBATTLEDUEL_GETINFO_REQ()
    local msg = ZoneBattleDuel_pb.TMSG_ZONEBATTLEDUEL_GETINFO_REQ()
    msg.nWorldID =gw_common_util.GetSandZoneSandBoxSid()
    TransmitLuaFuncReq(xManMsg_pb.MSG_ZONEBATTLEDUEL_GETINFO_REQ, msg, lua_pb.MicroService_SandBox)
end

---战区对决总信息回复
function MSG_ZONEBATTLEDUEL_GETINFO_RSP_Handler(msg)
    if msg.errCode == 0 then
        war_zone_duel_data.CopyFrom_MSG_ZONEBATTLEDUEL_GETINFO_RSP(msg)
    elseif msg.errCode ==error_code_pb.enErr_ZoneBattleDuel_Activity_Not_Open then
        war_zone_duel_data.ActivityNotOpen(msg)
    else
        flow_text.AddErrorCodeRes(msg.errCode)
    end
    event.Trigger(war_zone_duel_define.Evt_MSG_ZONEBATTLEDUEL_GETINFO_RSP_Handler, msg)
    local gw_event_activity_define = require "gw_event_activity_define"
    event.Trigger(gw_event_activity_define.GW_ACTIVITY_ENTRANCE_UPDATE)
end

---领取战区对决胜利奖励（连胜因素）请求
function MSG_ZONEBATTLEDUEL_GET_ROUNDWIN_REWARD_REQ()
    local msg = ZoneBattleDuel_pb.TMSG_ZONEBATTLEDUEL_GET_ROUNDWIN_REWARD_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ZONEBATTLEDUEL_GET_ROUNDWIN_REWARD_REQ, msg)--领取奖励发往场景服
end
---领取战区对决胜利奖励（连胜因素）回复
function MSG_ZONEBATTLEDUEL_GET_ROUNDWIN_REWARD_RSP_Handler(msg)
    event.Trigger(war_zone_duel_define.Evt_MSG_ZONEBATTLEDUEL_GET_ROUNDWIN_REWARD_RSP_Handler, msg)
    if msg.errCode ~= 0 then
        flow_text.AddErrorCodeRes(msg.errCode)
    else
        local red_system = require "red_system"
        local red_const = require "red_const"
        red_system.TriggerRed(red_const.Enum.WarZoneRewardBtn)
    end
end

---轮空奖励领取请求
function MSG_ZONEBATTLEDUEL_NOMATCH_REWARD_REQ()
    local msg=ZoneBattleDuel_pb.TMSG_ZONEBATTLEDUEL_NOMATCH_REWARD_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ZONEBATTLEDUEL_NOMATCH_REWARD_REQ, msg)--领取奖励发往场景服
end

---轮空奖励领取回复
function MSG_ZONEBATTLEDUEL_NOMATCH_REWARD_RSP_Handler(msg)
    if msg.errCode ~= 0 then
        flow_text.AddErrorCodeRes(msg.errCode)
    end
    event.Trigger(war_zone_duel_define.Evt_MSG_ZONEBATTLEDUEL_NOMATCH_REWARD_RSP_Handler, msg)
end


---战区1vs1信息请求（积分比拼界面打开时请求）.
function MSG_ZONEBATTLEDUEL_GET_VSINFO_REQ()
    local msg = ZoneBattleDuel_pb.TMSG_ZONEBATTLEDUEL_GET_VSINFO_REQ()
    msg.nWorldID = gw_common_util.GetSandZoneSandBoxSid()
    TransmitLuaFuncReq(xManMsg_pb.MSG_ZONEBATTLEDUEL_GET_VSINFO_REQ, msg, lua_pb.MicroService_SandBox)
end

---战区1vs1信息回复 (积分详情item变动)
function MSG_ZONEBATTLEDUEL_GET_VSINFO_RSP_Handler(msg)
    if msg.errCode ~= 0 then
        if msg.errCode == error_code_pb.enErr_ZoneBattleDuel_Activity_Not_Open or msg.errCode==error_code_pb.enErr_Abnormal then
            --活动未开启或异常
        else
            flow_text.AddErrorCodeRes(msg.errCode)
        end
    else
        war_zone_duel_data.CopyFrom_TMSG_ZONEBATTLEDUEL_GET_VSINFO_RSP(msg)
        war_zone_duel_data.RefreshReceiveRed() --刷新奖励红点
    end
    event.Trigger(war_zone_duel_define.Evt_MSG_ZONEBATTLEDUEL_GET_VSINFO_RSP_Handler, msg)
end

---巨炮攻击广播（播放动画表现，特效等入口）
function MSG_ZONEBATTLEDUEL_BIGGUN_ATTACK_NTF_Handler(msg)
    event.Trigger(war_zone_duel_define.Evt_MSG_ZONEBATTLEDUEL_BIGGUN_ATTACK_NTF_Handler,msg)
end

---请求查看临时占领列表
function MSG_ZONEBATTLEDUEL_GET_DEFTEAMLIST_REQ(sandBoxID,entitySid)
    local msg = ZoneBattleDuel_pb.TMSG_ZONEBATTLEDUEL_GET_DEFTEAMLIST_REQ()
    msg.sandboxid=sandBoxID
    msg.sid=entitySid
    TransmitLuaFuncReq(xManMsg_pb.MSG_ZONEBATTLEDUEL_GET_DEFTEAMLIST_REQ, msg, lua_pb.MicroService_SandBox,nil, nil, gw_common_util.GetSandRoleSandBoxSid())
end
---请求查看临时占领列表回复
function MSG_ZONEBATTLEDUEL_GET_DEFTEAMLIST_RSP_Handler(msg)
    if msg.errCode ~= 0 then
        flow_text.AddErrorCodeRes(msg.errCode)
    else
        local gw_ed = require("gw_ed")
        gw_ed.mgr:Trigger(gw_ed.GW_SAND_NET_EVENT, "OnSandBoxReinforceListRsp", msg)
    end
    event.Trigger(war_zone_duel_define.Evt_MSG_ZONEBATTLEDUEL_GET_DEFTEAMLIST_RSP_Handler, msg)
end

--活动状态改变时的推送
function MSG_ZONE_BATTLE_DUEL_CHANGE_NTF_Handler(msg)
    war_zone_duel_data.CopyFrom_MSG_ZONE_BATTLE_DUEL_CHANGE_NTF(msg)
    
    event.Trigger(war_zone_duel_define.Evt_ALLIANCE_BATTLE_DUEL_CHANGE_NTF_Handler, msg)
end

--处理请求获取指定战区对决国会坐标点请求
function MSG_ZONEBATTLEDUEL_GET_CONGRESS_POS_REQ(sandboxid)
    local msg = ZoneBattleDuel_pb.TMSG_ZONEBATTLEDUEL_GET_CONGRESS_POS_REQ()
    msg.sandboxid=sandboxid
    TransmitLuaFuncReq(xManMsg_pb.MSG_ZONEBATTLEDUEL_GET_CONGRESS_POS_REQ, msg, lua_pb.MicroService_SandBox ,nil, nil, gw_common_util.GetSandRoleSandBoxSid())
end

---处理请求获取指定战区对决国会坐标点回复
function MSG_ZONEBATTLEDUEL_GET_CONGRESS_POS_RSP_Handler(msg)
    if msg.errCode ~= 0 then
        flow_text.AddErrorCodeRes(msg.errCode)
    end
    event.Trigger(war_zone_duel_define.Evt_MSG_ZONEBATTLEDUEL_GET_CONGRESS_POS_RSP_Handler, msg)
end

---请求获取worlds对应的总统信息
function MSG_ZONEBATTLEDUEL_PRESIDENT_GET_INFO_REQ(data)
    if not data or #data==0 then
        return
    end
    local msg = ZoneBattleDuel_pb.TMSG_ZONEBATTLEDUEL_PRESIDENT_GET_INFO_REQ()
    for i, v in pairs(data) do
        msg.arrWorldIDs:append(v)
    end
    TransmitLuaFuncReq(xManMsg_pb.MSG_ZONEBATTLEDUEL_PRESIDENT_GET_INFO_REQ, msg, lua_pb.MicroService_SandBox)
end

---回复获取worlds对应的总统信息
function MSG_ZONEBATTLEDUEL_PRESIDENT_GET_INFO_RSP_Handler(msg)
    if msg.nErrorCode ~= 0 then
        flow_text.AddErrorCodeRes(msg.nErrorCode)
    else
        war_zone_duel_data.CopyFrom_MSG_ZONEBATTLEDUEL_PRESIDENT_GET_INFO(msg)
    end
    
    event.Trigger(war_zone_duel_define.Evt_MSG_ZONEBATTLEDUEL_PRESIDENT_GET_INFO_RSP_Handler, msg)
end


-- 请求战区对决王城占领积分
function MSG_ZONEBATTLEDUEL_GET_COUNTRY_SCORE_REQ(sandboxSid)
    local msg = ZoneBattleDuel_pb.TMSG_ZONEBATTLEDUEL_GET_COUNTRY_SCORE_REQ()
    msg.nWorldID = sandboxSid
    TransmitLuaFuncReq(xManMsg_pb.MSG_ZONEBATTLEDUEL_GET_COUNTRY_SCORE_REQ, msg, lua_pb.MicroService_SandBox ,nil, nil, sandboxSid)
end

-- 请求战区对决王城占领积分回复
function MSG_ZONEBATTLEDUEL_GET_COUNTRY_SCORE_RSP_Handler(msg)
    if msg.nErrorCode ~= 0 then
        flow_text.AddErrorCodeRes(msg.nErrorCode)
    else
        event.Trigger(war_zone_duel_define.Evt_MSG_ZONEBATTLEDUEL_GET_COUNTRY_SCORE_RSP_Handler, msg)
    end
end

local MessageTable = {
    { xManMsg_pb.MSG_ZONEBATTLEDUEL_GETINFO_RSP, MSG_ZONEBATTLEDUEL_GETINFO_RSP_Handler, ZoneBattleDuel_pb.TMSG_ZONEBATTLEDUEL_GETINFO_RSP },
    { xManMsg_pb.MSG_ZONEBATTLEDUEL_GET_ROUNDWIN_REWARD_RSP, MSG_ZONEBATTLEDUEL_GET_ROUNDWIN_REWARD_RSP_Handler, ZoneBattleDuel_pb.TMSG_ZONEBATTLEDUEL_GET_ROUNDWIN_REWARD_RSP },
    { xManMsg_pb.MSG_ZONEBATTLEDUEL_NOMATCH_REWARD_RSP, MSG_ZONEBATTLEDUEL_NOMATCH_REWARD_RSP_Handler, ZoneBattleDuel_pb.TMSG_ZONEBATTLEDUEL_NOMATCH_REWARD_RSP },
    { xManMsg_pb.MSG_ZONEBATTLEDUEL_GET_VSINFO_RSP, MSG_ZONEBATTLEDUEL_GET_VSINFO_RSP_Handler, ZoneBattleDuel_pb.TMSG_ZONEBATTLEDUEL_GET_VSINFO_RSP },
    { xManMsg_pb.MSG_ZONEBATTLEDUEL_BIGGUN_ATTACK_NTF, MSG_ZONEBATTLEDUEL_BIGGUN_ATTACK_NTF_Handler, ZoneBattleDuel_pb.TMSG_ZONEBATTLEDUEL_BIGGUN_ATTACK_NTF },
    { xManMsg_pb.MSG_ZONEBATTLEDUEL_GET_DEFTEAMLIST_RSP, MSG_ZONEBATTLEDUEL_GET_DEFTEAMLIST_RSP_Handler, ZoneBattleDuel_pb.TMSG_ZONEBATTLEDUEL_GET_DEFTEAMLIST_RSP },
    { xManMsg_pb.MSG_ZONE_BATTLE_DUEL_CHANGE_NTF, MSG_ZONE_BATTLE_DUEL_CHANGE_NTF_Handler, ZoneBattleDuel_pb.TMSG_ZONE_BATTLE_DUEL_CHANGE_NTF },
    {xManMsg_pb.MSG_ZONEBATTLEDUEL_GET_CONGRESS_POS_RSP,MSG_ZONEBATTLEDUEL_GET_CONGRESS_POS_RSP_Handler,ZoneBattleDuel_pb.TMSG_ZONEBATTLEDUEL_GET_CONGRESS_POS_RSP},
    {xManMsg_pb.MSG_ZONEBATTLEDUEL_PRESIDENT_GET_INFO_RSP, MSG_ZONEBATTLEDUEL_PRESIDENT_GET_INFO_RSP_Handler, ZoneBattleDuel_pb.TMSG_ZONEBATTLEDUEL_PRESIDENT_GET_INFO_RSP},
    { xManMsg_pb.MSG_ZONEBATTLEDUEL_GET_COUNTRY_SCORE_RSP, MSG_ZONEBATTLEDUEL_GET_COUNTRY_SCORE_RSP_Handler, ZoneBattleDuel_pb.TMSG_ZONEBATTLEDUEL_GET_COUNTRY_SCORE_RSP },
}

net_route.RegisterMsgHandlers(MessageTable)