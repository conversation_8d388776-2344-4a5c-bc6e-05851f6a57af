-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
local V=protobuf.EnumValueDescriptor
local F=protobuf.FieldDescriptor
local D=protobuf.Descriptor
local E=protobuf.EnumDescriptor
local M=protobuf.Message
module('tbs_pb')


V1M=V(4,"ReportType_BuildReport",0,1)
V2M=V(4,"ReportType_PalDieReport",1,2)
V3M=V(4,"ReportType_RoundReport",2,3)
V4M=V(4,"ReportType_UseSkillReport",3,4)
V5M=V(4,"ReportType_AddBuffReport",4,5)
V6M=V(4,"ReportType_RemoveBuffReport",5,6)
V7M=V(4,"ReportType_NumericReport",6,7)
V8M=V(4,"ReportType_GameOverReport",7,8)
V9M=V(4,"ReportType_StartActionReport",8,9)
V10M=V(4,"ReportType_DestroyReport",9,10)
V11M=V(4,"ReportType_LogReport",10,11)
V12M=V(4,"ReportType_ReliveReport",11,12)
V13M=V(4,"ReportType_UpdatePropReport",12,13)
V14M=V(4,"ReportType_PalTestReport",13,14)
V15M=V(4,"ReportType_WeaponEnergyReport",14,15)
V16M=V(4,"ReportType_CutsceneReport",15,16)
V17M=V(4,"ReportType_SuspendReport",16,17)
V18M=V(4,"ReportType_SkillContainerReport",17,18)
V19M=V(4,"ReportType_MiscReport",18,19)
V20M=V(4,"ReportType_ShieldReport",19,20)
V21M=V(4,"ReportType_SpaceDominatorReport",20,21)
V22M=V(4,"ReportType_SummonedReport",21,22)
V23M=V(4,"ReportType_summonedNumberReport",22,23)
V24M=V(4,"ReportType_MissReport",23,24)
V25M=V(4,"ReportType_SdDmgIncrReport",24,25)
V26M=V(4,"ReportType_ResistCntReport",25,26)
E1M=E(3,"ReportType",".CSMsg.ReportType")
V27M=V(4,"NumericType_HP",0,1)
V28M=V(4,"NumericType_MP",1,2)
V29M=V(4,"NumericType_SP",2,3)
E2M=E(3,"NumericType",".CSMsg.NumericType")
V30M=V(4,"ActionType_SKill",0,1)
V31M=V(4,"ActionType_Effect",1,2)
V32M=V(4,"ActionType_Start",2,3)
E3M=E(3,"ActionType",".CSMsg.ActionType")
V33M=V(4,"ModifierType_Normal",0,1)
V34M=V(4,"ModifierType_Critical",1,2)
V35M=V(4,"ModifierType_Blocked",2,3)
V36M=V(4,"ModifierType_Missed",3,5)
E4M=E(3,"ModifierType",".CSMsg.ModifierType")
V37M=V(4,"EnBuildProp_Hp",0,0)
V38M=V(4,"EnBuildProp_Mp",1,1)
V39M=V(4,"EnBuildProp_Lv",2,2)
V40M=V(4,"EnBuildProp_Max_Hp",3,3)
V41M=V(4,"EnBuildProp_Max_Mp",4,4)
V42M=V(4,"EnBuildProp_Star_Lv",5,5)
V43M=V(4,"EnBuildProp_Low_Hp",6,6)
V44M=V(4,"EnBuildProp_Low_Max_Hp",7,7)
V45M=V(4,"EnBuildProp_Cure_Max_Hp",8,8)
V46M=V(4,"EnBuildProp_Skill1",9,9)
V47M=V(4,"EnBuildProp_Skill6",10,14)
V48M=V(4,"EnBuildProp_SkinID",11,15)
V49M=V(4,"EnBuildProp_DefenceVal",12,16)
V50M=V(4,"EnBuildProp_IsTrialHero",13,17)
V51M=V(4,"EnBuildProp_ArtifactID",14,18)
E5M=E(3,"EnBuildProp",".CSMsg.EnBuildProp")
V52M=V(4,"Left",0,1)
V53M=V(4,"Right",1,2)
E6M=E(3,"TroopDir",".CSMsg.TroopDir")
V54M=V(4,"TEAM_HERO_NONE",0,0)
V55M=V(4,"TEAM_HERO_GATHERING",1,1)
V56M=V(4,"TEAM_HERO_GUARDING",2,2)
V57M=V(4,"TEAM_HERO_ATTACKING_ENEMY_BASE",3,4)
V58M=V(4,"TEAM_HERO_DEFENDING_CITY",4,8)
V59M=V(4,"TEAM_HERO_TRANSPORTING_ATTACK",5,16)
V60M=V(4,"TEAM_HERO_TRANSPORTING_DEFENSE",6,32)
E7M=E(3,"TeamHeroState",".CSMsg.TeamHeroState")
V61M=V(4,"EnStandardArmorBase",0,1)
V62M=V(4,"EnStandardArmorGrowUp",1,2)
E8M=E(3,"EnInitBattleProp",".CSMsg.EnInitBattleProp")
F1D=F(2,"lv",".CSMsg.TSoldierNum.lv",1,0,2,false,0,13,3)
F2D=F(2,"num",".CSMsg.TSoldierNum.num",2,1,2,false,0,13,3)
M1G=D(1,"TSoldierNum",".CSMsg.TSoldierNum",false,{},{},nil,{})
F3D=F(2,"data",".CSMsg.TSoldierNumList.data",1,0,3,false,{},11,10)
M2G=D(1,"TSoldierNumList",".CSMsg.TSoldierNumList",false,{},{},nil,{})
F4D=F(2,"morale",".CSMsg.SoldierBoostPB.morale",1,0,2,false,0,4,4)
F5D=F(2,"weights",".CSMsg.SoldierBoostPB.weights",2,1,2,false,0,4,4)
F6D=F(2,"hp",".CSMsg.SoldierBoostPB.hp",3,2,2,false,0,4,4)
F7D=F(2,"atk",".CSMsg.SoldierBoostPB.atk",4,3,2,false,0,4,4)
F8D=F(2,"def",".CSMsg.SoldierBoostPB.def",5,4,2,false,0,4,4)
M3G=D(1,"SoldierBoostPB",".CSMsg.SoldierBoostPB",false,{},{},nil,{})
F9D=F(2,"props",".CSMsg.PalReportContext.props",1,0,3,false,{},3,2)
F10D=F(2,"palID",".CSMsg.PalReportContext.palID",2,1,2,false,0,13,3)
F11D=F(2,"Row",".CSMsg.PalReportContext.Row",3,2,2,false,0,5,1)
F12D=F(2,"Col",".CSMsg.PalReportContext.Col",4,3,2,false,0,5,1)
F13D=F(2,"cfgID",".CSMsg.PalReportContext.cfgID",5,4,2,false,0,13,3)
F14D=F(2,"IsEnLarge",".CSMsg.PalReportContext.IsEnLarge",6,5,1,false,false,8,7)
F15D=F(2,"adornId",".CSMsg.PalReportContext.adornId",7,6,1,false,0,5,1)
M4G=D(1,"PalReportContext",".CSMsg.PalReportContext",false,{},{},nil,{})
F16D=F(2,"nVersion",".CSMsg.MiscReport.nVersion",1,0,2,false,0,13,3)
M5G=D(1,"MiscReport",".CSMsg.MiscReport",false,{},{},nil,{})
F17D=F(2,"leftID",".CSMsg.BuildReport.leftID",1,0,2,false,0,13,3)
F18D=F(2,"rightID",".CSMsg.BuildReport.rightID",2,1,2,false,0,13,3)
F19D=F(2,"mapID",".CSMsg.BuildReport.mapID",3,2,2,false,0,13,3)
F20D=F(2,"leftPals",".CSMsg.BuildReport.leftPals",4,3,3,false,{},11,10)
F21D=F(2,"rightPals",".CSMsg.BuildReport.rightPals",5,4,3,false,{},11,10)
F22D=F(2,"leftHaloid",".CSMsg.BuildReport.leftHaloid",6,5,2,false,0,5,1)
F23D=F(2,"rightHaloid",".CSMsg.BuildReport.rightHaloid",7,6,2,false,0,5,1)
F24D=F(2,"nTime",".CSMsg.BuildReport.nTime",8,7,1,false,0,13,3)
F25D=F(2,"leftAnimal",".CSMsg.BuildReport.leftAnimal",9,8,1,false,nil,11,10)
F26D=F(2,"rightAnimal",".CSMsg.BuildReport.rightAnimal",10,9,1,false,nil,11,10)
F27D=F(2,"stageType",".CSMsg.BuildReport.stageType",11,10,1,false,0,13,3)
F28D=F(2,"stageLv",".CSMsg.BuildReport.stageLv",12,11,1,false,0,13,3)
F29D=F(2,"leftHaloids",".CSMsg.BuildReport.leftHaloids",13,12,3,false,{},5,1)
F30D=F(2,"rightHaloids",".CSMsg.BuildReport.rightHaloids",14,13,3,false,{},5,1)
M6G=D(1,"BuildReport",".CSMsg.BuildReport",false,{},{},nil,{})
F31D=F(2,"BattleID",".CSMsg.DestroyReport.BattleID",1,0,2,false,0,13,3)
M7G=D(1,"DestroyReport",".CSMsg.DestroyReport",false,{},{},nil,{})
F32D=F(2,"level",".CSMsg.LogReport.level",1,0,2,false,0,5,1)
F33D=F(2,"content",".CSMsg.LogReport.content",2,1,2,false,"",9,9)
F34D=F(2,"sourceType",".CSMsg.LogReport.sourceType",3,2,2,false,0,5,1)
M8G=D(1,"LogReport",".CSMsg.LogReport",false,{},{},nil,{})
F35D=F(2,"palID",".CSMsg.PalDieReport.palID",1,0,2,false,0,13,3)
F36D=F(2,"target",".CSMsg.PalDieReport.target",2,1,2,false,0,13,3)
F37D=F(2,"srcPalKill",".CSMsg.PalDieReport.srcPalKill",3,2,1,false,0,13,3)
M9G=D(1,"PalDieReport",".CSMsg.PalDieReport",false,{},{},nil,{})
F38D=F(2,"round",".CSMsg.RoundReport.round",1,0,2,false,0,13,3)
F39D=F(2,"bEnd",".CSMsg.RoundReport.bEnd",2,1,1,false,false,8,7)
M10G=D(1,"RoundReport",".CSMsg.RoundReport",false,{},{},nil,{})
M11G=D(1,"StartActionReport",".CSMsg.StartActionReport",false,{},{},{},{})
F40D=F(2,"palID",".CSMsg.SkillContainerReport.palID",1,0,2,false,0,13,3)
F41D=F(2,"skillID",".CSMsg.SkillContainerReport.skillID",2,1,2,false,0,13,3)
M12G=D(1,"SkillContainerReport",".CSMsg.SkillContainerReport",false,{},{},nil,{})
F42D=F(2,"palID",".CSMsg.UseSkillReport.palID",1,0,2,false,0,13,3)
F43D=F(2,"targets",".CSMsg.UseSkillReport.targets",2,1,3,false,{},13,3)
F44D=F(2,"skillID",".CSMsg.UseSkillReport.skillID",3,2,2,false,0,13,3)
M13G=D(1,"UseSkillReport",".CSMsg.UseSkillReport",false,{},{},nil,{})
F45D=F(2,"buffID",".CSMsg.AddBuffReport.buffID",1,0,2,false,0,13,3)
F46D=F(2,"buffLevel",".CSMsg.AddBuffReport.buffLevel",2,1,2,false,0,13,3)
F47D=F(2,"palID",".CSMsg.AddBuffReport.palID",3,2,2,false,0,13,3)
F48D=F(2,"target",".CSMsg.AddBuffReport.target",4,3,2,false,0,13,3)
M14G=D(1,"AddBuffReport",".CSMsg.AddBuffReport",false,{},{},nil,{})
F49D=F(2,"buffID",".CSMsg.RemoveBuffReport.buffID",1,0,2,false,0,13,3)
F50D=F(2,"buffLevel",".CSMsg.RemoveBuffReport.buffLevel",2,1,2,false,0,13,3)
F51D=F(2,"palID",".CSMsg.RemoveBuffReport.palID",3,2,2,false,0,13,3)
F52D=F(2,"target",".CSMsg.RemoveBuffReport.target",4,3,2,false,0,13,3)
M15G=D(1,"RemoveBuffReport",".CSMsg.RemoveBuffReport",false,{},{},nil,{})
F53D=F(2,"palID",".CSMsg.NumericReport.palID",1,0,2,false,0,13,3)
F54D=F(2,"targets",".CSMsg.NumericReport.targets",2,1,2,false,0,13,3)
F55D=F(2,"numType",".CSMsg.NumericReport.numType",3,2,2,false,nil,14,8)
F56D=F(2,"numbers",".CSMsg.NumericReport.numbers",4,3,2,false,0,3,2)
F57D=F(2,"actionID",".CSMsg.NumericReport.actionID",6,4,2,false,0,13,3)
F58D=F(2,"Critical",".CSMsg.NumericReport.Critical",7,5,1,false,false,8,7)
F59D=F(2,"Blocked",".CSMsg.NumericReport.Blocked",8,6,1,false,false,8,7)
F60D=F(2,"Dead",".CSMsg.NumericReport.Dead",9,7,1,false,false,8,7)
F61D=F(2,"fakeDead",".CSMsg.NumericReport.fakeDead",10,8,1,false,false,8,7)
F62D=F(2,"removeShield",".CSMsg.NumericReport.removeShield",11,9,1,false,false,8,7)
F63D=F(2,"breakTrend",".CSMsg.NumericReport.breakTrend",12,10,1,false,false,8,7)
F64D=F(2,"damageType",".CSMsg.NumericReport.damageType",13,11,1,false,0,13,3)
M16G=D(1,"NumericReport",".CSMsg.NumericReport",false,{},{},nil,{})
F65D=F(2,"itemID",".CSMsg.RewardContext.itemID",1,0,2,false,0,13,3)
F66D=F(2,"number",".CSMsg.RewardContext.number",2,1,2,false,0,13,3)
F67D=F(2,"rewardID",".CSMsg.RewardContext.rewardID",3,2,2,false,0,13,3)
F68D=F(2,"ItemFlag",".CSMsg.RewardContext.ItemFlag",4,3,1,false,0,13,3)
M18G=D(1,"RewardContext",".CSMsg.RewardContext",false,{},{},nil,{})
F69D=F(2,"winner",".CSMsg.GameOverReport.winner",1,0,2,false,0,13,3)
F70D=F(2,"loser",".CSMsg.GameOverReport.loser",2,1,2,false,0,13,3)
F71D=F(2,"winnerRewards",".CSMsg.GameOverReport.winnerRewards",3,2,3,false,{},11,10)
F72D=F(2,"loserRewards",".CSMsg.GameOverReport.loserRewards",4,3,3,false,{},11,10)
M19G=D(1,"GameOverReport",".CSMsg.GameOverReport",false,{},{},nil,{})
F73D=F(2,"palId",".CSMsg.PalReliveReport.palId",1,0,2,false,0,13,3)
F74D=F(2,"Hp",".CSMsg.PalReliveReport.Hp",2,1,2,false,0,13,3)
F75D=F(2,"Mp",".CSMsg.PalReliveReport.Mp",3,2,2,false,0,13,3)
M20G=D(1,"PalReliveReport",".CSMsg.PalReliveReport",false,{},{},nil,{})
F76D=F(2,"palID",".CSMsg.UpdatePropReport.palID",1,0,2,false,0,13,3)
F77D=F(2,"propID",".CSMsg.UpdatePropReport.propID",2,1,2,false,0,5,1)
F78D=F(2,"propValue",".CSMsg.UpdatePropReport.propValue",3,2,2,false,0,3,2)
M21G=D(1,"UpdatePropReport",".CSMsg.UpdatePropReport",false,{},{},nil,{})
F79D=F(2,"palSerialNo",".CSMsg.PalDamage.palSerialNo",1,0,2,false,0,13,3)
F80D=F(2,"heroId",".CSMsg.PalDamage.heroId",2,1,2,false,0,13,3)
F81D=F(2,"masterid",".CSMsg.PalDamage.masterid",3,2,2,false,0,13,3)
F82D=F(2,"dir",".CSMsg.PalDamage.dir",4,3,2,false,nil,14,8)
F83D=F(2,"damage",".CSMsg.PalDamage.damage",5,4,2,false,0,3,2)
F84D=F(2,"suffer",".CSMsg.PalDamage.suffer",6,5,2,false,0,3,2)
F85D=F(2,"hprate",".CSMsg.PalDamage.hprate",7,6,2,false,0,5,1)
M22G=D(1,"PalDamage",".CSMsg.PalDamage",false,{},{},nil,{})
F86D=F(2,"battleid",".CSMsg.PalTestReport.battleid",1,0,2,false,0,13,3)
F87D=F(2,"round",".CSMsg.PalTestReport.round",2,1,2,false,0,13,3)
F88D=F(2,"bLeftWin",".CSMsg.PalTestReport.bLeftWin",3,2,2,false,false,8,7)
F89D=F(2,"palsDamage",".CSMsg.PalTestReport.palsDamage",4,3,3,false,{},11,10)
M24G=D(1,"PalTestReport",".CSMsg.PalTestReport",false,{},{},nil,{})
F90D=F(2,"palID",".CSMsg.WeaponEnergyReport.palID",1,0,2,false,0,13,3)
F91D=F(2,"masterId",".CSMsg.WeaponEnergyReport.masterId",2,1,2,false,0,5,1)
F92D=F(2,"value",".CSMsg.WeaponEnergyReport.value",3,2,2,false,0,5,1)
M25G=D(1,"WeaponEnergyReport",".CSMsg.WeaponEnergyReport",false,{},{},nil,{})
F93D=F(2,"cutsceneID",".CSMsg.CutsceneReport.cutsceneID",1,0,2,false,0,13,3)
M26G=D(1,"CutsceneReport",".CSMsg.CutsceneReport",false,{},{},nil,{})
F94D=F(2,"palID",".CSMsg.SuspendReport.palID",1,0,2,false,0,13,3)
M27G=D(1,"SuspendReport",".CSMsg.SuspendReport",false,{},{},nil,{})
F95D=F(2,"palID",".CSMsg.ShieldValueReport.palID",1,0,2,false,0,13,3)
F96D=F(2,"masterId",".CSMsg.ShieldValueReport.masterId",2,1,2,false,0,5,1)
F97D=F(2,"value",".CSMsg.ShieldValueReport.value",3,2,2,false,0,5,1)
M28G=D(1,"ShieldValueReport",".CSMsg.ShieldValueReport",false,{},{},nil,{})
F98D=F(2,"sdpowerid",".CSMsg.SpaceDominatorDamageReport.sdpowerid",1,0,2,false,0,5,1)
F99D=F(2,"damage",".CSMsg.SpaceDominatorDamageReport.damage",2,1,2,false,0,3,2)
F100D=F(2,"sdconfigtype",".CSMsg.SpaceDominatorDamageReport.sdconfigtype",3,2,1,false,0,5,1)
M29G=D(1,"SpaceDominatorDamageReport",".CSMsg.SpaceDominatorDamageReport",false,{},{},nil,{})
F101D=F(2,"palID",".CSMsg.CreateSummonedReport.palID",1,0,2,false,0,13,3)
F102D=F(2,"heroid",".CSMsg.CreateSummonedReport.heroid",2,1,2,false,0,13,3)
F103D=F(2,"masterSid",".CSMsg.CreateSummonedReport.masterSid",3,2,2,false,0,5,1)
F104D=F(2,"dir",".CSMsg.CreateSummonedReport.dir",4,3,2,false,nil,14,8)
F105D=F(2,"data",".CSMsg.CreateSummonedReport.data",5,4,1,false,nil,11,10)
M30G=D(1,"CreateSummonedReport",".CSMsg.CreateSummonedReport",false,{},{},nil,{})
F106D=F(2,"numtype",".CSMsg.tSummonedNumberData.numtype",1,0,2,false,0,5,1)
F107D=F(2,"val",".CSMsg.tSummonedNumberData.val",2,1,2,false,0,13,3)
F108D=F(2,"skilleffectId",".CSMsg.tSummonedNumberData.skilleffectId",3,2,1,false,0,5,1)
M31G=D(1,"tSummonedNumberData",".CSMsg.tSummonedNumberData",false,{},{},nil,{})
F109D=F(2,"palID",".CSMsg.UpdateSummonedNumberReport.palID",1,0,2,false,0,13,3)
F110D=F(2,"data",".CSMsg.UpdateSummonedNumberReport.data",2,1,2,false,nil,11,10)
M32G=D(1,"UpdateSummonedNumberReport",".CSMsg.UpdateSummonedNumberReport",false,{},{},nil,{})
F111D=F(2,"palID",".CSMsg.MissReport.palID",1,0,2,false,0,13,3)
M33G=D(1,"MissReport",".CSMsg.MissReport",false,{},{},nil,{})
F112D=F(2,"stageType",".CSMsg.TSdDmgIncrReport.stageType",1,0,2,false,0,5,1)
F113D=F(2,"damage",".CSMsg.TSdDmgIncrReport.damage",2,1,1,false,0,3,2)
F114D=F(2,"dmgflag",".CSMsg.TSdDmgIncrReport.dmgflag",3,2,1,false,0,5,1)
F115D=F(2,"lastdmg",".CSMsg.TSdDmgIncrReport.lastdmg",4,3,1,false,0,3,2)
M34G=D(1,"TSdDmgIncrReport",".CSMsg.TSdDmgIncrReport",false,{},{},nil,{})
F116D=F(2,"palID",".CSMsg.ResistCntReport.palID",1,0,2,false,0,13,3)
F117D=F(2,"curResistCnt",".CSMsg.ResistCntReport.curResistCnt",2,1,2,false,0,5,1)
F118D=F(2,"maxResistCnt",".CSMsg.ResistCntReport.maxResistCnt",3,2,2,false,0,5,1)
M35G=D(1,"ResistCntReport",".CSMsg.ResistCntReport",false,{},{},nil,{})
F119D=F(2,"reportType",".CSMsg.TbsReport.reportType",1,0,2,false,nil,14,8)
F120D=F(2,"buildReport",".CSMsg.TbsReport.buildReport",2,1,1,false,nil,11,10)
F121D=F(2,"palDieReport",".CSMsg.TbsReport.palDieReport",3,2,1,false,nil,11,10)
F122D=F(2,"roundReport",".CSMsg.TbsReport.roundReport",4,3,1,false,nil,11,10)
F123D=F(2,"useSkillReport",".CSMsg.TbsReport.useSkillReport",5,4,1,false,nil,11,10)
F124D=F(2,"addBuffReport",".CSMsg.TbsReport.addBuffReport",6,5,1,false,nil,11,10)
F125D=F(2,"removeBuffReport",".CSMsg.TbsReport.removeBuffReport",7,6,1,false,nil,11,10)
F126D=F(2,"numericReport",".CSMsg.TbsReport.numericReport",8,7,1,false,nil,11,10)
F127D=F(2,"gameOverReport",".CSMsg.TbsReport.gameOverReport",9,8,1,false,nil,11,10)
F128D=F(2,"startActionReport",".CSMsg.TbsReport.startActionReport",10,9,1,false,nil,11,10)
F129D=F(2,"destroyReport",".CSMsg.TbsReport.destroyReport",11,10,1,false,nil,11,10)
F130D=F(2,"logReport",".CSMsg.TbsReport.logReport",12,11,1,false,nil,11,10)
F131D=F(2,"reliveReport",".CSMsg.TbsReport.reliveReport",13,12,1,false,nil,11,10)
F132D=F(2,"updatePropReport",".CSMsg.TbsReport.updatePropReport",14,13,1,false,nil,11,10)
F133D=F(2,"palTestReport",".CSMsg.TbsReport.palTestReport",15,14,1,false,nil,11,10)
F134D=F(2,"wpEnergyReport",".CSMsg.TbsReport.wpEnergyReport",16,15,1,false,nil,11,10)
F135D=F(2,"cutsceneReport",".CSMsg.TbsReport.cutsceneReport",17,16,1,false,nil,11,10)
F136D=F(2,"suspendReport",".CSMsg.TbsReport.suspendReport",18,17,1,false,nil,11,10)
F137D=F(2,"skillContainerReport",".CSMsg.TbsReport.skillContainerReport",19,18,1,false,nil,11,10)
F138D=F(2,"miscReport",".CSMsg.TbsReport.miscReport",20,19,1,false,nil,11,10)
F139D=F(2,"shieldReport",".CSMsg.TbsReport.shieldReport",21,20,1,false,nil,11,10)
F140D=F(2,"sdDamageReport",".CSMsg.TbsReport.sdDamageReport",22,21,1,false,nil,11,10)
F141D=F(2,"summonedReport",".CSMsg.TbsReport.summonedReport",23,22,1,false,nil,11,10)
F142D=F(2,"summonedNumReport",".CSMsg.TbsReport.summonedNumReport",24,23,1,false,nil,11,10)
F143D=F(2,"missReport",".CSMsg.TbsReport.missReport",25,24,1,false,nil,11,10)
F144D=F(2,"sddmgincrReport",".CSMsg.TbsReport.sddmgincrReport",26,25,1,false,nil,11,10)
F145D=F(2,"resistCntReport",".CSMsg.TbsReport.resistCntReport",27,26,1,false,nil,11,10)
M36G=D(1,"TbsReport",".CSMsg.TbsReport",false,{},{},nil,{})
F146D=F(2,"reports",".CSMsg.TbsReports.reports",1,0,3,false,{},11,10)
M38G=D(1,"TbsReports",".CSMsg.TbsReports",false,{},{},nil,{})
F147D=F(2,"buffID",".CSMsg.TbsAdditionBuffContext.buffID",1,0,2,false,0,5,1)
F148D=F(2,"buffLv",".CSMsg.TbsAdditionBuffContext.buffLv",2,1,2,false,0,5,1)
F149D=F(2,"buffNums",".CSMsg.TbsAdditionBuffContext.buffNums",3,2,2,false,0,5,1)
F150D=F(2,"buffRepalceRule",".CSMsg.TbsAdditionBuffContext.buffRepalceRule",4,3,2,false,0,5,1)
M39G=D(1,"TbsAdditionBuffContext",".CSMsg.TbsAdditionBuffContext",false,{},{},nil,{})
F151D=F(2,"gemstoneId",".CSMsg.tGemstoneInfo.gemstoneId",1,0,1,false,0,5,1)
F152D=F(2,"gemstoneLv",".CSMsg.tGemstoneInfo.gemstoneLv",2,1,1,false,0,5,1)
F153D=F(2,"exclusiveId",".CSMsg.tGemstoneInfo.exclusiveId",3,2,1,false,0,5,1)
F154D=F(2,"exclusiveLv",".CSMsg.tGemstoneInfo.exclusiveLv",4,3,1,false,0,5,1)
M40G=D(1,"tGemstoneInfo",".CSMsg.tGemstoneInfo",false,{},{},nil,{})
F155D=F(2,"equipid",".CSMsg.tEquipInfo.equipid",1,0,2,false,0,5,1)
F156D=F(2,"lv",".CSMsg.tEquipInfo.lv",2,1,1,false,0,5,1)
F157D=F(2,"proId",".CSMsg.tEquipInfo.proId",3,2,3,false,{},5,1)
F158D=F(2,"resonanceproId",".CSMsg.tEquipInfo.resonanceproId",4,3,1,false,0,5,1)
F159D=F(2,"secondArtifactLevel",".CSMsg.tEquipInfo.secondArtifactLevel",6,4,1,false,0,5,1)
M41G=D(1,"tEquipInfo",".CSMsg.tEquipInfo",false,{},{},nil,{})
F160D=F(2,"faction",".CSMsg.TFactionAdditionInfo.faction",1,0,2,false,0,5,1)
F161D=F(2,"factionStage",".CSMsg.TFactionAdditionInfo.factionStage",2,1,2,false,0,5,1)
F162D=F(2,"factionProLv",".CSMsg.TFactionAdditionInfo.factionProLv",3,2,2,false,0,5,1)
F163D=F(2,"factionSkillLv",".CSMsg.TFactionAdditionInfo.factionSkillLv",4,3,1,false,0,5,1)
M42G=D(1,"TFactionAdditionInfo",".CSMsg.TFactionAdditionInfo",false,{},{},nil,{})
F164D=F(2,"profession",".CSMsg.TTechAdditionInfo.profession",1,0,2,false,0,5,1)
F165D=F(2,"professionSkillLv",".CSMsg.TTechAdditionInfo.professionSkillLv",2,1,3,false,{},5,1)
F166D=F(2,"professionSkill2Lv",".CSMsg.TTechAdditionInfo.professionSkill2Lv",3,2,3,false,{},5,1)
M43G=D(1,"TTechAdditionInfo",".CSMsg.TTechAdditionInfo",false,{},{},nil,{})
F167D=F(2,"decorate",".CSMsg.TBSDecorateData.decorate",1,0,1,false,nil,11,10)
F168D=F(2,"lv",".CSMsg.TBSDecorateData.lv",2,1,2,false,0,5,1)
M44G=D(1,"TBSDecorateData",".CSMsg.TBSDecorateData",false,{},{},nil,{})
F169D=F(2,"skillID",".CSMsg.TBWeaponDiamondSkill.skillID",1,0,1,false,0,5,1)
F170D=F(2,"skillLv",".CSMsg.TBWeaponDiamondSkill.skillLv",2,1,1,false,0,5,1)
M45G=D(1,"TBWeaponDiamondSkill",".CSMsg.TBWeaponDiamondSkill",false,{},{},nil,{})
F171D=F(2,"diamondID",".CSMsg.TBWeaponDiamond.diamondID",1,0,3,false,{},5,1)
F172D=F(2,"skillData",".CSMsg.TBWeaponDiamond.skillData",2,1,3,false,{},11,10)
F173D=F(2,"curLv",".CSMsg.TBWeaponDiamond.curLv",3,2,1,false,0,5,1)
F174D=F(2,"maxLv",".CSMsg.TBWeaponDiamond.maxLv",4,3,1,false,0,5,1)
M46G=D(1,"TBWeaponDiamond",".CSMsg.TBWeaponDiamond",false,{},{},nil,{})
F175D=F(2,"attributesId",".CSMsg.TBHeroAttributes.attributesId",1,0,2,false,0,3,2)
F176D=F(2,"attributesValue",".CSMsg.TBHeroAttributes.attributesValue",2,1,2,false,0,3,2)
M47G=D(1,"TBHeroAttributes",".CSMsg.TBHeroAttributes",false,{},{},nil,{})
F177D=F(2,"props",".CSMsg.TbsPalContext.props",1,0,3,false,{},3,2)
F178D=F(2,"PalID",".CSMsg.TbsPalContext.PalID",2,1,2,false,0,5,1)
F179D=F(2,"Row",".CSMsg.TbsPalContext.Row",3,2,2,false,0,5,1)
F180D=F(2,"Col",".CSMsg.TbsPalContext.Col",4,3,2,false,0,5,1)
F181D=F(2,"PalCfgID",".CSMsg.TbsPalContext.PalCfgID",5,4,2,false,0,5,1)
F182D=F(2,"isLarge",".CSMsg.TbsPalContext.isLarge",6,5,1,false,false,8,7)
F183D=F(2,"buffContext",".CSMsg.TbsPalContext.buffContext",7,6,3,false,{},11,10)
F184D=F(2,"testSerialNo",".CSMsg.TbsPalContext.testSerialNo",8,7,1,false,0,5,1)
F185D=F(2,"isInSoulLinkSlot",".CSMsg.TbsPalContext.isInSoulLinkSlot",9,8,1,false,false,8,7)
F186D=F(2,"gemstoneInfo",".CSMsg.TbsPalContext.gemstoneInfo",10,9,1,false,nil,11,10)
F187D=F(2,"equipinfo",".CSMsg.TbsPalContext.equipinfo",11,10,3,false,{},11,10)
F188D=F(2,"nFactionAddCE",".CSMsg.TbsPalContext.nFactionAddCE",12,11,1,false,0,5,1)
F189D=F(2,"nTechAddCE",".CSMsg.TbsPalContext.nTechAddCE",13,12,1,false,0,5,1)
F190D=F(2,"nWeight",".CSMsg.TbsPalContext.nWeight",14,13,1,false,0,5,1)
F191D=F(2,"fixedSid",".CSMsg.TbsPalContext.fixedSid",15,14,1,false,0,5,1)
F192D=F(2,"decorate",".CSMsg.TbsPalContext.decorate",16,15,3,false,{},11,10)
F193D=F(2,"nDecorateAddCE",".CSMsg.TbsPalContext.nDecorateAddCE",17,16,1,false,0,5,1)
F194D=F(2,"skinID",".CSMsg.TbsPalContext.skinID",18,17,1,false,0,13,3)
F195D=F(2,"weaponDiamond",".CSMsg.TbsPalContext.weaponDiamond",19,18,1,false,nil,11,10)
F196D=F(2,"weaponDiamondCE",".CSMsg.TbsPalContext.weaponDiamondCE",20,19,1,false,0,5,1)
F197D=F(2,"isTrialHero",".CSMsg.TbsPalContext.isTrialHero",21,20,1,false,false,8,7)
F198D=F(2,"soldierNum",".CSMsg.TbsPalContext.soldierNum",22,21,1,false,0,13,3)
F199D=F(2,"heroAttributes",".CSMsg.TbsPalContext.heroAttributes",23,22,3,false,{},11,10)
M48G=D(1,"TbsPalContext",".CSMsg.TbsPalContext",false,{},{},nil,{})
F200D=F(2,"partType",".CSMsg.TbsWeaponPart.partType",1,0,2,false,0,5,1)
F201D=F(2,"nLv",".CSMsg.TbsWeaponPart.nLv",2,1,2,false,0,5,1)
M49G=D(1,"TbsWeaponPart",".CSMsg.TbsWeaponPart",false,{},{},nil,{})
F202D=F(2,"weaponId",".CSMsg.TbsWeaponContext.weaponId",1,0,2,false,0,5,1)
F203D=F(2,"part",".CSMsg.TbsWeaponContext.part",2,1,3,false,{},11,10)
F204D=F(2,"mateLv",".CSMsg.TbsWeaponContext.mateLv",3,2,1,false,0,5,1)
M50G=D(1,"TbsWeaponContext",".CSMsg.TbsWeaponContext",false,{},{},nil,{})
F205D=F(2,"place",".CSMsg.TDroneStarSchemePlace.place",1,0,2,false,0,5,1)
F206D=F(2,"itemID",".CSMsg.TDroneStarSchemePlace.itemID",2,1,2,false,0,5,1)
F207D=F(2,"starSid",".CSMsg.TDroneStarSchemePlace.starSid",3,2,2,false,0,5,1)
F208D=F(2,"starLv",".CSMsg.TDroneStarSchemePlace.starLv",4,3,2,false,0,5,1)
M51G=D(1,"TDroneStarSchemePlace",".CSMsg.TDroneStarSchemePlace",false,{},{},nil,{})
F209D=F(2,"schemeID",".CSMsg.TDroneStarScheme.schemeID",1,0,1,false,0,13,3)
F210D=F(2,"starSchemePlace",".CSMsg.TDroneStarScheme.starSchemePlace",2,1,3,false,{},11,10)
M52G=D(1,"TDroneStarScheme",".CSMsg.TDroneStarScheme",false,{},{},nil,{})
F211D=F(2,"droneId",".CSMsg.TbsDroneContext.droneId",1,0,2,false,0,5,1)
F212D=F(2,"droneLv",".CSMsg.TbsDroneContext.droneLv",2,1,2,false,0,5,1)
F213D=F(2,"upgradeStage",".CSMsg.TbsDroneContext.upgradeStage",3,2,2,false,0,5,1)
F214D=F(2,"dronePower",".CSMsg.TbsDroneContext.dronePower",4,3,2,false,0,5,1)
F215D=F(2,"part",".CSMsg.TbsDroneContext.part",5,4,3,false,{},11,10)
F216D=F(2,"heroHP",".CSMsg.TbsDroneContext.heroHP",6,5,1,false,0,13,3)
F217D=F(2,"heroDef",".CSMsg.TbsDroneContext.heroDef",7,6,1,false,0,13,3)
F218D=F(2,"heroAtk",".CSMsg.TbsDroneContext.heroAtk",8,7,1,false,0,13,3)
F219D=F(2,"droneHP",".CSMsg.TbsDroneContext.droneHP",9,8,1,false,0,13,3)
F220D=F(2,"droneAtk",".CSMsg.TbsDroneContext.droneAtk",10,9,1,false,0,13,3)
F221D=F(2,"droneDef",".CSMsg.TbsDroneContext.droneDef",11,10,1,false,0,13,3)
F222D=F(2,"skillId",".CSMsg.TbsDroneContext.skillId",12,11,1,false,0,13,3)
F223D=F(2,"adornId",".CSMsg.TbsDroneContext.adornId",13,12,1,false,0,5,1)
F224D=F(2,"droneAllPower",".CSMsg.TbsDroneContext.droneAllPower",14,13,1,false,0,5,1)
F225D=F(2,"advanceLv",".CSMsg.TbsDroneContext.advanceLv",15,14,1,false,0,5,1)
F226D=F(2,"starScheme",".CSMsg.TbsDroneContext.starScheme",16,15,1,false,nil,11,10)
M53G=D(1,"TbsDroneContext",".CSMsg.TbsDroneContext",false,{},{},nil,{})
F227D=F(2,"typeId",".CSMsg.TbsScientific.typeId",1,0,1,false,0,5,1)
F228D=F(2,"weight",".CSMsg.TbsScientific.weight",2,1,1,false,0,5,1)
M54G=D(1,"TbsScientific",".CSMsg.TbsScientific",false,{},{},nil,{})
F229D=F(2,"scientificPower",".CSMsg.TbsScientificContext.scientificPower",1,0,1,false,0,5,1)
F230D=F(2,"scientificPart",".CSMsg.TbsScientificContext.scientificPart",2,1,3,false,{},11,10)
M55G=D(1,"TbsScientificContext",".CSMsg.TbsScientificContext",false,{},{},nil,{})
F231D=F(2,"rarity",".CSMsg.TDecorateData.rarity",1,0,2,false,0,5,1)
F232D=F(2,"rarityNum",".CSMsg.TDecorateData.rarityNum",2,1,2,false,0,5,1)
F233D=F(2,"rarityTotalLv",".CSMsg.TDecorateData.rarityTotalLv",3,2,2,false,0,5,1)
M56G=D(1,"TDecorateData",".CSMsg.TDecorateData",false,{},{},nil,{})
F234D=F(2,"HonorType",".CSMsg.THonorWallData.HonorType",1,0,2,false,0,5,1)
F235D=F(2,"heroNum",".CSMsg.THonorWallData.heroNum",2,1,2,false,0,5,1)
F236D=F(2,"heroTotalLv",".CSMsg.THonorWallData.heroTotalLv",3,2,2,false,0,5,1)
M57G=D(1,"THonorWallData",".CSMsg.THonorWallData",false,{},{},nil,{})
F237D=F(2,"power",".CSMsg.TDecorateInfo.power",1,0,2,false,0,5,1)
F238D=F(2,"decorateData",".CSMsg.TDecorateInfo.decorateData",2,1,3,false,{},11,10)
M58G=D(1,"TDecorateInfo",".CSMsg.TDecorateInfo",false,{},{},nil,{})
F239D=F(2,"power",".CSMsg.THonorWallInfo.power",1,0,1,false,0,5,1)
F240D=F(2,"honorWallData",".CSMsg.THonorWallInfo.honorWallData",2,1,3,false,{},11,10)
M59G=D(1,"THonorWallInfo",".CSMsg.THonorWallInfo",false,{},{},nil,{})
F241D=F(2,"effectId",".CSMsg.TBuffEffectInfo.effectId",1,0,3,false,{},5,1)
F242D=F(2,"effectModule",".CSMsg.TBuffEffectInfo.effectModule",2,1,3,false,{},5,1)
F243D=F(2,"effectMessage",".CSMsg.TBuffEffectInfo.effectMessage",3,2,3,false,{},5,1)
M60G=D(1,"TBuffEffectInfo",".CSMsg.TBuffEffectInfo",false,{},{},nil,{})
F244D=F(2,"buffId",".CSMsg.TBuffBattleInfo.buffId",1,0,3,false,{},5,1)
F245D=F(2,"buffEffectInfo",".CSMsg.TBuffBattleInfo.buffEffectInfo",2,1,3,false,{},11,10)
F246D=F(2,"buffLevel",".CSMsg.TBuffBattleInfo.buffLevel",3,2,3,false,{},5,1)
F247D=F(2,"buffExpired",".CSMsg.TBuffBattleInfo.buffExpired",4,3,3,false,{},5,1)
M61G=D(1,"TBuffBattleInfo",".CSMsg.TBuffBattleInfo",false,{},{},nil,{})
F248D=F(2,"MasterID",".CSMsg.TbsTroopContext.MasterID",1,0,2,false,0,5,1)
F249D=F(2,"dir",".CSMsg.TbsTroopContext.dir",2,1,2,false,nil,14,8)
F250D=F(2,"PalsNum",".CSMsg.TbsTroopContext.PalsNum",3,2,2,false,0,5,1)
F251D=F(2,"pals",".CSMsg.TbsTroopContext.pals",4,3,3,false,{},11,10)
F252D=F(2,"haloid",".CSMsg.TbsTroopContext.haloid",6,4,2,false,0,5,1)
F253D=F(2,"weapon",".CSMsg.TbsTroopContext.weapon",7,5,1,false,nil,11,10)
F254D=F(2,"TreasureIDList",".CSMsg.TbsTroopContext.TreasureIDList",8,6,3,false,{},5,1)
F255D=F(2,"haloids",".CSMsg.TbsTroopContext.haloids",9,7,3,false,{},5,1)
F256D=F(2,"dynamicRate",".CSMsg.TbsTroopContext.dynamicRate",10,8,1,false,0,5,1)
F257D=F(2,"weaponCE",".CSMsg.TbsTroopContext.weaponCE",11,9,1,false,0,5,1)
F258D=F(2,"arrFaction",".CSMsg.TbsTroopContext.arrFaction",12,10,3,false,{},11,10)
F259D=F(2,"arrTech",".CSMsg.TbsTroopContext.arrTech",13,11,3,false,{},11,10)
F260D=F(2,"slgWeightCostPect",".CSMsg.TbsTroopContext.slgWeightCostPect",14,12,1,false,0,5,1)
F261D=F(2,"power",".CSMsg.TbsTroopContext.power",15,13,1,false,0,3,2)
F262D=F(2,"killingTowerWave",".CSMsg.TbsTroopContext.killingTowerWave",16,14,1,false,0,5,1)
F263D=F(2,"soldierNumList",".CSMsg.TbsTroopContext.soldierNumList",17,15,1,false,nil,11,10)
F264D=F(2,"soldierBoost",".CSMsg.TbsTroopContext.soldierBoost",18,16,3,false,{},11,10)
F265D=F(2,"drone",".CSMsg.TbsTroopContext.drone",19,17,1,false,nil,11,10)
F266D=F(2,"scientific",".CSMsg.TbsTroopContext.scientific",20,18,1,false,nil,11,10)
F267D=F(2,"decorateInfo",".CSMsg.TbsTroopContext.decorateInfo",21,19,1,false,nil,11,10)
F268D=F(2,"honorWallInfo",".CSMsg.TbsTroopContext.honorWallInfo",22,20,1,false,nil,11,10)
F269D=F(2,"teamState",".CSMsg.TbsTroopContext.teamState",23,21,1,false,0,5,1)
F270D=F(2,"teamId",".CSMsg.TbsTroopContext.teamId",24,22,1,false,0,5,1)
F271D=F(2,"buffList",".CSMsg.TbsTroopContext.buffList",25,23,1,false,nil,11,10)
M62G=D(1,"TbsTroopContext",".CSMsg.TbsTroopContext",false,{},{},nil,{})
F272D=F(2,"winnerRewardContext",".CSMsg.TbsBattleParam.winnerRewardContext",6,0,3,false,{},11,10)
F273D=F(2,"loserRewardContext",".CSMsg.TbsBattleParam.loserRewardContext",7,1,3,false,{},11,10)
F274D=F(2,"leaguebosswarid",".CSMsg.TbsBattleParam.leaguebosswarid",13,2,1,false,0,5,1)
F275D=F(2,"leftTeamState",".CSMsg.TbsBattleParam.leftTeamState",14,3,1,false,0,5,1)
F276D=F(2,"rightTeamState",".CSMsg.TbsBattleParam.rightTeamState",15,4,1,false,0,5,1)
F277D=F(2,"extraStrData",".CSMsg.TbsBattleParam.extraStrData",16,5,1,false,"",9,9)
F278D=F(2,"teamId",".CSMsg.TbsBattleParam.teamId",24,6,1,false,0,5,1)
M64G=D(1,"TbsBattleParam",".CSMsg.TbsBattleParam",false,{},{},nil,{})
F279D=F(2,"MapID",".CSMsg.TbsBattleContext.MapID",1,0,2,false,0,5,1)
F280D=F(2,"StageType",".CSMsg.TbsBattleContext.StageType",2,1,2,false,0,5,1)
F281D=F(2,"StageLv",".CSMsg.TbsBattleContext.StageLv",3,2,2,false,0,5,1)
F282D=F(2,"TroopsNum",".CSMsg.TbsBattleContext.TroopsNum",4,3,2,false,0,5,1)
F283D=F(2,"troops",".CSMsg.TbsBattleContext.troops",5,4,3,false,{},11,10)
F284D=F(2,"winnerRewardContext",".CSMsg.TbsBattleContext.winnerRewardContext",6,5,3,false,{},11,10)
F285D=F(2,"loserRewardContext",".CSMsg.TbsBattleContext.loserRewardContext",7,6,3,false,{},11,10)
F286D=F(2,"roundLimitNum",".CSMsg.TbsBattleContext.roundLimitNum",8,7,1,false,0,13,3)
F287D=F(2,"defaultWinner",".CSMsg.TbsBattleContext.defaultWinner",9,8,1,false,0,13,3)
F288D=F(2,"defaultLoser",".CSMsg.TbsBattleContext.defaultLoser",10,9,1,false,0,13,3)
F289D=F(2,"sdpowerid",".CSMsg.TbsBattleContext.sdpowerid",11,10,1,false,0,5,1)
F290D=F(2,"sdconfigtype",".CSMsg.TbsBattleContext.sdconfigtype",12,11,1,false,0,5,1)
F291D=F(2,"leaguebosswarid",".CSMsg.TbsBattleContext.leaguebosswarid",13,12,1,false,0,5,1)
F292D=F(2,"bossTrialID",".CSMsg.TbsBattleContext.bossTrialID",14,13,1,false,0,5,1)
F293D=F(2,"rebirthSpaceCfgID",".CSMsg.TbsBattleContext.rebirthSpaceCfgID",15,14,1,false,0,5,1)
M65G=D(1,"TbsBattleContext",".CSMsg.TbsBattleContext",false,{},{},nil,{})
F294D=F(2,"propId",".CSMsg.ExtendedDataTroopPalProp.propId",1,0,2,false,0,5,1)
F295D=F(2,"propValue",".CSMsg.ExtendedDataTroopPalProp.propValue",2,1,2,false,0,5,1)
M63G=D(1,"ExtendedDataTroopPalProp",".CSMsg.ExtendedDataTroopPalProp",false,{},{},nil,{})
F296D=F(2,"props",".CSMsg.ExtendedDataTroopPal.props",1,0,3,false,{},11,10)
F297D=F(2,"PalID",".CSMsg.ExtendedDataTroopPal.PalID",2,1,2,false,0,5,1)
F298D=F(2,"Row",".CSMsg.ExtendedDataTroopPal.Row",3,2,2,false,0,5,1)
F299D=F(2,"Col",".CSMsg.ExtendedDataTroopPal.Col",4,3,2,false,0,5,1)
F300D=F(2,"PalCfgID",".CSMsg.ExtendedDataTroopPal.PalCfgID",5,4,2,false,0,5,1)
F301D=F(2,"isLarge",".CSMsg.ExtendedDataTroopPal.isLarge",6,5,1,false,false,8,7)
M66G=D(1,"ExtendedDataTroopPal",".CSMsg.ExtendedDataTroopPal",false,{},{},nil,{})
F302D=F(2,"MasterID",".CSMsg.ETopRace.MasterID",1,0,2,false,0,5,1)
F303D=F(2,"remainPal",".CSMsg.ETopRace.remainPal",2,1,2,false,0,5,1)
F304D=F(2,"bloodRatio",".CSMsg.ETopRace.bloodRatio",3,2,2,false,0,13,3)
F305D=F(2,"Damage",".CSMsg.ETopRace.Damage",4,3,2,false,0,4,4)
F306D=F(2,"alivenum",".CSMsg.ETopRace.alivenum",5,4,1,false,0,5,1)
M67G=D(1,"ETopRace",".CSMsg.ETopRace",false,{},{},nil,{})
F307D=F(2,"MasterID",".CSMsg.ExtendedDataTroop.MasterID",1,0,2,false,0,5,1)
F308D=F(2,"pals",".CSMsg.ExtendedDataTroop.pals",2,1,3,false,{},11,10)
F309D=F(2,"slgWeightCostPect",".CSMsg.ExtendedDataTroop.slgWeightCostPect",3,2,1,false,0,5,1)
F310D=F(2,"haloid",".CSMsg.ExtendedDataTroop.haloid",4,3,2,false,0,5,1)
F311D=F(2,"weapon",".CSMsg.ExtendedDataTroop.weapon",5,4,1,false,nil,11,10)
F312D=F(2,"TreasureIDList",".CSMsg.ExtendedDataTroop.TreasureIDList",6,5,3,false,{},5,1)
F313D=F(2,"haloids",".CSMsg.ExtendedDataTroop.haloids",7,6,3,false,{},5,1)
F314D=F(2,"chinaRed",".CSMsg.ExtendedDataTroop.chinaRed",8,7,1,false,nil,11,10)
M68G=D(1,"ExtendedDataTroop",".CSMsg.ExtendedDataTroop",false,{},{},nil,{})
F315D=F(2,"minPercentHp",".CSMsg.ChinaRedReportData.minPercentHp",1,0,1,false,0,5,1)
M69G=D(1,"ChinaRedReportData",".CSMsg.ChinaRedReportData",false,{},{},nil,{})
F316D=F(2,"heroId",".CSMsg.PalBatlleDamage.heroId",1,0,2,false,0,13,3)
F317D=F(2,"damageWL",".CSMsg.PalBatlleDamage.damageWL",2,1,1,false,0,3,2)
F318D=F(2,"damageMF",".CSMsg.PalBatlleDamage.damageMF",3,2,1,false,0,3,2)
F319D=F(2,"damageBJ",".CSMsg.PalBatlleDamage.damageBJ",4,3,1,false,0,3,2)
F320D=F(2,"timesBJ",".CSMsg.PalBatlleDamage.timesBJ",5,4,1,false,0,3,2)
F321D=F(2,"sufferWL",".CSMsg.PalBatlleDamage.sufferWL",6,5,1,false,0,3,2)
F322D=F(2,"sufferMF",".CSMsg.PalBatlleDamage.sufferMF",7,6,1,false,0,3,2)
M70G=D(1,"PalBatlleDamage",".CSMsg.PalBatlleDamage",false,{},{},nil,{})
F323D=F(2,"masterId",".CSMsg.TroopBatlleDamage.masterId",1,0,2,false,0,13,3)
F324D=F(2,"palDamage",".CSMsg.TroopBatlleDamage.palDamage",2,1,3,false,{},11,10)
F325D=F(2,"allDamage",".CSMsg.TroopBatlleDamage.allDamage",3,2,1,false,0,4,4)
M71G=D(1,"TroopBatlleDamage",".CSMsg.TroopBatlleDamage",false,{},{},nil,{})
F326D=F(2,"troopDamage",".CSMsg.TroopBatlleDamageAll.troopDamage",1,0,3,false,{},11,10)
M72G=D(1,"TroopBatlleDamageAll",".CSMsg.TroopBatlleDamageAll",false,{},{},nil,{})
F327D=F(2,"troops",".CSMsg.ExtendedDataReport.troops",1,0,3,false,{},11,10)
F328D=F(2,"leagueBossWarDamage",".CSMsg.ExtendedDataReport.leagueBossWarDamage",2,1,1,false,0,3,2)
F329D=F(2,"toprace",".CSMsg.ExtendedDataReport.toprace",3,2,3,false,{},11,10)
F330D=F(2,"rebirthSpaceDamage",".CSMsg.ExtendedDataReport.rebirthSpaceDamage",4,3,1,false,.0,1,5)
F331D=F(2,"damageInfo",".CSMsg.ExtendedDataReport.damageInfo",5,4,1,false,nil,11,10)
F332D=F(2,"battleFrameNo",".CSMsg.ExtendedDataReport.battleFrameNo",6,5,1,false,0,5,1)
M73G=D(1,"ExtendedDataReport",".CSMsg.ExtendedDataReport",false,{},{},nil,{})
F333D=F(2,"DieHero",".CSMsg.ReportDataCommon.DieHero",1,0,1,false,0,5,1)
F334D=F(2,"MaxBout",".CSMsg.ReportDataCommon.MaxBout",2,1,1,false,0,5,1)
F335D=F(2,"WinnerMasterID",".CSMsg.ReportDataCommon.WinnerMasterID",3,2,1,false,0,5,1)
F336D=F(2,"LoserMasterID",".CSMsg.ReportDataCommon.LoserMasterID",4,3,1,false,0,5,1)
F337D=F(2,"IsBattleCatchException",".CSMsg.ReportDataCommon.IsBattleCatchException",5,4,1,false,false,8,7)
F338D=F(2,"PalGames",".CSMsg.ReportDataCommon.PalGames",6,5,3,false,{},5,1)
M74G=D(1,"ReportDataCommon",".CSMsg.ReportDataCommon",false,{},{},nil,{})
F339D=F(2,"soldierBoost",".CSMsg.TroopSoldierProp.soldierBoost",1,0,3,false,{},11,10)
M75G=D(1,"TroopSoldierProp",".CSMsg.TroopSoldierProp",false,{},{},nil,{})

E1M.values = {V1M,V2M,V3M,V4M,V5M,V6M,V7M,V8M,V9M,V10M,V11M,V12M,V13M,V14M,V15M,V16M,V17M,V18M,V19M,V20M,V21M,V22M,V23M,V24M,V25M,V26M}
E2M.values = {V27M,V28M,V29M}
E3M.values = {V30M,V31M,V32M}
E4M.values = {V33M,V34M,V35M,V36M}
E5M.values = {V37M,V38M,V39M,V40M,V41M,V42M,V43M,V44M,V45M,V46M,V47M,V48M,V49M,V50M,V51M}
E6M.values = {V52M,V53M}
E7M.values = {V54M,V55M,V56M,V57M,V58M,V59M,V60M}
E8M.values = {V61M,V62M}
M1G.fields={F1D, F2D}
F3D.message_type=M1G
M2G.fields={F3D}
M3G.fields={F4D, F5D, F6D, F7D, F8D}
M4G.fields={F9D, F10D, F11D, F12D, F13D, F14D, F15D}
M5G.fields={F16D}
F20D.message_type=M4G
F21D.message_type=M4G
F25D.message_type=M4G
F26D.message_type=M4G
M6G.fields={F17D, F18D, F19D, F20D, F21D, F22D, F23D, F24D, F25D, F26D, F27D, F28D, F29D, F30D}
M7G.fields={F31D}
M8G.fields={F32D, F33D, F34D}
M9G.fields={F35D, F36D, F37D}
M10G.fields={F38D, F39D}
M12G.fields={F40D, F41D}
M13G.fields={F42D, F43D, F44D}
M14G.fields={F45D, F46D, F47D, F48D}
M15G.fields={F49D, F50D, F51D, F52D}
F55D.enum_type=M17G
M16G.fields={F53D, F54D, F55D, F56D, F57D, F58D, F59D, F60D, F61D, F62D, F63D, F64D}
M18G.fields={F65D, F66D, F67D, F68D}
F71D.message_type=M18G
F72D.message_type=M18G
M19G.fields={F69D, F70D, F71D, F72D}
M20G.fields={F73D, F74D, F75D}
M21G.fields={F76D, F77D, F78D}
F82D.enum_type=M23G
M22G.fields={F79D, F80D, F81D, F82D, F83D, F84D, F85D}
F89D.message_type=M22G
M24G.fields={F86D, F87D, F88D, F89D}
M25G.fields={F90D, F91D, F92D}
M26G.fields={F93D}
M27G.fields={F94D}
M28G.fields={F95D, F96D, F97D}
M29G.fields={F98D, F99D, F100D}
F104D.enum_type=M23G
F105D.message_type=M31G
M30G.fields={F101D, F102D, F103D, F104D, F105D}
M31G.fields={F106D, F107D, F108D}
F110D.message_type=M31G
M32G.fields={F109D, F110D}
M33G.fields={F111D}
M34G.fields={F112D, F113D, F114D, F115D}
M35G.fields={F116D, F117D, F118D}
F119D.enum_type=M37G
F120D.message_type=M6G
F121D.message_type=M9G
F122D.message_type=M10G
F123D.message_type=M13G
F124D.message_type=M14G
F125D.message_type=M15G
F126D.message_type=M16G
F127D.message_type=M19G
F128D.message_type=M11G
F129D.message_type=M7G
F130D.message_type=M8G
F131D.message_type=M20G
F132D.message_type=M21G
F133D.message_type=M24G
F134D.message_type=M25G
F135D.message_type=M26G
F136D.message_type=M27G
F137D.message_type=M12G
F138D.message_type=M5G
F139D.message_type=M28G
F140D.message_type=M29G
F141D.message_type=M30G
F142D.message_type=M32G
F143D.message_type=M33G
F144D.message_type=M34G
F145D.message_type=M35G
M36G.fields={F119D, F120D, F121D, F122D, F123D, F124D, F125D, F126D, F127D, F128D, F129D, F130D, F131D, F132D, F133D, F134D, F135D, F136D, F137D, F138D, F139D, F140D, F141D, F142D, F143D, F144D, F145D}
F146D.message_type=M36G
M38G.fields={F146D}
M39G.fields={F147D, F148D, F149D, F150D}
M40G.fields={F151D, F152D, F153D, F154D}
M41G.fields={F155D, F156D, F157D, F158D, F159D}
M42G.fields={F160D, F161D, F162D, F163D}
M43G.fields={F164D, F165D, F166D}
F167D.message_type=M41G
M44G.fields={F167D, F168D}
M45G.fields={F169D, F170D}
F172D.message_type=M45G
M46G.fields={F171D, F172D, F173D, F174D}
M47G.fields={F175D, F176D}
F183D.message_type=M39G
F186D.message_type=M40G
F187D.message_type=M41G
F192D.message_type=M44G
F195D.message_type=M46G
F199D.message_type=M47G
M48G.fields={F177D, F178D, F179D, F180D, F181D, F182D, F183D, F184D, F185D, F186D, F187D, F188D, F189D, F190D, F191D, F192D, F193D, F194D, F195D, F196D, F197D, F198D, F199D}
M49G.fields={F200D, F201D}
F203D.message_type=M49G
M50G.fields={F202D, F203D, F204D}
M51G.fields={F205D, F206D, F207D, F208D}
F210D.message_type=M51G
M52G.fields={F209D, F210D}
F215D.message_type=M49G
F226D.message_type=M52G
M53G.fields={F211D, F212D, F213D, F214D, F215D, F216D, F217D, F218D, F219D, F220D, F221D, F222D, F223D, F224D, F225D, F226D}
M54G.fields={F227D, F228D}
F230D.message_type=M54G
M55G.fields={F229D, F230D}
M56G.fields={F231D, F232D, F233D}
M57G.fields={F234D, F235D, F236D}
F238D.message_type=M56G
M58G.fields={F237D, F238D}
F240D.message_type=M57G
M59G.fields={F239D, F240D}
M60G.fields={F241D, F242D, F243D}
F245D.message_type=M60G
M61G.fields={F244D, F245D, F246D, F247D}
F249D.enum_type=M23G
F251D.message_type=M48G
F253D.message_type=M50G
F258D.message_type=M42G
F259D.message_type=M43G
F263D.message_type=M2G
F264D.message_type=M63G
F265D.message_type=M53G
F266D.message_type=M55G
F267D.message_type=M58G
F268D.message_type=M59G
F271D.message_type=M61G
M62G.fields={F248D, F249D, F250D, F251D, F252D, F253D, F254D, F255D, F256D, F257D, F258D, F259D, F260D, F261D, F262D, F263D, F264D, F265D, F266D, F267D, F268D, F269D, F270D, F271D}
F272D.message_type=M18G
F273D.message_type=M18G
M64G.fields={F272D, F273D, F274D, F275D, F276D, F277D, F278D}
F283D.message_type=M62G
F284D.message_type=M18G
F285D.message_type=M18G
M65G.fields={F279D, F280D, F281D, F282D, F283D, F284D, F285D, F286D, F287D, F288D, F289D, F290D, F291D, F292D, F293D}
M63G.fields={F294D, F295D}
F296D.message_type=M63G
M66G.fields={F296D, F297D, F298D, F299D, F300D, F301D}
M67G.fields={F302D, F303D, F304D, F305D, F306D}
F308D.message_type=M48G
F311D.message_type=M50G
F314D.message_type=M69G
M68G.fields={F307D, F308D, F309D, F310D, F311D, F312D, F313D, F314D}
M69G.fields={F315D}
M70G.fields={F316D, F317D, F318D, F319D, F320D, F321D, F322D}
F324D.message_type=M70G
M71G.fields={F323D, F324D, F325D}
F326D.message_type=M71G
M72G.fields={F326D}
F327D.message_type=M68G
F329D.message_type=M67G
F331D.message_type=M72G
M73G.fields={F327D, F328D, F329D, F330D, F331D, F332D}
M74G.fields={F333D, F334D, F335D, F336D, F337D, F338D}
F339D.message_type=M63G
M75G.fields={F339D}

ActionType_Effect = 2
ActionType_SKill = 1
ActionType_Start = 3
AddBuffReport =M(M14G)
BuildReport =M(M6G)
ChinaRedReportData =M(M69G)
CreateSummonedReport =M(M30G)
CutsceneReport =M(M26G)
DestroyReport =M(M7G)
ETopRace =M(M67G)
EnBuildProp_ArtifactID = 18
EnBuildProp_Cure_Max_Hp = 8
EnBuildProp_DefenceVal = 16
EnBuildProp_Hp = 0
EnBuildProp_IsTrialHero = 17
EnBuildProp_Low_Hp = 6
EnBuildProp_Low_Max_Hp = 7
EnBuildProp_Lv = 2
EnBuildProp_Max_Hp = 3
EnBuildProp_Max_Mp = 4
EnBuildProp_Mp = 1
EnBuildProp_Skill1 = 9
EnBuildProp_Skill6 = 14
EnBuildProp_SkinID = 15
EnBuildProp_Star_Lv = 5
EnStandardArmorBase = 1
EnStandardArmorGrowUp = 2
ExtendedDataReport =M(M73G)
ExtendedDataTroop =M(M68G)
ExtendedDataTroopPal =M(M66G)
ExtendedDataTroopPalProp =M(M63G)
GameOverReport =M(M19G)
Left = 1
LogReport =M(M8G)
MiscReport =M(M5G)
MissReport =M(M33G)
ModifierType_Blocked = 3
ModifierType_Critical = 2
ModifierType_Missed = 5
ModifierType_Normal = 1
NumericReport =M(M16G)
NumericType_HP = 1
NumericType_MP = 2
NumericType_SP = 3
PalBatlleDamage =M(M70G)
PalDamage =M(M22G)
PalDieReport =M(M9G)
PalReliveReport =M(M20G)
PalReportContext =M(M4G)
PalTestReport =M(M24G)
RemoveBuffReport =M(M15G)
ReportDataCommon =M(M74G)
ReportType_AddBuffReport = 5
ReportType_BuildReport = 1
ReportType_CutsceneReport = 16
ReportType_DestroyReport = 10
ReportType_GameOverReport = 8
ReportType_LogReport = 11
ReportType_MiscReport = 19
ReportType_MissReport = 24
ReportType_NumericReport = 7
ReportType_PalDieReport = 2
ReportType_PalTestReport = 14
ReportType_ReliveReport = 12
ReportType_RemoveBuffReport = 6
ReportType_ResistCntReport = 26
ReportType_RoundReport = 3
ReportType_SdDmgIncrReport = 25
ReportType_ShieldReport = 20
ReportType_SkillContainerReport = 18
ReportType_SpaceDominatorReport = 21
ReportType_StartActionReport = 9
ReportType_SummonedReport = 22
ReportType_SuspendReport = 17
ReportType_UpdatePropReport = 13
ReportType_UseSkillReport = 4
ReportType_WeaponEnergyReport = 15
ReportType_summonedNumberReport = 23
ResistCntReport =M(M35G)
RewardContext =M(M18G)
Right = 2
RoundReport =M(M10G)
ShieldValueReport =M(M28G)
SkillContainerReport =M(M12G)
SoldierBoostPB =M(M3G)
SpaceDominatorDamageReport =M(M29G)
StartActionReport =M(M11G)
SuspendReport =M(M27G)
TBHeroAttributes =M(M47G)
TBSDecorateData =M(M44G)
TBWeaponDiamond =M(M46G)
TBWeaponDiamondSkill =M(M45G)
TBuffBattleInfo =M(M61G)
TBuffEffectInfo =M(M60G)
TDecorateData =M(M56G)
TDecorateInfo =M(M58G)
TDroneStarScheme =M(M52G)
TDroneStarSchemePlace =M(M51G)
TEAM_HERO_ATTACKING_ENEMY_BASE = 4
TEAM_HERO_DEFENDING_CITY = 8
TEAM_HERO_GATHERING = 1
TEAM_HERO_GUARDING = 2
TEAM_HERO_NONE = 0
TEAM_HERO_TRANSPORTING_ATTACK = 16
TEAM_HERO_TRANSPORTING_DEFENSE = 32
TFactionAdditionInfo =M(M42G)
THonorWallData =M(M57G)
THonorWallInfo =M(M59G)
TSdDmgIncrReport =M(M34G)
TSoldierNum =M(M1G)
TSoldierNumList =M(M2G)
TTechAdditionInfo =M(M43G)
TbsAdditionBuffContext =M(M39G)
TbsBattleContext =M(M65G)
TbsBattleParam =M(M64G)
TbsDroneContext =M(M53G)
TbsPalContext =M(M48G)
TbsReport =M(M36G)
TbsReports =M(M38G)
TbsScientific =M(M54G)
TbsScientificContext =M(M55G)
TbsTroopContext =M(M62G)
TbsWeaponContext =M(M50G)
TbsWeaponPart =M(M49G)
TroopBatlleDamage =M(M71G)
TroopBatlleDamageAll =M(M72G)
TroopSoldierProp =M(M75G)
UpdatePropReport =M(M21G)
UpdateSummonedNumberReport =M(M32G)
UseSkillReport =M(M13G)
WeaponEnergyReport =M(M25G)
tEquipInfo =M(M41G)
tGemstoneInfo =M(M40G)
tSummonedNumberData =M(M31G)

