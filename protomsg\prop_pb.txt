-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
local V=protobuf.EnumValueDescriptor
local F=protobuf.FieldDescriptor
local D=protobuf.Descriptor
local E=protobuf.EnumDescriptor
local M=protobuf.Message
local common_new_pb=require("common_new_pb")
module('prop_pb')


V1M=V(4,"PropType_Unknow",0,0)
V2M=V(4,"PropType_Person",1,1)
V3M=V(4,"PropType_PersonAdditional",2,2)
V4M=V(4,"PropType_PersonOffline",3,3)
E1M=E(3,"PropType",".CSMsg.PropType")
V5M=V(4,"PERSON_PROP_UNKNOW",0,0)
V6M=V(4,"PERSON_PROP_PDBID",1,1)
V7M=V(4,"PERSON_PROP_EXP",2,2)
V8M=V(4,"PERSON_PROP_LV",3,3)
V9M=V(4,"PERSON_PROP_COIN",4,4)
V10M=V(4,"PERSON_PROP_DIAMOND",5,5)
V11M=V(4,"PERSON_PROP_BIND_DIAMOND",6,6)
V12M=V(4,"PERSON_PROP_BUY_HERO_LIST_NUM",7,7)
V13M=V(4,"PERSON_PROP_FACEID",8,8)
V14M=V(4,"PERSON_PROP_FACEPROPID",9,9)
V15M=V(4,"PERSON_PROP_SEX",10,10)
V16M=V(4,"PERSON_PROP_VIP_LV",11,11)
V17M=V(4,"PERSON_PROP_VIP_EXP",12,12)
V18M=V(4,"PERSON_PROP_UDBID",13,13)
V19M=V(4,"PERSON_UNIVERSE_STAR",14,14)
V20M=V(4,"PERSON_GALAXY_STAR",15,15)
V21M=V(4,"PERSON_PROP_FRAMEID",16,16)
V22M=V(4,"PERSON_PROP_FROMWORLDID",17,17)
V23M=V(4,"PERSON_PROP_RECHARGE_RMB",18,18)
V24M=V(4,"PERSON_PROP_RECHARGE_USD",19,19)
V25M=V(4,"PERSON_PROP_VALUABLE_COIN",20,20)
V26M=V(4,"PERSON_PROP_RECHARGE_RMBREAL",21,21)
V27M=V(4,"PERSON_PROP_FREE_VIP_EXP",22,22)
V28M=V(4,"PERSON_PROP_NATIONALFLAGID",23,23)
V29M=V(4,"PERSON_PROP_VIP_EXP_UPDATE_TIME",24,24)
V30M=V(4,"PERSON_PROP_FOOD",25,25)
V31M=V(4,"PERSON_PROP_STEEL",26,26)
V32M=V(4,"PERSON_PROP_SCHLOSS",27,27)
V33M=V(4,"PERSON_PROP_VIP_EXPIRED",28,28)
V34M=V(4,"PERSON_PROP_SCHLOSS_EFFECT",29,29)
V35M=V(4,"PERSON_PROP_FOOD_OUTPUT_RATE",30,30)
V36M=V(4,"PERSON_PROP_IRON_OUTPUT_RATE",31,31)
V37M=V(4,"PERSON_PROP_GOLD_OUTPUT_RATE",32,32)
V38M=V(4,"PERSON_PROP_FOOD_SAFE_AMOUNT",33,33)
V39M=V(4,"PERSON_PROP_IRON_SAFE_AMOUNT",34,34)
V40M=V(4,"PERSON_PROP_GOLD_SAFE_AMOUNT",35,35)
V41M=V(4,"PERSON_PROP_FOOD_SAFE_INCREASE_RATE",36,36)
V42M=V(4,"PERSON_PROP_IRON_SAFE_INCREASE_RATE",37,37)
V43M=V(4,"PERSON_PROP_GOLD_SAFE_INCREASE_RATE",38,38)
V44M=V(4,"PERSON_PROP_AFK_TIME",39,39)
V45M=V(4,"PERSON_PROP_AFK_OUTPUT_RATE",40,40)
V46M=V(4,"PERSON_PROP_EQUIP_MAKE_SPEED_RATE",41,41)
V47M=V(4,"PERSON_PROP_EQUIP_COST_REDUCE_RATE",42,42)
V48M=V(4,"PERSON_PROP_FREE_ADV_RECRUIT_CD",43,43)
V49M=V(4,"PERSON_PROP_FREE_SURV_RECRUIT_CD",44,44)
V50M=V(4,"PERSON_PROP_EXTRA_BARRACKS",45,45)
V51M=V(4,"PERSON_PROP_BARRACK_LIMIT_RATE",46,46)
V52M=V(4,"PERSON_PROP_TRAINING_SPEED_RATE",47,47)
V53M=V(4,"PERSON_PROP_TRAINING_COST_REDUCE_RATE",48,48)
V54M=V(4,"PERSON_PROP_HOSPITAL_CAPACITY",49,49)
V55M=V(4,"PERSON_PROP_HOSPITAL_CAP_INCR_RATE",50,50)
V56M=V(4,"PERSON_PROP_TREATMENT_SPEED_RATE",51,51)
V57M=V(4,"PERSON_PROP_TREATMENT_COST_RATE",52,52)
V58M=V(4,"PERSON_PROP_PARADE_GROUND",53,53)
V59M=V(4,"PERSON_PROP_PARADE_CAP_INCR_RATE",54,54)
V60M=V(4,"PERSON_PROP_UNIT_HP_BONUS",55,55)
V61M=V(4,"PERSON_PROP_UNIT_ATT_BONUS",56,56)
V62M=V(4,"PERSON_PROP_UNIT_DEF_BONUS",57,57)
V63M=V(4,"PERSON_PROP_UNIT_HP_INCR_RATE",58,58)
V64M=V(4,"PERSON_PROP_UNIT_ATT_INCR_RATE",59,59)
V65M=V(4,"PERSON_PROP_UNIT_DEF_INCR_RATE",60,60)
V66M=V(4,"PERSON_PROP_UNIT_MORALE",61,61)
V67M=V(4,"PERSON_PROP_UNIT_LOAD_INCR_RATE",62,62)
V68M=V(4,"PERSON_PROP_UNLOCK_T10_SOLDIERS",63,63)
V69M=V(4,"PERSON_PROP_AUTO_DISPATCH",64,64)
V70M=V(4,"PERSON_PROP_HELP_COUNT",65,65)
V71M=V(4,"PERSON_PROP_HELP_TIME_REDUCE",66,66)
V72M=V(4,"PERSON_PROP_CONST_SPEED_RATE",67,67)
V73M=V(4,"PERSON_PROP_CONST_COST_REDUCE_RATE",68,68)
V74M=V(4,"PERSON_PROP_FREE_CONST_SPEED",69,69)
V75M=V(4,"PERSON_PROP_TECH_SPEED_RATE",70,70)
V76M=V(4,"PERSON_PROP_TECH_COST_REDUCE_RATE",71,71)
V77M=V(4,"PERSON_PROP_FREE_TECH_SPEED",72,72)
V78M=V(4,"PERSON_PROP_MARCH_SPEED_RATE",73,73)
V79M=V(4,"PERSON_PROP_MARCH_SPEED_RATE_1",74,74)
V80M=V(4,"PERSON_PROP_MARCH_SPEED_RATE_2",75,75)
V81M=V(4,"PERSON_PROP_MARCH_SPEED_RATE_3",76,76)
V82M=V(4,"PERSON_PROP_MARCH_SPEED_RATE_4",77,77)
V83M=V(4,"PERSON_PROP_ZOMBIE_MARCH_RATE",78,78)
V84M=V(4,"PERSON_PROP_ZOMBIE_MARCH_RATE_1",79,79)
V85M=V(4,"PERSON_PROP_ZOMBIE_MARCH_RATE_2",80,80)
V86M=V(4,"PERSON_PROP_ZOMBIE_MARCH_RATE_3",81,81)
V87M=V(4,"PERSON_PROP_ZOMBIE_MARCH_RATE_4",82,82)
V88M=V(4,"PERSON_PROP_RESOURCE_MARCH_RATE",83,83)
V89M=V(4,"PERSON_PROP_RESOURCE_MARCH_RATE_1",84,84)
V90M=V(4,"PERSON_PROP_RESOURCE_MARCH_RATE_2",85,85)
V91M=V(4,"PERSON_PROP_RESOURCE_MARCH_RATE_3",86,86)
V92M=V(4,"PERSON_PROP_RESOURCE_MARCH_RATE_4",87,87)
V93M=V(4,"PERSON_PROP_ENEMY_MARCH_RATE",88,88)
V94M=V(4,"PERSON_PROP_ENEMY_MARCH_RATE_1",89,89)
V95M=V(4,"PERSON_PROP_ENEMY_MARCH_RATE_2",90,90)
V96M=V(4,"PERSON_PROP_ENEMY_MARCH_RATE_3",91,91)
V97M=V(4,"PERSON_PROP_ENEMY_MARCH_RATE_4",92,92)
V98M=V(4,"PERSON_PROP_CITY_MARCH_RATE",93,93)
V99M=V(4,"PERSON_PROP_CITY_MARCH_RATE_1",94,94)
V100M=V(4,"PERSON_PROP_CITY_MARCH_RATE_2",95,95)
V101M=V(4,"PERSON_PROP_CITY_MARCH_RATE_3",96,96)
V102M=V(4,"PERSON_PROP_CITY_MARCH_RATE_4",97,97)
V103M=V(4,"PERSON_PROP_DEFENSE_MARCH_RATE",98,98)
V104M=V(4,"PERSON_PROP_DEFENSE_MARCH_RATE_1",99,99)
V105M=V(4,"PERSON_PROP_DEFENSE_MARCH_RATE_2",100,100)
V106M=V(4,"PERSON_PROP_DEFENSE_MARCH_RATE_3",101,101)
V107M=V(4,"PERSON_PROP_DEFENSE_MARCH_RATE_4",102,102)
V108M=V(4,"PERSON_PROP_GATHER_SPEED_RATE",103,103)
V109M=V(4,"PERSON_PROP_GATHER_SPEED_RATE_1",104,104)
V110M=V(4,"PERSON_PROP_GATHER_SPEED_RATE_2",105,105)
V111M=V(4,"PERSON_PROP_GATHER_SPEED_RATE_3",106,106)
V112M=V(4,"PERSON_PROP_GATHER_SPEED_RATE_4",107,107)
V113M=V(4,"PERSON_PROP_FOOD_GATHER_RATE",108,108)
V114M=V(4,"PERSON_PROP_FOOD_GATHER_RATE_1",109,109)
V115M=V(4,"PERSON_PROP_FOOD_GATHER_RATE_2",110,110)
V116M=V(4,"PERSON_PROP_FOOD_GATHER_RATE_3",111,111)
V117M=V(4,"PERSON_PROP_FOOD_GATHER_RATE_4",112,112)
V118M=V(4,"PERSON_PROP_IRON_GATHER_RATE",113,113)
V119M=V(4,"PERSON_PROP_IRON_GATHER_RATE_1",114,114)
V120M=V(4,"PERSON_PROP_IRON_GATHER_RATE_2",115,115)
V121M=V(4,"PERSON_PROP_IRON_GATHER_RATE_3",116,116)
V122M=V(4,"PERSON_PROP_IRON_GATHER_RATE_4",117,117)
V123M=V(4,"PERSON_PROP_GOLD_GATHER_RATE",118,118)
V124M=V(4,"PERSON_PROP_GOLD_GATHER_RATE_1",119,119)
V125M=V(4,"PERSON_PROP_GOLD_GATHER_RATE_2",120,120)
V126M=V(4,"PERSON_PROP_GOLD_GATHER_RATE_3",121,121)
V127M=V(4,"PERSON_PROP_GOLD_GATHER_RATE_4",122,122)
V128M=V(4,"PERSON_PROP_LOAD_BONUS_RATE",123,123)
V129M=V(4,"PERSON_PROP_LOAD_BONUS_RATE_1",124,124)
V130M=V(4,"PERSON_PROP_LOAD_BONUS_RATE_2",125,125)
V131M=V(4,"PERSON_PROP_LOAD_BONUS_RATE_3",126,126)
V132M=V(4,"PERSON_PROP_LOAD_BONUS_RATE_4",127,127)
V133M=V(4,"PERSON_PROP_CAN_NOT_ADD_SHIELD",128,128)
V134M=V(4,"PERSON_PROP_CAN_NOT_BE_ATTACKED",129,129)
V135M=V(4,"PERSON_PROP_CAN_NOT_BE_SPIED",130,130)
V136M=V(4,"PERSON_PROP_CAN_NOT_BE_GATHERED",131,131)
V137M=V(4,"PERSON_PROP_ACCELERATE_DASH_COUNT",132,132)
V138M=V(4,"PERSON_PROP_AUTO_DISPATCH_MISSION",133,133)
V139M=V(4,"PERSON_PROP_PHOTO_STICKER_UNLOCK",134,134)
V140M=V(4,"PERSON_PROP_SHAKE_COLLECT_RESOURCES",135,135)
V141M=V(4,"PERSON_PROP_BATCH_COLLECT_RESOURCES",136,136)
V142M=V(4,"PERSON_PROP_SHOW_VIP_LEVEL_IN_CHAT",137,137)
V143M=V(4,"PERSON_PROP_SUPER_MODE_STEALTH_SQUAD",138,138)
V144M=V(4,"PERSON_PROP_FAST_CHALLENGE_EXPEDITION",139,139)
V145M=V(4,"PERSON_PROP_INCREASE_TRUCK_CARRY_RATE",140,140)
V146M=V(4,"PERSON_PROP_ACCELERATE_TRUCK_SPEED_RATE",141,141)
V147M=V(4,"PERSON_PROP_EXTRA_LOOT_ON_ATTACK",142,142)
V148M=V(4,"PERSON_PROP_EXTRA_LOOT_ON_DEFEND",143,143)
V149M=V(4,"PERSON_PROP_DAILY_TRUCK_INCREASE",144,144)
V150M=V(4,"PERSON_PROP_MAX_ATTACK_FAILURE_LIMIT",145,145)
V151M=V(4,"PERSON_PROP_TRADE_CONTRACT_REINDEER",146,146)
V152M=V(4,"PERSON_PROP_RADAR_TASK_POINTS_RATE",147,147)
V153M=V(4,"PERSON_PROP_ACCELERATE_POINTS_RATE",148,148)
V154M=V(4,"PERSON_PROP_HERO_RECRUIT_POINTS_RATE",149,149)
V155M=V(4,"PERSON_PROP_UNLOCK_REWARDS_TIER_4_TO_6",150,150)
V156M=V(4,"PERSON_PROP_ALL_POINTS_INCREASE_RATE",151,151)
V157M=V(4,"PERSON_PROP_BUILDING_POWER_POINTS_RATE",152,152)
V158M=V(4,"PERSON_PROP_TECH_POWER_POINTS_RATE",153,153)
V159M=V(4,"PERSON_PROP_TRAIN_SOLDIER_POINTS_RATE",154,154)
V160M=V(4,"PERSON_PROP_KILL_ENEMY_POINTS_RATE",155,155)
V161M=V(4,"PERSON_PROP_UNLOCK_REWARDS_TIER_7_TO_9",156,156)
V162M=V(4,"PERSON_PROP_INCREASE_DISPATCH_QUEUE",157,157)
V163M=V(4,"PERSON_PROP_INCREASE_SUPPLY_BOX_OUTPUT",158,158)
V164M=V(4,"PERSON_PROP_RADAR_MISSION_TREASURE_BOX",159,159)
V165M=V(4,"PERSON_PROP_MAX_SOLDIERS_INCR_NUM",160,160)
V166M=V(4,"PERSON_PROP_POWER",161,161)
V167M=V(4,"PERSON_PROP_LASTONLINETIME",162,162)
V168M=V(4,"PERSON_PROP_EXTRA_FARM",163,163)
V169M=V(4,"PERSON_PROP_EXTRA_IRON",164,164)
V170M=V(4,"PERSON_PROP_EXTRA_GOLD",165,165)
V171M=V(4,"PERSON_PROP_POSITIONID",166,166)
V172M=V(4,"PERSON_PROP_PLATE",167,167)
V173M=V(4,"PERSON_PROP_MAXID",168,168)
E2M=E(3,"PersonProp",".CSMsg.PersonProp")
V174M=V(4,"PERSON_ADDPROP_UNKNOW",0,0)
V175M=V(4,"PERSON_ADDPROP_NAME",1,1)
V176M=V(4,"PERSON_ALLIANCE_ID",2,2)
V177M=V(4,"PERSON_ALLIANCE_NAME",3,3)
V178M=V(4,"PERSON_ALLIANCE_SHORTNAME",4,4)
V179M=V(4,"PERSON_ALLIANCE_AUTH",5,5)
V180M=V(4,"PERSON_ALLIANCE_POST",6,6)
V181M=V(4,"PERSON_ALLIANCE_CREATETIME",7,7)
V182M=V(4,"PERSON_ALLIANCE_ADDTIME",8,8)
V183M=V(4,"PERSON_ALLIANCE_FLAG",9,9)
V184M=V(4,"PERSON_ALLIANCE_EXITTIME",10,10)
V185M=V(4,"PERSON_ALLIANCE_FIRST_JOIN_TIME",11,11)
V186M=V(4,"PERSON_ADDPROP_FACESTR",12,12)
V187M=V(4,"PERSON_ADDPROP_MODIFYNAME_TIME",13,13)
V188M=V(4,"PERSON_ADDPROP_CITY_WALL_LEVEL",14,20)
V189M=V(4,"PERSON_ADDPROP_CITY_HOSPITAL_CAPACITY",15,21)
V190M=V(4,"PERSON_ADDPROP_CITY_MAIN_LEVEL",16,22)
V191M=V(4,"PERSON_ADDPROP_CITY_FOOD_PROTECT",17,23)
V192M=V(4,"PERSON_ADDPROP_CITY_IRON_PROTECT",18,24)
V193M=V(4,"PERSON_ADDPROP_CITY_GOLD_PROTECT",19,25)
V194M=V(4,"PERSON_ADDPROP_CITY_CAN_RECRUIT_SOLDIER_MAX_LEVEL",20,26)
V195M=V(4,"PERSON_ADDPROP_SCHLOSS",21,27)
V196M=V(4,"PERSON_ADDPROP_CARRIAGE_BASERATE",22,28)
V197M=V(4,"PERSON_ADDPROP_CARRIAGE_EXRECAS",23,29)
V198M=V(4,"PERSON_ADDPROP_CARRIAGE_EXLOOTS",24,30)
V199M=V(4,"PERSON_ADDPROP_CARRIAGE_EXTRADES",25,31)
V200M=V(4,"PERSON_ADDPROP_CARRIAGE_SUPERDEF",26,32)
V201M=V(4,"PERSON_ADDPROP_CARRIAGE_REINDEER",27,33)
V202M=V(4,"PERSON_ADDPROP_CITY_TRAINNINGCENTER_CAPACITY",28,34)
V203M=V(4,"PERSON_ADDPROP_CITY_ACORNPUB_CAN_SEND_TASK_COUNT",29,35)
V204M=V(4,"PERSON_ADDPROP_SCHLOSS_EFFECT",30,36)
V205M=V(4,"PERSON_ALIANCE_TASK_FINISH",31,50)
V206M=V(4,"PERSON_BATTLE_POINT",32,51)
V207M=V(4,"PERSON_ALLIANCE_TASK_PLAY_ATY_STATUS",33,52)
V208M=V(4,"PERSON_ALLIANCE_TASK_PLAY_ATY_TIME",34,53)
V209M=V(4,"PERSON_ZONE_BATTLE_SCORE_NUM",35,54)
V210M=V(4,"PERSON_PROP_ALLIANCEDUEL_BATTLE_POINT",36,55)
V211M=V(4,"PERSON_PROP_BATTLE_CROSS_RELOCATE",37,56)
V212M=V(4,"PERSON_ADDPROP_PLATE",38,57)
E3M=E(3,"PersonAdditionalProp",".CSMsg.PersonAdditionalProp")
V213M=V(4,"PERSON_OFFLINE_PROP_UNKNOW",0,0)
V214M=V(4,"PERSON_OFFLINE_PROP_COIN",1,1)
V215M=V(4,"PERSON_OFFLINE_PROP_FOOD",2,2)
V216M=V(4,"PERSON_OFFLINE_PROP_STEEL",3,3)
V217M=V(4,"PERSON_OFFLINE_PROP_STAMINA",4,4)
V218M=V(4,"PERSON_OFFLINE_PROP_SANDBOXID",5,5)
V219M=V(4,"PERSON_OFFLINE_PROP_KILLNUM",6,6)
V220M=V(4,"PERSON_OFFLINE_PROP_CROSS_STATUS",7,7)
V221M=V(4,"PERSON_OFFLINE_PROP_CITY_CUR_DEFEND_VALUE",8,8)
V222M=V(4,"PERSON_OFFLINE_PROP_CITY_WALL_BEGINTIME",9,9)
V223M=V(4,"PERSON_OFFLINE_PROP_CITY_WALL_FIRE_ENDTIME",10,10)
V224M=V(4,"PERSON_OFFLINE_PROP_INCITY_SOLDIER_LEVEL1",11,11)
V225M=V(4,"PERSON_OFFLINE_PROP_INCITY_SOLDIER_MAX",12,30)
V226M=V(4,"PERSON_OFFLINE_PROP_OUTCITY_SOLDIER_LEVEL1",13,31)
V227M=V(4,"PERSON_OFFLINE_PROP_OUTCITY_SOLDIER_MAX",14,50)
V228M=V(4,"PERSON_OFFLINE_PROP_INJURED_SOLDIER_LEVEL1",15,51)
V229M=V(4,"PERSON_OFFLINE_PROP_INJURED_SOLDIER_MAX",16,70)
V230M=V(4,"PERSON_OFFLINE_PROP_PLUNDER_DAY_NUM",17,71)
V231M=V(4,"PERSON_OFFLINE_PROP_PLUNDER_DAY_TIME",18,72)
V232M=V(4,"PERSON_OFFLINE_PROP_NC_OA_COUNT",19,73)
V233M=V(4,"PERSON_OFFLINE_PROP_NC_OA_TIME",20,74)
V234M=V(4,"PERSON_PROP_LINUP_POWER_1",21,75)
V235M=V(4,"PERSON_OFFLINE_PROP_GATHERING_ID",22,76)
V236M=V(4,"PERSON_OFFLINE_PROP_GATHERING_ENDTIME",23,77)
V237M=V(4,"PERSON_OFFLINE_PROP_IS_OFFLINE_BROKEN_CITY",24,78)
V238M=V(4,"PERSON_OFFLINE_PROP_OFFLINE_BROKEN_CITY_TIME",25,79)
V239M=V(4,"PERSON_OFFLINE_PROP_WORLDID",26,80)
V240M=V(4,"PERSON_OFFLINE_PROP_UNLOCK_TROOP",27,81)
V241M=V(4,"PERSON_OFFLINE_PROP_GATHERING_TermID",28,82)
V242M=V(4,"PERSON_OFFLINE_PROP_GATHERING_State",29,83)
V243M=V(4,"PERSON_OFFLINE_PROP_GATHERING_Cnt",30,84)
V244M=V(4,"PERSON_OFFLINE_PROP_GATHERING_LastMassTime",31,85)
V245M=V(4,"PERSON_OFFLINE_PROP_GATHERING_JoinMassCnt",32,86)
V246M=V(4,"PERSON_OFFLINE_PROP_GATHERING_LastJoinMassTime",33,87)
V247M=V(4,"PERSON_OFFLINE_PROP_TOTAL_FIVE_PAL_MAX_TROOP",34,88)
V248M=V(4,"PERSON_OFFLINE_PROP_ALLIANCEBOSS_REWARDINFO",35,89)
V249M=V(4,"PERSON_OFFLINE_PROP_IS_OFFLINE_BROKENCITY_EVENT",36,90)
V250M=V(4,"PERSON_OFFLINE_PROP_UNLOCK_TRUCK",37,91)
V251M=V(4,"PERSON_OFFLINE_PROP_OFFLINE_BROKEN_GET_REWARD_TIME",38,92)
V252M=V(4,"PERSON_OFFLINE_PROP_ACORNPUB_TREASURE_FLAG",39,93)
V253M=V(4,"PERSON_OFFLINE_PROP_CITY_POS",40,94)
V254M=V(4,"PERSON_OFFLINE_PROP_ISGET_REWARD_ACHIEVEMENT_1",41,100)
V255M=V(4,"PERSON_OFFLINE_PROP_ISGET_REWARD_ACHIEVEMENT_MAX",42,149)
V256M=V(4,"PERSON_OFFLINE_PROP_GET_REWARD_TIME_ACHIEVEMENT_1",43,150)
V257M=V(4,"PERSON_OFFLINE_PROP_GET_REWARD_TIME_ACHIEVEMENT_MAX",44,199)
V258M=V(4,"PERSON_OFFLINE_PROP_CITY_MIN_TYPE_LEVEL",45,200)
V259M=V(4,"PERSON_OFFLINE_PROP_CITY_MAX_TYPE_LEVEL",46,249)
V260M=V(4,"PERSON_OFFLINE_PROP_AUTO_SEND_MAIL_ISSEND_1",47,301)
V261M=V(4,"PERSON_OFFLINE_PROP_AUTO_SEND_MAIL_ISSEND_MAX",48,400)
V262M=V(4,"PERSON_OFFLINE_PROP_AUTO_SEND_MAIL_LASTTIME_1",49,401)
V263M=V(4,"PERSON_OFFLINE_PROP_AUTO_SEND_MAIL_LASTTIME_MAX",50,500)
V264M=V(4,"PERSON_OFFLINE_PROP_POSITIONID",51,501)
V265M=V(4,"PERSON_OFFLINE_PROP_TROOP_MAX_POWER",52,502)
V266M=V(4,"PERSON_OFFLINE_PROP_CHANNELID",53,503)
V267M=V(4,"PERSON_OFFLINE_PROP_ACCOUNTID",54,504)
V268M=V(4,"PERSON_OFFLINE_PROP_CREATE_ROLE_TIME",55,505)
V269M=V(4,"PERSON_OFFLINE_PROP_PASS_MAX_STAGE",56,506)
V270M=V(4,"PERSON_OFFLINE_PROP_UUID",57,507)
V271M=V(4,"PERSON_OFFLINE_PROP_DEVICE_LANGUAGE",58,508)
V272M=V(4,"PERSON_OFFLINE_PROP_PUSHMSG_MAINBUTTON",59,509)
V273M=V(4,"PERSON_OFFLINE_PROP_PUSHMSG_BUTTON_1",60,510)
V274M=V(4,"PERSON_OFFLINE_PROP_PUSHMSG_BUTTON_2",61,511)
V275M=V(4,"PERSON_OFFLINE_PROP_PUSHMSG_ISSEND_1",62,512)
V276M=V(4,"PERSON_OFFLINE_PROP_PUSHMSG_ISSEND_2",63,513)
V277M=V(4,"PERSON_OFFLINE_PROP_PUSHMSG_ISFIRST",64,514)
V278M=V(4,"PERSON_OFFLINE_PROP_GAME_LANGUAGE",65,515)
V279M=V(4,"PERSON_OFFLINE_PROP_ZONEBATTLEDUEL_WIN_REWARD",66,516)
V280M=V(4,"PERSON_OFFLINE_PROP_ZONEBATTLEDUEL_NOMATCH_REWARD",67,517)
V281M=V(4,"PERSON_OFFLINE_PROP_USERID",68,518)
V282M=V(4,"PERSON_OFFLINE_PROP_AREAID",69,519)
V283M=V(4,"PERSON_OFFLINE_PROP_BUILDING_VISITOR_LASTSENDTIME_1",70,520)
V284M=V(4,"PERSON_OFFLINE_PROP_TIME_ZONE",71,521)
V285M=V(4,"PERSON_OFFLINE_PROP_CHANGEBAG_RECVERSION",72,522)
V286M=V(4,"PERSON_OFFLINE_PROP_CHANGEBAG_REWARDVERSION",73,523)
V287M=V(4,"PERSON_OFFLINE_PROP_OUTFIRE_CNT",74,524)
V288M=V(4,"PERSON_OFFLINE_PROP_OUTFIRE_TIME",75,525)
V289M=V(4,"PERSON_OFFLINE_PROP_BUFF_LIST",76,526)
V290M=V(4,"PERSON_OFFLINE_PROP_BUFF_LAYERS",77,527)
V291M=V(4,"PERSON_OFFLINE_PROP_BUFF_ENDTIME",78,528)
V292M=V(4,"PERSON_OFFLINE_PROP_R4R5TODO_REWARD",79,529)
V293M=V(4,"PERSON_OFFLINE_PROP_R4R5TODO_TIME",80,530)
V294M=V(4,"PERSON_OFFLINE_PROP_SDKUSERID",81,531)
V295M=V(4,"PERSON_OFFLINE_PROP_UNLOCKHONORWALL",82,532)
V296M=V(4,"PERSON_OFFLINE_PROP_COUNTRY_ID",83,533)
V297M=V(4,"PERSON_OFFLINE_PROP_SKD_CHANNEL",84,534)
V298M=V(4,"PERSON_OFFLINE_PROP_NATIONAL_FALG",85,535)
V299M=V(4,"PERSON_OFFLINE_PROP_NATIONAL_FALG_TIME",86,536)
V300M=V(4,"PERSON_OFFLINE_PROP_MAX",87,537)
E4M=E(3,"PersonOfflineProp",".CSMsg.PersonOfflineProp")
V301M=V(4,"PAL_PROP_UNKNOW",0,0)
V302M=V(4,"PAL_PROP_ID",1,1)
V303M=V(4,"PAL_PROP_SID",2,2)
V304M=V(4,"PAL_PROP_LV",3,3)
V305M=V(4,"PAL_PROP_STEP_LV",4,4)
V306M=V(4,"PAL_PROP_SKILL_POINT",5,5)
V307M=V(4,"PAL_PROP_SKILL_1",6,6)
V308M=V(4,"PAL_PROP_SKILL_2",7,7)
V309M=V(4,"PAL_PROP_SKILL_3",8,8)
V310M=V(4,"PAL_PROP_SKILL_4",9,9)
V311M=V(4,"PAL_PROP_SKILL_LV_1",10,10)
V312M=V(4,"PAL_PROP_SKILL_LV_2",11,11)
V313M=V(4,"PAL_PROP_SKILL_LV_3",12,12)
V314M=V(4,"PAL_PROP_SKILL_LV_4",13,13)
V315M=V(4,"PAL_PROP_EQUIP_SKEPID",14,14)
V316M=V(4,"PAL_PROP_SKILL_SKEPID",15,15)
V317M=V(4,"PAL_PROP_STAR_LV",16,16)
V318M=V(4,"PAL_PROP_OCCUPATION",17,17)
V319M=V(4,"PAL_PROP_REMAIN_HP",18,18)
V320M=V(4,"PAL_PROP_SYNC_MAXID",19,19)
V321M=V(4,"PAL_PROP_POWER",20,20)
V322M=V(4,"PAL_PROP_BASE_HP",21,21)
V323M=V(4,"PAL_PROP_BASE_ATTACK",22,22)
V324M=V(4,"PAL_PROP_BASE_DEFENCE",23,23)
V325M=V(4,"PAL_PROP_BASE_SPEED",24,24)
V326M=V(4,"PAL_PROP_BASE_FATAL_RATE",25,25)
V327M=V(4,"PAL_PROP_BASE_FATAL_SCALE",26,26)
V328M=V(4,"PAL_PROP_HP",27,27)
V329M=V(4,"PAL_PROP_ATTACK",28,28)
V330M=V(4,"PAL_PROP_DEFENCE",29,29)
V331M=V(4,"PAL_PROP_SPEED",30,30)
V332M=V(4,"PAL_PROP_FATAL_RATE",31,31)
V333M=V(4,"PAL_PROP_FATAL_SCALE",32,32)
V334M=V(4,"PAL_PROP_EFFECT_HIT_RATE",33,33)
V335M=V(4,"PAL_PROP_EFFECT_RESIST_RATE",34,34)
V336M=V(4,"PAL_PROP_HIT_RATE",35,35)
V337M=V(4,"PAL_PROP_DODGE_RATE",36,36)
V338M=V(4,"PAL_PROP_PIERCE_RATE",37,37)
V339M=V(4,"PAL_PROP_BLOCK_RATE",38,38)
V340M=V(4,"PAL_PROP_BREAK_RATE",39,39)
V341M=V(4,"PAL_PROP_IMMUNE_RATE",40,40)
V342M=V(4,"PAL_PROP_HP_RATE",41,41)
V343M=V(4,"PAL_PROP_ATTACK_RATE",42,42)
V344M=V(4,"PAL_PROP_DEFENCE_RATE",43,43)
V345M=V(4,"PAL_PROP_SPEED_RATE",44,44)
V346M=V(4,"PAL_PROP_SKILL_DAMAGE_RATE",45,45)
V347M=V(4,"PAL_PROP_BASE_ENERGY",46,46)
V348M=V(4,"PAL_PROP_HOLY_DAMAGE_RATE",47,47)
V349M=V(4,"PAL_PROP_GLOBAL_HP_RATE",48,48)
V350M=V(4,"PAL_PROP_GLOBAL_ATTACK_RATE",49,49)
V351M=V(4,"PAL_PROP_GLOBAL_DEFENCE_RATE",50,50)
V352M=V(4,"PAL_PROP_GLOBAL_SPEED_RATE",51,51)
V353M=V(4,"PAL_PROP_EXTRA_SKILL_POINTS",52,52)
V354M=V(4,"PAL_PROP_ZS_DAMEGE_RATE",53,53)
V355M=V(4,"PAL_PROP_FS_DAMEGE_RATE",54,54)
V356M=V(4,"PAL_PROP_YX_DAMEGE_RATE",55,55)
V357M=V(4,"PAL_PROP_CK_DAMEGE_RATE",56,56)
V358M=V(4,"PAL_PROP_MS_DAMEGE_RATE",57,57)
V359M=V(4,"PAL_PROP_BASE_HP_RATE",58,58)
V360M=V(4,"PAL_PROP_BASE_ATTACK_RATE",59,59)
V361M=V(4,"PAL_PROP_BASE_DEFENCE_RATE",60,60)
V362M=V(4,"PAL_PROP_BASE_SPEED_RATE",61,61)
V363M=V(4,"PAL_PROP_FATAL_REDUCE_RATE",62,62)
V364M=V(4,"PAL_PROP_DL_DAMEGE_RATE",63,63)
V365M=V(4,"PAL_PROP_CR_DAMEGE_RATE",64,64)
V366M=V(4,"PAL_PROP_KJ_DAMEGE_RATE",65,65)
V367M=V(4,"PAL_PROP_ZR_DAMEGE_RATE",66,66)
V368M=V(4,"PAL_PROP_YZ_DAMEGE_RATE",67,67)
V369M=V(4,"PAL_PROP_SM_DAMEGE_RATE",68,68)
V370M=V(4,"PAL_PROP_TS_DAMEGE_RATE",69,69)
V371M=V(4,"PAL_PROP_TS_DAMEGE_FIX_RATE",70,70)
V372M=V(4,"PAL_PROP_SKILL_EX",71,71)
V373M=V(4,"PAL_PROP_SKILL_EX_MAX",72,76)
V374M=V(4,"PAL_PROP_SKILL_LV_EX",73,77)
V375M=V(4,"PAL_PROP_SKILL_LV_EX_MAX",74,82)
V376M=V(4,"PAL_PROP_EXTEND_STABLE_HP",75,83)
V377M=V(4,"PAL_PROP_EXTEND_STABLE_ATTACK",76,84)
V378M=V(4,"PAL_PROP_EXTEND_STABLE_DEFENCE",77,85)
V379M=V(4,"PAL_PROP_EXTEND_STABLE_SPEED",78,86)
V380M=V(4,"PAL_PROP_EXTEND_STABLE_AUX_HP",79,87)
V381M=V(4,"PAL_PROP_EXTEND_STABLE_AUX_ATTACK",80,88)
V382M=V(4,"PAL_PROP_EXTEND_STABLE_AUX_DEFENCE",81,89)
V383M=V(4,"PAL_PROP_EXTEND_STABLE_AUX_SPEED",82,90)
V384M=V(4,"PAL_PROP_CURE_HP_MAX",83,91)
V385M=V(4,"PAL_PROP_SKILL_EX2",84,92)
V386M=V(4,"PAL_PROP_SKILL_EX2_MAX",85,99)
V387M=V(4,"PAL_PROP_SKILL_LV_EX2",86,100)
V388M=V(4,"PAL_PROP_SKILL_LV_EX2_MAX",87,107)
V389M=V(4,"PAL_PROP_FATAL_DEFEND",88,108)
V390M=V(4,"PAL_PROP_FATAL_SCALE_DEFEND",89,109)
V391M=V(4,"PAL_PROP_POWER_DEFEND",90,110)
V392M=V(4,"PAL_PROP_POWER_ATTACK",91,111)
V393M=V(4,"PAL_PROP_SIGIL_SKEPID",92,112)
V394M=V(4,"PAL_PROP_AWAKE_SKILLID",93,113)
V395M=V(4,"PAL_PROP_AWAKE_SKILLLV",94,114)
V396M=V(4,"PAL_PROP_DECO_SKEPID",95,115)
V397M=V(4,"PAL_PROP_BLESSING_LV1",96,116)
V398M=V(4,"PAL_PROP_BLESSING_LV2",97,117)
V399M=V(4,"PAL_PROP_BLESSING_LV3",98,118)
V400M=V(4,"PAL_PROP_BLESSING_LV4",99,119)
V401M=V(4,"PAL_PROP_BLESSING_LV5",100,120)
V402M=V(4,"PAL_PROP_BLESSING_LV6",101,121)
V403M=V(4,"PAL_PROP_BLESSING_LV7",102,122)
V404M=V(4,"PAL_PROP_BLESSING_LV8",103,123)
V405M=V(4,"PAL_PROP_BLESSING_LV9",104,124)
V406M=V(4,"PAL_PROP_SKILL_5",105,125)
V407M=V(4,"PAL_PROP_SKILL_LV_5",106,126)
V408M=V(4,"PAL_PROP_BREAK_TREND",107,127)
V409M=V(4,"PAL_PROP_PARRY",108,128)
V410M=V(4,"PAL_PROP_STARDIAMOND_SKEPID",109,129)
V411M=V(4,"PAL_PROP_WEAPONDIAMOND_SKILL_1",110,130)
V412M=V(4,"PAL_PROP_WEAPONDIAMOND_SKILL_2",111,131)
V413M=V(4,"PAL_PROP_WEAPONDIAMOND_SKILL_3",112,132)
V414M=V(4,"PAL_PROP_WEAPONDIAMOND_SKILLLV_1",113,133)
V415M=V(4,"PAL_PROP_WEAPONDIAMOND_SKILLLV_2",114,134)
V416M=V(4,"PAL_PROP_WEAPONDIAMOND_SKILLLV_3",115,135)
V417M=V(4,"PAL_PROP_WEAPONDIAMOND_MAXLEVEL",116,136)
V418M=V(4,"PAL_PROP_WEAPONDIAMOND_CURLEVEL",117,137)
V419M=V(4,"PAL_PROP_SKILL_POWER",118,138)
V420M=V(4,"PAL_PROP_EQUIP_POWER",119,139)
V421M=V(4,"PAL_PROP_HONORWALL",120,140)
V422M=V(4,"PAL_PROP_SOLDIERS_POWER",121,141)
V423M=V(4,"PAL_PROP_MAXID",122,142)
E5M=E(3,"PalProp",".CSMsg.PalProp")
V424M=V(4,"enPro_HP",0,1)
V425M=V(4,"enPro_Attack",1,2)
V426M=V(4,"enPro_Defend",2,3)
V427M=V(4,"enPro_Speed",3,4)
V428M=V(4,"enPro_Fatal",4,5)
V429M=V(4,"enPro_Fatal_Scale",5,6)
V430M=V(4,"enPro_Effect_Hit",6,7)
V431M=V(4,"enPro_Effect_Resist",7,8)
V432M=V(4,"enPro_Hit",8,9)
V433M=V(4,"enPro_Dodge",9,10)
V434M=V(4,"enPro_Pierce",10,11)
V435M=V(4,"enPro_Block",11,12)
V436M=V(4,"enPro_Break",12,13)
V437M=V(4,"enPro_Immune",13,14)
V438M=V(4,"enPro_HP_Rate",14,15)
V439M=V(4,"enPro_Attack_Rate",15,16)
V440M=V(4,"enPro_Defend_Rate",16,17)
V441M=V(4,"enPro_Speed_Rate",17,18)
V442M=V(4,"enPro_Skill_Damage_Rate",18,19)
V443M=V(4,"enPro_Energy",19,20)
V444M=V(4,"enPro_Holy_Damage_Rate",20,21)
V445M=V(4,"enPro_Global_HP_Rate",21,22)
V446M=V(4,"enPro_Global_Attack_Rate",22,23)
V447M=V(4,"enPro_Global_Defend_Rate",23,24)
V448M=V(4,"enPro_Global_Speed_Rate",24,25)
V449M=V(4,"enPro_Extra_Skill_Points_Rate",25,26)
V450M=V(4,"enPro_ZS_Damage_Rate",26,27)
V451M=V(4,"enPro_FS_Damage_Rate",27,28)
V452M=V(4,"enPro_YX_Damage_Rate",28,29)
V453M=V(4,"enPro_CK_Damage_Rate",29,30)
V454M=V(4,"enPro_MS_Damage_Rate",30,31)
V455M=V(4,"enPro_Base_HP_Rate",31,32)
V456M=V(4,"enPro_Base_Attack_Rate",32,33)
V457M=V(4,"enPro_Base_Defend_Rate",33,34)
V458M=V(4,"enPro_Base_Speed_Rate",34,35)
V459M=V(4,"enPro_Fatal_Reduce_Rate",35,36)
V460M=V(4,"enPro_DL_Damege_Rate",36,37)
V461M=V(4,"enPro_CR_Damege_Rate",37,38)
V462M=V(4,"enPro_KJ_Damege_Rate",38,39)
V463M=V(4,"enPro_ZR_Damege_Rate",39,40)
V464M=V(4,"enPro_YZ_Damege_Rate",40,41)
V465M=V(4,"enPro_SM_Damege_Rate",41,42)
V466M=V(4,"enPro_TS_Damege_Rate",42,43)
V467M=V(4,"enPro_TS_Damege_Fix_Rate",43,44)
V468M=V(4,"enPro_Fatal_Defend",44,45)
V469M=V(4,"enPro_Fatal_Scale_Defend",45,46)
V470M=V(4,"enPro_Power_Defend",46,47)
V471M=V(4,"enPro_Power_Attack",47,48)
V472M=V(4,"enPro_Ex_Hp",48,49)
V473M=V(4,"enPro_Ex_Attack",49,50)
V474M=V(4,"enPro_Ex_Defence",50,51)
V475M=V(4,"enPro_Ex_Speed",51,52)
V476M=V(4,"enPro_Break_Trend",52,53)
V477M=V(4,"enPro_Parry",53,54)
E6M=E(3,"tBattleProType",".CSMsg.tBattleProType")
V478M=V(4,"enProdPro_Food_Output_Rate",0,301)
V479M=V(4,"enProdPro_Iron_Output_Rate",1,302)
V480M=V(4,"enProdPro_Gold_Output_Rate",2,303)
V481M=V(4,"enProdPro_Food_Safe_Increase_Rate",3,304)
V482M=V(4,"enProdPro_Iron_Safe_Increase_Rate",4,305)
V483M=V(4,"enProdPro_Gold_Safe_Increase_Rate",5,306)
V484M=V(4,"enProdPro_AFK_Time",6,307)
V485M=V(4,"enProdPro_AFK_Output_Rate",7,308)
V486M=V(4,"enProdPro_Equip_Make_Speed_Rate",8,309)
V487M=V(4,"enProdPro_Equip_Cost_Reduce_Rate",9,310)
V488M=V(4,"enProdPro_Free_Adv_Recruit_CD",10,311)
V489M=V(4,"enProdPro_Free_Surv_Recruit_CD",11,312)
V490M=V(4,"enProdPro_Extra_Barracks",12,313)
V491M=V(4,"enProdPro_Food_Safe_Amount",13,314)
V492M=V(4,"enProdPro_Iron_Safe_Amount",14,315)
V493M=V(4,"enProdPro_Gold_Safe_Amount",15,316)
V494M=V(4,"enProdPro_Extra_Farm",16,317)
V495M=V(4,"enProdPro_Extra_Iron",17,318)
V496M=V(4,"enProdPro_Extra_Gold",18,319)
V497M=V(4,"enProdPro_Barrack_Limit_Rate",19,401)
V498M=V(4,"enProdPro_Training_Speed_Rate",20,402)
V499M=V(4,"enProdPro_Training_Cost_Reduce_Rate",21,403)
V500M=V(4,"enProdPro_Hospital_Capacity",22,404)
V501M=V(4,"enProdPro_Hospital_Cap_Incr_Rate",23,405)
V502M=V(4,"enProdPro_Treatment_Speed_Rate",24,406)
V503M=V(4,"enProdPro_Treatment_Cost_Rate",25,407)
V504M=V(4,"enProdPro_Parade_Ground",26,408)
V505M=V(4,"enProdPro_Parade_Cap_Incr_Rate",27,409)
V506M=V(4,"enProdPro_Unit_HP_Bonus",28,410)
V507M=V(4,"enProdPro_Unit_Att_Bonus",29,411)
V508M=V(4,"enProdPro_Unit_Def_Bonus",30,412)
V509M=V(4,"enProdPro_Unit_HP_Incr_Rate",31,413)
V510M=V(4,"enProdPro_Unit_Att_Incr_Rate",32,414)
V511M=V(4,"enProdPro_Unit_Def_Incr_Rate",33,415)
V512M=V(4,"enProdPro_Unit_Morale",34,416)
V513M=V(4,"enProdPro_Unit_Load_Incr_Rate",35,417)
V514M=V(4,"enProdPro_Unlock_T10_Soldiers",36,418)
V515M=V(4,"enProdPro_Max_Soldiers_Incr_Num",37,419)
V516M=V(4,"enProdPro_Auto_Dispatch",38,501)
V517M=V(4,"enProdPro_Help_Count",39,502)
V518M=V(4,"enProdPro_Help_Time_Reduce",40,503)
V519M=V(4,"enProdPro_Const_Speed_Rate",41,504)
V520M=V(4,"enProdPro_Const_Cost_Reduce_Rate",42,505)
V521M=V(4,"enProdPro_Free_Const_Speed",43,506)
V522M=V(4,"enProdPro_Tech_Speed_Rate",44,507)
V523M=V(4,"enProdPro_Tech_Cost_Reduce_Rate",45,508)
V524M=V(4,"enProdPro_Free_Tech_Speed",46,509)
V525M=V(4,"enProdPro_March_Speed_Rate",47,510)
V526M=V(4,"enProdPro_Zombie_March_Rate",48,511)
V527M=V(4,"enProdPro_Resource_March_Rate",49,512)
V528M=V(4,"enProdPro_Enemy_March_Rate",50,513)
V529M=V(4,"enProdPro_City_March_Rate",51,514)
V530M=V(4,"enProdPro_Defense_March_Rate",52,515)
V531M=V(4,"enProdPro_Gather_Speed_Rate",53,516)
V532M=V(4,"enProdPro_Food_Gather_Rate",54,517)
V533M=V(4,"enProdPro_Iron_Gather_Rate",55,518)
V534M=V(4,"enProdPro_Gold_Gather_Rate",56,519)
V535M=V(4,"enProdPro_Load_Bonus_Rate",57,520)
V536M=V(4,"enProdPro_Can_Not_Add_Shield",58,521)
V537M=V(4,"enProdPro_Can_Not_Be_Attacked",59,522)
V538M=V(4,"enProdPro_Can_Not_Be_Spied",60,523)
V539M=V(4,"enProdPro_Can_Not_Be_Gathered",61,524)
V540M=V(4,"enProdPro_Accelerate_Dash_Count",62,601)
V541M=V(4,"enProdPro_Auto_Dispatch_Mission",63,602)
V542M=V(4,"enProdPro_Photo_Sticker_Unlock",64,603)
V543M=V(4,"enProdPro_Shake_Collect_Resources",65,604)
V544M=V(4,"enProdPro_Batch_Collect_Resources",66,605)
V545M=V(4,"enProdPro_Show_VIP_Level_In_Chat",67,606)
V546M=V(4,"enProdPro_Super_Mode_Stealth_Squad",68,607)
V547M=V(4,"enProdPro_Fast_Challenge_Expedition",69,608)
V548M=V(4,"enProdPro_Increase_Truck_Carry_Rate",70,701)
V549M=V(4,"enProdPro_Accelerate_Truck_Speed_Rate",71,702)
V550M=V(4,"enProdPro_Extra_Loot_On_Attack",72,703)
V551M=V(4,"enProdPro_Extra_Loot_On_Defend",73,704)
V552M=V(4,"enProdPro_Daily_Truck_Increase",74,705)
V553M=V(4,"enProdPro_Max_Attack_Failure_Limit",75,706)
V554M=V(4,"enProdPro_Trade_Contract_Reindeer",76,707)
V555M=V(4,"enProdPro_Radar_Task_Points_Rate",77,708)
V556M=V(4,"enProdPro_Accelerate_Points_Rate",78,709)
V557M=V(4,"enProdPro_Hero_Recruit_Points_Rate",79,710)
V558M=V(4,"enProdPro_Unlock_Rewards_Tier_4_to_6",80,711)
V559M=V(4,"enProdPro_All_Points_Increase_Rate",81,712)
V560M=V(4,"enProdPro_Building_Power_Points_Rate",82,713)
V561M=V(4,"enProdPro_Tech_Power_Points_Rate",83,714)
V562M=V(4,"enProdPro_Train_Soldier_Points_Rate",84,715)
V563M=V(4,"enProdPro_Kill_Enemy_Points_Rate",85,716)
V564M=V(4,"enProdPro_Unlock_Rewards_Tier_7_to_9",86,717)
V565M=V(4,"enProdPro_Increase_Dispatch_Queue",87,718)
V566M=V(4,"enProdPro_Increase_Supply_Box_Output",88,719)
V567M=V(4,"enProdPro_Radar_Mission_Treasure_Box",89,720)
V568M=V(4,"enProdPro_Radar_Mission_Treasure_Box_Max",90,721)
V569M=V(4,"enProdPro_Radar_Mission_Treasure_Box_Id",91,722)
V570M=V(4,"enProdPro_DesertStromm_MoveCity_Speed_Rate",92,1001)
V571M=V(4,"enProdPro_DesertStromm_Score_Speed_Rate",93,1002)
V572M=V(4,"enProdPro_DesertStromm_Auto_Cure_Soilder",94,1003)
E7M=E(3,"tProductionProType",".CSMsg.tProductionProType")
V573M=V(4,"enFuncPro_Carriage_BaseRotio",0,1000)
V574M=V(4,"enFuncPro_Max",1,1100)
E8M=E(3,"tFunctionProType",".CSMsg.tFunctionProType")
V575M=V(4,"GOODS_PROP_UNKNOW",0,0)
V576M=V(4,"GOODS_PROP_ID",1,1)
V577M=V(4,"GOODS_PROP_SID",2,2)
V578M=V(4,"GOODS_PROP_QTY",3,3)
V579M=V(4,"GOODS_PROP_SKEPID",4,4)
V580M=V(4,"GOODS_PROP_SKEP_PLACE",5,5)
V581M=V(4,"GOODS_PROP_EXPIRE",6,6)
V582M=V(4,"GOODS_PROP_MAXID",7,7)
V583M=V(4,"EQUIP_PROP_UNKNOW",8,8)
V584M=V(4,"EQUIP_PROP_STR_LV",9,9)
V585M=V(4,"EQUIP_PROP_PRO_1",10,10)
V586M=V(4,"EQUIP_PROP_PRO_2",11,11)
V587M=V(4,"EQUIP_PROP_PRO_3",12,12)
V588M=V(4,"EQUIP_PROP_PRO_4",13,13)
V589M=V(4,"EQUIP_PROP_UPGRADE_EXP",14,14)
V590M=V(4,"EQUIP_PROP_LOCK",15,15)
V591M=V(4,"EQUIP_PROP_EXTEND_PRO_1",16,16)
V592M=V(4,"EQUIP_PROP_EXTEND_PRO_2",17,17)
V593M=V(4,"EQUIP_PROP_EXTEND_PRO_3",18,18)
V594M=V(4,"EQUIP_PROP_EXTEND_PRO_4",19,19)
V595M=V(4,"EQUIP_PROP_EXTEND_PRO_5",20,20)
V596M=V(4,"EQUIP_PROP_TEMP_EXTEND_PRO_1",21,21)
V597M=V(4,"EQUIP_PROP_TEMP_EXTEND_PRO_2",22,22)
V598M=V(4,"EQUIP_PROP_TEMP_EXTEND_PRO_3",23,23)
V599M=V(4,"EQUIP_PROP_TEMP_EXTEND_PRO_4",24,24)
V600M=V(4,"EQUIP_PROP_TEMP_EXTEND_PRO_5",25,25)
V601M=V(4,"EQUIP_PROP_RECAST_TYPE",26,26)
V602M=V(4,"EQUIP_PROP_HIGH_REFORGE_COUNT",27,27)
V603M=V(4,"EQUIP_PROP_RESONANCE_PRO",28,28)
V604M=V(4,"EQUIP_PROP_RECAST_TIME",29,29)
V605M=V(4,"EQUIP_PROP_MAXID",30,30)
E9M=E(3,"GoodsProp",".CSMsg.GoodsProp")
V606M=V(4,"DRONESTAR_PROP_UNKNOW",0,8)
V607M=V(4,"DRONESTAR_PROP_STAR",1,9)
V608M=V(4,"DRONESTAR_PROP_PRO_1",2,10)
V609M=V(4,"DRONESTAR_PROP_PRO_2",3,11)
V610M=V(4,"DRONESTAR_PROP_PRO_3",4,12)
V611M=V(4,"DRONESTAR_PROP_PRO_4",5,13)
V612M=V(4,"DRONESTAR_PROP_PRO_5",6,14)
V613M=V(4,"DRONESTAR_PROP_LOCK",7,15)
V614M=V(4,"DRONESTAR_PROP_MAXID",8,16)
E10M=E(3,"DroneStarProp",".CSMsg.DroneStarProp")
V615M=V(4,"SKILL_PROP_UNKNOW",0,8)
V616M=V(4,"SKILL_PROP_STR_LV",1,9)
V617M=V(4,"SKILL_PROP_USE_COUNT",2,10)
V618M=V(4,"SKILL_PROP_PRO_1",3,11)
V619M=V(4,"SKILL_PROP_PRO_2",4,12)
V620M=V(4,"SKILL_PROP_PRO_3",5,13)
V621M=V(4,"SKILL_PROP_PRO_4",6,14)
V622M=V(4,"SKILL_PROP_PRO_5",7,15)
V623M=V(4,"SKILL_PROP_PRO_VALUE_1",8,16)
V624M=V(4,"SKILL_PROP_PRO_VALUE_2",9,17)
V625M=V(4,"SKILL_PROP_PRO_VALUE_3",10,18)
V626M=V(4,"SKILL_PROP_PRO_VALUE_4",11,19)
V627M=V(4,"SKILL_PROP_PRO_VALUE_5",12,20)
V628M=V(4,"SKILL_PROP_HERO_SKEPID_1",13,21)
V629M=V(4,"SKILL_PROP_HERO_SKEPID_2",14,22)
V630M=V(4,"SKILL_PROP_HERO_SKEPID_3",15,23)
V631M=V(4,"SKILL_PROP_HERO_SKEPID_4",16,24)
V632M=V(4,"SKILL_PROP_HERO_SKEPID_5",17,25)
V633M=V(4,"SKILL_PROP_HERO_SKEPID_6",18,26)
V634M=V(4,"SKILL_PROP_MAXID",19,27)
E11M=E(3,"SkillProp",".CSMsg.SkillProp")
V635M=V(4,"SIGIL_PROP_UNKNOW",0,8)
V636M=V(4,"SIGIL_PROP_STR_LV",1,9)
V637M=V(4,"SIGIL_PROP_PRO_1",2,10)
V638M=V(4,"SIGIL_PROP_PRO_2",3,11)
V639M=V(4,"SIGIL_PROP_PRO_3",4,12)
V640M=V(4,"SIGIL_PROP_PRO_4",5,13)
V641M=V(4,"SIGIL_PROP_EXTEND_PRO_1",6,14)
V642M=V(4,"SIGIL_PROP_EXTEND_PRO_2",7,15)
V643M=V(4,"SIGIL_PROP_EXTEND_PRO_3",8,16)
V644M=V(4,"SIGIL_PROP_EXTEND_PRO_4",9,17)
V645M=V(4,"SIGIL_PROP_MAXID",10,18)
E12M=E(3,"SigilProp",".CSMsg.SigilProp")
V646M=V(4,"DECORATE_PROP_UNKNOW",0,8)
V647M=V(4,"DECORATE_PROP_LOCK",1,9)
V648M=V(4,"DECORATE_PROP_PALSID",2,10)
V649M=V(4,"DECORATE_PROP_STR_LV",3,11)
V650M=V(4,"DECORATE_PROP_MAXID",4,12)
E13M=E(3,"DecorateProp",".CSMsg.DecorateProp")
V651M=V(4,"STARDIAMOND_PROP_UNKNOW",0,8)
V652M=V(4,"STARDIAMOND_PROP_LOCK",1,9)
V653M=V(4,"STARDIAMOND_PROP_PALSID",2,10)
V654M=V(4,"STARDIAMOND_PROP_STR_LV",3,11)
V655M=V(4,"STARDIAMOND_PROP_MAXID",4,12)
E14M=E(3,"StarDiamondProp",".CSMsg.StarDiamondProp")
V656M=V(4,"SKEP_TYPE_UNKNOW",0,0)
V657M=V(4,"SKEP_TYPE_PACK",1,1)
V658M=V(4,"SKEP_TYPE_PALEQUIP",2,2)
V659M=V(4,"SKEP_TYPE_PALSKILL",3,3)
V660M=V(4,"SKEP_TYPE_IDLEPACK",4,4)
V661M=V(4,"SKEP_TYPE_MAZE",5,5)
V662M=V(4,"SKEP_TYPE_MAZETEMP",6,6)
V663M=V(4,"SKEP_TYPE_PEAK",7,7)
V664M=V(4,"SKEP_TYPE_PEAKTEMP",8,8)
V665M=V(4,"SKEP_TYPE_ASHDUNGEON",9,9)
V666M=V(4,"SKEP_TYPE_PALSIGIL",10,10)
V667M=V(4,"SKEP_TYPE_PALDECORATE",11,11)
V668M=V(4,"SKEP_TYPE_STARDIAMOND",12,12)
V669M=V(4,"SKEP_TYPE_DRONESTAR",13,13)
V670M=V(4,"SKEP_TYPE_MAX",14,14)
E15M=E(3,"SkepType",".CSMsg.SkepType")
V671M=V(4,"GOODS_TYPE_EQUIP",0,1)
V672M=V(4,"GOODS_TYPE_SKILL",1,2)
V673M=V(4,"GOODS_TYPE_PIECE",2,3)
V674M=V(4,"GOODS_TYPE_OTHER",3,4)
V675M=V(4,"GOODS_TYPE_PACKAGE",4,5)
V676M=V(4,"GOODS_TYPE_RELIC",5,9)
V677M=V(4,"GOODS_TYPE_SIGIL",6,19)
V678M=V(4,"GOODS_TYPE_DECORATE",7,21)
V679M=V(4,"GOODS_TYPE_TRESURERARE",8,22)
V680M=V(4,"GOODS_TYPE_STARDIAMOND",9,23)
V681M=V(4,"GOODS_TYPE_MYTHICAL",10,40)
E16M=E(3,"GoodsType",".CSMsg.GoodsType")
V682M=V(4,"DailyAttend_NoLogin",0,1)
V683M=V(4,"DailyAttend_LoginNoSign",1,2)
V684M=V(4,"DailyAttend_LoginSign",2,3)
E17M=E(3,"DailyAttendType",".CSMsg.DailyAttendType")
V685M=V(4,"enRolePlot_NotUnlock",0,1)
V686M=V(4,"enRolePlot_HaveUnlockedNoAward",1,2)
V687M=V(4,"enRolePlot_HaveUnlockHaveAward",2,3)
E18M=E(3,"RolePlotGetRewardType",".CSMsg.RolePlotGetRewardType")
V688M=V(4,"PERSONPART_PACKET",0,1)
V689M=V(4,"PERSONPART_PAL",1,2)
V690M=V(4,"PERSONPART_IDLE",2,3)
V691M=V(4,"PERSONPART_CITY",3,4)
V692M=V(4,"PERSONPART_LOTTERY",4,5)
V693M=V(4,"PERSONPART_LOTTERY_WISH",5,6)
V694M=V(4,"PERSONPART_SUBJECT",6,7)
V695M=V(4,"PERSONPART_ILLUSIONTOWER",7,8)
V696M=V(4,"PERSONPART_TAVERN",8,9)
V697M=V(4,"PERSONPART_ARENAPART",9,11)
V698M=V(4,"PERSONPART_TRIAL",10,12)
V699M=V(4,"PERSONPART_ASHDUNGEON",11,13)
V700M=V(4,"PERSONPART_FACEDATA",12,14)
V701M=V(4,"PERSONPART_ALLIEDTECH",13,15)
V702M=V(4,"PERSONPART_ACTIVITY",14,16)
V703M=V(4,"PERSONPART_LEAGUE",15,17)
V704M=V(4,"PERSONPART_LEAGUECOMP",16,18)
V705M=V(4,"PERSONPART_HALO",17,19)
V706M=V(4,"PERSONPART_FARM",18,20)
V707M=V(4,"PERSONPART_WEAPON",19,21)
V708M=V(4,"PERSONPART_MATE",20,22)
V709M=V(4,"PERSONPART_MAZE",21,23)
V710M=V(4,"PERSONPART_PASSPORT",22,24)
V711M=V(4,"PERSONPART_SOULLINK",23,25)
V712M=V(4,"PERSONPART_FRAME",24,26)
V713M=V(4,"PERSONPART_FACTION",25,27)
V714M=V(4,"PERSONPART_PEAK",26,28)
V715M=V(4,"PERSONPART_SEVENCHALLENGE",27,29)
V716M=V(4,"PERSONPART_OPERATIVE",28,30)
V717M=V(4,"PERSONPART_MERGESERVER",29,31)
V718M=V(4,"PERSONPART_RETURNACT",30,32)
V719M=V(4,"PERSONPART_SPACEEXPLORATION",31,33)
V720M=V(4,"PERSONPART_BATTLESHIPTECH",32,34)
V721M=V(4,"PERSONPART_SUBGIFT",33,35)
V722M=V(4,"PERSONPART_CHINARED",34,36)
V723M=V(4,"PERSONPART_DECORATE",35,37)
V724M=V(4,"PERSONPART_SHOP_DECORATE",36,38)
V725M=V(4,"PERSONPART_INVITEGIFT",37,39)
V726M=V(4,"PERSONPART_TREASURERARE",38,40)
V727M=V(4,"PERSONPART_STARTEMPLE",39,41)
V728M=V(4,"PERSONPART_XYX",40,42)
V729M=V(4,"PERSONPART_PICKTHEROUTE",41,43)
V730M=V(4,"PERSONPART_SCIENTIFICRESEARCH",42,44)
V731M=V(4,"PERSONPART_DRONECENTER",43,45)
V732M=V(4,"PERSONPART_TASKMGR",44,46)
V733M=V(4,"PERSONPART_RECHARGEGIFT",45,47)
V734M=V(4,"PERSONPART_ENTITY_MAXID",46,48)
E19M=E(3,"PersonPart",".CSMsg.PersonPart")
V735M=V(4,"emHeroCamp_min",0,0)
V736M=V(4,"emHeroCamp_Dim",1,1)
V737M=V(4,"emHeroCamp_Fort",2,2)
V738M=V(4,"emHeroCamp_Abyss",3,3)
V739M=V(4,"emHeroCamp_Forest",4,4)
V740M=V(4,"emHeroCamp_Dark",5,5)
V741M=V(4,"emHeroCamp_Light",6,6)
V742M=V(4,"emHeroCamp_Max",7,7)
E20M=E(3,"enHeroCampType",".CSMsg.enHeroCampType")
V743M=V(4,"emTechAddCamp_min",0,0)
V744M=V(4,"emTechAddCamp_Dim",1,1)
V745M=V(4,"emTechAddCamp_Fort",2,2)
V746M=V(4,"emTechAddCamp_Abyss",3,3)
V747M=V(4,"emTechAddCamp_Forest",4,4)
V748M=V(4,"emTechAddCamp_Dark",5,5)
V749M=V(4,"emTechAddCamp_Light",6,6)
V750M=V(4,"emTechAddCamp_FourDepartment",7,7)
V751M=V(4,"emTechAddCamp_DarkLight",8,8)
V752M=V(4,"emTechAddCamp_AllDepartment",9,9)
V753M=V(4,"emTechAddCamp_Max",10,10)
E21M=E(3,"enTechAddCampType",".CSMsg.enTechAddCampType")
V754M=V(4,"HeroOccupation_Warrior",0,1)
V755M=V(4,"HeroOccupation_Master",1,2)
V756M=V(4,"HeroOccupation_Ranger",2,3)
V757M=V(4,"HeroOccupation_Assassin",3,4)
V758M=V(4,"HeroOccupation_Priest",4,5)
E22M=E(3,"HeroOccupation",".CSMsg.HeroOccupation")
V759M=V(4,"PAL_SRC_DEFAULT",0,0)
V760M=V(4,"PAL_SRC_MAZE",1,1)
V761M=V(4,"PAL_SRC_PEAK",2,2)
V762M=V(4,"PAL_SRC_TRIALATY",3,3)
V763M=V(4,"PAL_SRC_MAX",4,4)
E23M=E(3,"PalSrc",".CSMsg.PalSrc")
V764M=V(4,"TrialHeroBattleType_Maze",0,1)
V765M=V(4,"TrialHeroBattleType_Peak",1,2)
V766M=V(4,"TrialHeroBattleType_ChinaRed",2,3)
V767M=V(4,"TrialHeroBattleType_IdlePassStage",3,4)
V768M=V(4,"TrialHeroBattleType_IllusionTower",4,5)
V769M=V(4,"TrialHeroBattleType_LeaActivityBoss",5,6)
V770M=V(4,"TrialHeroBattleType_AshDungeon",6,7)
V771M=V(4,"TrialHeroBattleType_Friend",7,8)
V772M=V(4,"TrialHeroBattleType_CrystalCrown",8,9)
V773M=V(4,"TrialHeroBattleType_Legend",9,10)
V774M=V(4,"TrialHeroBattleType_Max",10,11)
E24M=E(3,"TrialHeroBattleType",".CSMsg.TrialHeroBattleType")
V775M=V(4,"enTask_DailyTask",0,1)
V776M=V(4,"enTask_ChapterTask",1,2)
V777M=V(4,"enTask_MainTask",2,3)
V778M=V(4,"enTask_BranchTask",3,4)
V779M=V(4,"enTask_CampTrial",4,5)
V780M=V(4,"enTask_ArmsRace",5,6)
V781M=V(4,"enTask_CommanderWeek",6,12)
V782M=V(4,"enTask_WorldBossTask",7,13)
V783M=V(4,"enTask_AllianceDuel",8,18)
V784M=V(4,"enTask_Competition",9,20)
V785M=V(4,"enTask_Lord_Tour",10,22)
V786M=V(4,"enTask_ZoneBattleDuelTask",11,29)
V787M=V(4,"enTask_SkyFall",12,36)
V788M=V(4,"enTask_RankTask",13,24)
V789M=V(4,"enTask_LoginReward",14,26)
V790M=V(4,"enTask_MonstersApproaching",15,35)
V791M=V(4,"enTask_FirstBuy",16,37)
V792M=V(4,"enTask_ZombieTreasure",17,38)
E25M=E(3,"TTaskModule",".CSMsg.TTaskModule")
V793M=V(4,"enPerson",0,1)
V794M=V(4,"enHero",1,2)
V795M=V(4,"enGoods",2,3)
E26M=E(3,"EntityType",".CSMsg.EntityType")
V796M=V(4,"HERO_BASE_HP",0,1)
V797M=V(4,"HERO_BASE_ATK",1,2)
V798M=V(4,"HERO_BASE_DEF",2,3)
V799M=V(4,"HERO_ENERGY_LIMIT",3,4)
V800M=V(4,"HERO_SPEED",4,5)
V801M=V(4,"CRITICAL_HIT_BOOST",5,6)
V802M=V(4,"CRITICAL_RESISTANCE",6,7)
V803M=V(4,"CRITICAL_DMG_BOOST",7,8)
V804M=V(4,"CRITICAL_DMG_RESISTANCE",8,9)
V805M=V(4,"GENERIC_DMG_BOOST",9,10)
V806M=V(4,"GENERIC_DMG_REDUCTION",10,11)
V807M=V(4,"ANTI_DAMAGE_BOOST",11,12)
V808M=V(4,"ANTI_DAMAGE_REDUCTION",12,13)
V809M=V(4,"MONSTER_DMG_BOOST",13,14)
V810M=V(4,"MONSTER_DMG_REDUCTION",14,15)
V811M=V(4,"PHYSICAL_DMG_BOOST",15,16)
V812M=V(4,"PHYSICAL_DMG_REDUCTION",16,17)
V813M=V(4,"ENERGY_DMG_BOOST",17,18)
V814M=V(4,"ENERGY_DMG_REDUCTION",18,19)
V815M=V(4,"FOREST_HERO_DMG_BOOST",19,20)
V816M=V(4,"HUMANOID_HERO_DMG_BOOST",20,21)
V817M=V(4,"NIGHT_ELF_HERO_DMG_BOOST",21,22)
V818M=V(4,"GENERIC_TROOP_AMOUNT",22,23)
V819M=V(4,"FOREST_HERO_TROOP_BOOST",23,24)
V820M=V(4,"HUMANOID_HERO_TROOP_BOOST",24,25)
V821M=V(4,"NIGHT_ELF_HERO_TROOP_BOOST",25,26)
V822M=V(4,"SKILL_DMG_BOOST",26,27)
V823M=V(4,"ENERGY_RECOVERY",27,28)
V824M=V(4,"ENERGY_INITIAL",28,41)
V825M=V(4,"DRONE_HP",29,29)
V826M=V(4,"DRONE_ATTACK",30,30)
V827M=V(4,"DRONE_DEFENCE",31,31)
V828M=V(4,"INDEPENDENT_DAMAGE_VALUE",32,42)
V829M=V(4,"COMBAT_POWER_DAMAGE_VALUE",33,43)
V830M=V(4,"COMBAT_POWER_DAMAGE_CAP",34,44)
V831M=V(4,"HERO_EXTRA_HP",35,101)
V832M=V(4,"HERO_EXTRA_ATK",36,102)
V833M=V(4,"HERO_EXTRA_DEF",37,103)
V834M=V(4,"FOREST_HERO_EXTRA_HP",38,104)
V835M=V(4,"FOREST_HERO_EXTRA_ATK",39,105)
V836M=V(4,"FOREST_HERO_EXTRA_DEF",40,106)
V837M=V(4,"HUMANOID_HERO_EXTRA_HP",41,107)
V838M=V(4,"HUMANOID_HERO_EXTRA_ATK",42,108)
V839M=V(4,"HUMANOID_HERO_EXTRA_DEF",43,109)
V840M=V(4,"NIGHT_ELF_HERO_EXTRA_HP",44,110)
V841M=V(4,"NIGHT_ELF_HERO_EXTRA_ATK",45,111)
V842M=V(4,"NIGHT_ELF_HERO_EXTRA_DEF",46,112)
V843M=V(4,"HERO_BASE_HP_BOOST",47,201)
V844M=V(4,"HERO_BASE_ATK_BOOST",48,202)
V845M=V(4,"HERO_BASE_DEF_BOOST",49,203)
V846M=V(4,"FOREST_HERO_HP_BOOST",50,204)
V847M=V(4,"FOREST_HERO_ATK_BOOST",51,205)
V848M=V(4,"FOREST_HERO_DEF_BOOST",52,206)
V849M=V(4,"HUMANOID_HERO_HP_BOOST",53,207)
V850M=V(4,"HUMANOID_HERO_ATK_BOOST",54,208)
V851M=V(4,"HUMANOID_HERO_DEF_BOOST",55,209)
V852M=V(4,"NIGHT_ELF_HERO_HP_BOOST",56,210)
V853M=V(4,"NIGHT_ELF_HERO_ATK_BOOST",57,211)
V854M=V(4,"NIGHT_ELF_HERO_DEF_BOOST",58,212)
V855M=V(4,"ALLY_GATHERING_HP_BOOST",59,213)
V856M=V(4,"ALLY_GATHERING_ATK_BOOST",60,214)
V857M=V(4,"ALLY_GATHERING_DEF_BOOST",61,215)
V858M=V(4,"ALLY_DEFENSE_HP_BOOST",62,216)
V859M=V(4,"ALLY_DEFENSE_ATK_BOOST",63,217)
V860M=V(4,"ALLY_DEFENSE_DEF_BOOST",64,218)
V861M=V(4,"ATTACK_BASE_HP_BOOST",65,219)
V862M=V(4,"ATTACK_BASE_ATK_BOOST",66,220)
V863M=V(4,"ATTACK_BASE_DEF_BOOST",67,221)
V864M=V(4,"DEFEND_IN_CITY_HP_BOOST",68,222)
V865M=V(4,"DEFEND_IN_CITY_ATK_BOOST",69,223)
V866M=V(4,"DEFEND_IN_CITY_DEF_BOOST",70,224)
V867M=V(4,"PULL_TRUCK_ATTACK_BOOST",71,225)
V868M=V(4,"PULL_TRUCK_DEFENSE_BOOST",72,226)
V869M=V(4,"TeamX_HeroHealth",73,271)
V870M=V(4,"TeamX_HeroAttack",74,272)
V871M=V(4,"TeamX_HeroDefense",75,273)
V872M=V(4,"TeamX_AttackBase_HealthBoost",76,274)
V873M=V(4,"TeamX_AttackBase_AttackBoost",77,275)
V874M=V(4,"TeamX_AttackBase_DefenseBoost",78,276)
V875M=V(4,"TeamX_DefendCity_HealthBoost",79,277)
V876M=V(4,"TeamX_DefendCity_AttackBoost",80,278)
V877M=V(4,"TeamX_DefendCity_DefenseBoost",81,279)
V878M=V(4,"HeroAttributes_MAXVALUE",82,280)
E27M=E(3,"HeroAttributes",".CSMsg.HeroAttributes")
V879M=V(4,"BATTLE_NEW_TOTAL_HEALTH_BASE",0,0)
V880M=V(4,"BATTLE_NEW_CURR_HEALTH_BASE",1,1)
V881M=V(4,"BATTLE_NEW_TOTAL_ATTACK_BASE",2,2)
V882M=V(4,"BATTLE_NEW_TOTAL_DEFENSE_BASE",3,3)
V883M=V(4,"BATTLE_NEW_TOTAL_TROOPS_CAPACITY",4,4)
V884M=V(4,"BATTLE_NEW_ENERGY_CAPACITY",5,5)
V885M=V(4,"BATTLE_NEW_CURR_ENERGY",6,6)
V886M=V(4,"BATTLE_NEW_SPEED",7,7)
V887M=V(4,"BATTLE_NEW_CRITICAL_HIT_RATE",8,8)
V888M=V(4,"BATTLE_NEW_CRITICAL_RESISTANCE_RATE",9,9)
V889M=V(4,"BATTLE_NEW_CRITICAL_DAMAGE_RATE",10,10)
V890M=V(4,"BATTLE_NEW_CRITICAL_DAMAGE_RES_RATE",11,11)
V891M=V(4,"BATTLE_NEW_GENERIC_DAMAGE_INCREASE",12,12)
V892M=V(4,"BATTLE_NEW_GENERIC_DAMAGE_REDUCTION",13,13)
V893M=V(4,"BATTLE_NEW_COUNTER_DAMAGE_INCREASE",14,14)
V894M=V(4,"BATTLE_NEW_COUNTER_DAMAGE_REDUCTION",15,15)
V895M=V(4,"BATTLE_NEW_MONSTER_DAMAGE_INCREASE",16,16)
V896M=V(4,"BATTLE_NEW_MONSTER_DAMAGE_REDUCTION",17,17)
V897M=V(4,"BATTLE_NEW_PHYSICAL_DAMAGE_INCREASE",18,18)
V898M=V(4,"BATTLE_NEW_PHYSICAL_DAMAGE_REDUCTION",19,19)
V899M=V(4,"BATTLE_NEW_ENERGY_DAMAGE_INCREASE",20,20)
V900M=V(4,"BATTLE_NEW_ENERGY_DAMAGE_REDUCTION",21,21)
V901M=V(4,"BATTLE_NEW_SKILL_DAMAGE_INCREASE",22,22)
V902M=V(4,"BATTLE_NEW_ENERGY_RECOVER",23,23)
V903M=V(4,"BATTLE_NEW_INDEPENDENT_DAMAGE_VALUE",24,24)
V904M=V(4,"BATTLE_NEW_COMBAT_POWER_DAMAGE_VALUE",25,25)
V905M=V(4,"BATTLE_NEW_COMBAT_POWER_DAMAGE_CAP",26,26)
V906M=V(4,"BATTLE_NEW_LEVEL_DAMAGE_INCREASE",27,27)
V907M=V(4,"BATTLE_NEW_MORAL_DAMAGE_INCREASE",28,28)
V908M=V(4,"BATTLE_NEW_MAX_HEALTH_BASE",29,29)
V909M=V(4,"BATTLE_NEW_MAX_ENERGY_BASE",30,30)
V910M=V(4,"BATTLE_NEW_MAXVALUE",31,31)
E28M=E(3,"HeroPalAttributes",".CSMsg.HeroPalAttributes")
V911M=V(4,"GWBuffType_None",0,0)
V912M=V(4,"GWBuffType_Feverish",1,1)
V913M=V(4,"GWBuffType_Sheild",2,2)
V914M=V(4,"GWBuffType_Burn",3,3)
E29M=E(3,"GWBuffType",".CSMsg.GWBuffType")
V915M=V(4,"GWMapType_CityBuilding",0,0)
V916M=V(4,"GWMapType_Technology",1,1)
V917M=V(4,"GWMapType_Research",2,2)
V918M=V(4,"GWMapType_Survival",3,3)
V919M=V(4,"GWMapType_Buff",4,4)
V920M=V(4,"GWMapType_DroneCenter",5,5)
V921M=V(4,"GWMapType_VIPPart",6,6)
V922M=V(4,"GWMapType_NeutralCity",7,7)
V923M=V(4,"GWMapType_SchlossId",8,8)
V924M=V(4,"GWMapType_MonthlyCard",9,9)
V925M=V(4,"GWMapType_DecorateBuild",10,10)
V926M=V(4,"GWMapType_DressUp",11,11)
V927M=V(4,"GWMapType_HonorWall",12,12)
V928M=V(4,"GWMapType_Congress",13,13)
V929M=V(4,"GWMapType_Sum",14,999)
E30M=E(3,"GWMapType",".CSMsg.GWMapType")
V930M=V(4,"ModuleOpenType_SandBox",0,1001)
V931M=V(4,"ModuleOpenType_League",1,1002)
V932M=V(4,"ModuleOpenType_SciResearch",2,1003)
V933M=V(4,"ModuleOpenType_Citybuilding",3,1004)
V934M=V(4,"ModuleOpenType_Email",4,1005)
V935M=V(4,"ModuleOpenType_Chat",5,1006)
V936M=V(4,"ModuleOpenType_Radar",6,1007)
V937M=V(4,"ModuleOpenType_ArmamentRace",7,1008)
V938M=V(4,"ModuleOpenType_AllianceBattle",8,1009)
V939M=V(4,"ModuleOpenType_DiffServer_AllianceBattle",9,1010)
V940M=V(4,"ModuleOpenType_CityCompetition",10,1011)
V941M=V(4,"ModuleOpenType_WarZoneBattle",11,1012)
V942M=V(4,"ModuleOpenType_SandBoxWarning",12,1013)
V943M=V(4,"ModuleOpenType_Reconnaissance",13,1014)
V944M=V(4,"ModuleOpenType_MarchingBand",14,1015)
V945M=V(4,"ModuleOpenType_Search",15,1016)
V946M=V(4,"ModuleOpenType_Collect",16,1017)
V947M=V(4,"ModuleOpenType_Building",17,1018)
V948M=V(4,"ModuleOpenType_Rank",18,1022)
V949M=V(4,"ModuleOpenType_AcornPubTreasure1",19,1024)
V950M=V(4,"ModuleOpenType_AcornPubTreasure2",20,1025)
V951M=V(4,"ModuleOpenType_HeroRecruit",21,1100)
V952M=V(4,"ModuleOpenType_SurvivalRecruit",22,1200)
V953M=V(4,"ModuleOpenType_HeroEquipment",23,1300)
V954M=V(4,"ModuleOpenType_HeroEquipLevel",24,1301)
V955M=V(4,"ModuleOpenType_HeroEquipStrength",25,1302)
V956M=V(4,"ModuleOpenType_HeroEquipMake",26,1303)
V957M=V(4,"ModuleOpenType_Carriage",27,1507)
V958M=V(4,"ModuleOpenType_LineUpBoostHalo",28,1524)
V959M=V(4,"ModuleOpenType_AllianceAutoHelp",29,2105)
E31M=E(3,"ModuleOpenType",".CSMsg.ModuleOpenType")
F1D=F(2,"typeId",".CSMsg.TMapTypeData.typeId",1,0,2,false,0,13,3)
F2D=F(2,"propvalue",".CSMsg.TMapTypeData.propvalue",2,1,2,false,0,3,2)
M1G=D(1,"TMapTypeData",".CSMsg.TMapTypeData",false,{},{},nil,{})
F3D=F(2,"propId",".CSMsg.RoleProp.propId",1,0,2,false,0,13,3)
F4D=F(2,"propData",".CSMsg.RoleProp.propData",2,1,3,false,{},11,10)
M2G=D(1,"RoleProp",".CSMsg.RoleProp",false,{},{},nil,{})
F5D=F(2,"roleData",".CSMsg.TMSG_ROLEPROP_UPDATE_NTF.roleData",1,0,3,false,{},11,10)
M3G=D(1,"TMSG_ROLEPROP_UPDATE_NTF",".CSMsg.TMSG_ROLEPROP_UPDATE_NTF",false,{},{},nil,{})
F6D=F(2,"roleData",".CSMsg.TMSG_ROLEPROP_LOGIN_NTF.roleData",1,0,3,false,{},11,10)
M4G=D(1,"TMSG_ROLEPROP_LOGIN_NTF",".CSMsg.TMSG_ROLEPROP_LOGIN_NTF",false,{},{},nil,{})
F7D=F(2,"gender",".CSMsg.TActorFormData.TFormBase.gender",1,0,2,false,0,5,1)
F8D=F(2,"faceType",".CSMsg.TActorFormData.TFormBase.faceType",2,1,2,false,0,5,1)
F9D=F(2,"skinColorID",".CSMsg.TActorFormData.TFormBase.skinColorID",3,2,2,false,0,5,1)
F10D=F(2,"hairStyle",".CSMsg.TActorFormData.TFormBase.hairStyle",4,3,2,false,0,5,1)
F11D=F(2,"hairColorID",".CSMsg.TActorFormData.TFormBase.hairColorID",5,4,2,false,0,5,1)
M6G=D(1,"TFormBase",".CSMsg.TActorFormData.TFormBase",false,{},{},nil,{})
F12D=F(2,"equips",".CSMsg.TActorFormData.TFormEquip.equips",1,0,3,false,{},5,1)
M7G=D(1,"TFormEquip",".CSMsg.TActorFormData.TFormEquip",false,{},{},nil,{})
F13D=F(2,"formBase",".CSMsg.TActorFormData.formBase",1,0,2,false,nil,11,10)
F14D=F(2,"formEquip",".CSMsg.TActorFormData.formEquip",2,1,2,false,nil,11,10)
M5G=D(1,"TActorFormData",".CSMsg.TActorFormData",false,nil,{},nil,{})
F15D=F(2,"id",".CSMsg.TPartData.id",1,0,2,false,0,5,1)
F16D=F(2,"data",".CSMsg.TPartData.data",2,1,2,false,"",12,9)
M8G=D(1,"TPartData",".CSMsg.TPartData",false,{},{},nil,{})
F17D=F(2,"propid",".CSMsg.TPropData.propid",1,0,2,false,0,5,1)
F18D=F(2,"propvalue",".CSMsg.TPropData.propvalue",2,1,2,false,0,3,2)
M9G=D(1,"TPropData",".CSMsg.TPropData",false,{},{},nil,{})
F19D=F(2,"prop",".CSMsg.TGoodsData.prop",1,0,3,false,{},11,10)
M10G=D(1,"TGoodsData",".CSMsg.TGoodsData",false,{},{},nil,{})
F20D=F(2,"prop",".CSMsg.TPalData.prop",1,0,3,false,{},11,10)
F21D=F(2,"src",".CSMsg.TPalData.src",2,1,1,false,nil,14,8)
F22D=F(2,"reason",".CSMsg.TPalData.reason",3,2,1,false,0,5,1)
F23D=F(2,"decoratePartLv",".CSMsg.TPalData.decoratePartLv",4,3,3,false,{},5,1)
M11G=D(1,"TPalData",".CSMsg.TPalData",false,{},{},nil,{})
F24D=F(2,"heroID",".CSMsg.TPalHBookData.heroID",1,0,2,false,0,5,1)
F25D=F(2,"heroStarLv",".CSMsg.TPalHBookData.heroStarLv",2,1,2,false,0,5,1)
M13G=D(1,"TPalHBookData",".CSMsg.TPalHBookData",false,{},{},nil,{})
F26D=F(2,"heroID",".CSMsg.TPalRolePlot.heroID",1,0,2,false,0,5,1)
F27D=F(2,"nLvStage",".CSMsg.TPalRolePlot.nLvStage",2,1,2,false,0,5,1)
F28D=F(2,"type",".CSMsg.TPalRolePlot.type",3,2,2,false,nil,14,8)
M14G=D(1,"TPalRolePlot",".CSMsg.TPalRolePlot",false,{},{},nil,{})
F29D=F(2,"skepID",".CSMsg.TSkepData.skepID",1,0,2,false,0,5,1)
F30D=F(2,"skepType",".CSMsg.TSkepData.skepType",2,1,2,false,0,5,1)
M16G=D(1,"TSkepData",".CSMsg.TSkepData",false,{},{},nil,{})
F31D=F(2,"id",".CSMsg.TTechData.id",1,0,2,false,0,5,1)
F32D=F(2,"level",".CSMsg.TTechData.level",2,1,2,false,0,5,1)
M17G=D(1,"TTechData",".CSMsg.TTechData",false,{},{},nil,{})
F33D=F(2,"iProfession",".CSMsg.TProfessionTechData.iProfession",1,0,2,false,nil,14,8)
F34D=F(2,"arrTechData",".CSMsg.TProfessionTechData.arrTechData",2,1,3,false,{},11,10)
F35D=F(2,"resetTimes",".CSMsg.TProfessionTechData.resetTimes",3,2,2,false,0,5,1)
F36D=F(2,"resetTimes2",".CSMsg.TProfessionTechData.resetTimes2",4,3,1,false,0,5,1)
M18G=D(1,"TProfessionTechData",".CSMsg.TProfessionTechData",false,{},{},nil,{})
F37D=F(2,"millExp",".CSMsg.TMillData.millExp",1,0,2,false,0,13,3)
F38D=F(2,"millLv",".CSMsg.TMillData.millLv",2,1,2,false,0,13,3)
F39D=F(2,"mullCtb",".CSMsg.TMillData.mullCtb",3,2,2,false,0,13,3)
M20G=D(1,"TMillData",".CSMsg.TMillData",false,{},{},nil,{})
F40D=F(2,"goodsData",".CSMsg.TPacketPart.goodsData",1,0,3,false,{},11,10)
F41D=F(2,"skepData",".CSMsg.TPacketPart.skepData",2,1,3,false,{},11,10)
M21G=D(1,"TPacketPart",".CSMsg.TPacketPart",false,{},{},nil,{})
F42D=F(2,"type",".CSMsg.TTrialPalBattleTimes.type",1,0,2,false,nil,14,8)
F43D=F(2,"times",".CSMsg.TTrialPalBattleTimes.times",2,1,2,false,0,13,3)
M22G=D(1,"TTrialPalBattleTimes",".CSMsg.TTrialPalBattleTimes",false,{},{},nil,{})
F44D=F(2,"herosid",".CSMsg.TTrialPal.herosid",1,0,2,false,0,13,3)
F45D=F(2,"activityid",".CSMsg.TTrialPal.activityid",2,1,1,false,0,13,3)
F46D=F(2,"getTime",".CSMsg.TTrialPal.getTime",3,2,1,false,0,13,3)
F47D=F(2,"battleTimes",".CSMsg.TTrialPal.battleTimes",4,3,3,false,{},11,10)
M24G=D(1,"TTrialPal",".CSMsg.TTrialPal",false,{},{},nil,{})
F48D=F(2,"palData",".CSMsg.TPalPart.palData",1,0,3,false,{},11,10)
F49D=F(2,"palHBookData",".CSMsg.TPalPart.palHBookData",2,1,3,false,{},11,10)
F50D=F(2,"palRolePlotData",".CSMsg.TPalPart.palRolePlotData",3,2,3,false,{},11,10)
F51D=F(2,"palTrialData",".CSMsg.TPalPart.palTrialData",4,3,3,false,{},11,10)
M25G=D(1,"TPalPart",".CSMsg.TPalPart",false,{},{},nil,{})
F52D=F(2,"palTrialData",".CSMsg.TMSG_TRIALHERO_UPDATE_NTF.palTrialData",1,0,3,false,{},11,10)
M26G=D(1,"TMSG_TRIALHERO_UPDATE_NTF",".CSMsg.TMSG_TRIALHERO_UPDATE_NTF",false,{},{},nil,{})
F53D=F(2,"idleStage",".CSMsg.TIdlePart.idleStage",1,0,2,false,0,13,3)
F54D=F(2,"coins",".CSMsg.TIdlePart.coins",2,1,2,false,0,13,3)
F55D=F(2,"souls",".CSMsg.TIdlePart.souls",3,2,2,false,0,13,3)
F56D=F(2,"exp",".CSMsg.TIdlePart.exp",4,3,2,false,0,13,3)
F57D=F(2,"passStage",".CSMsg.TIdlePart.passStage",5,4,2,false,0,13,3)
F58D=F(2,"starReward",".CSMsg.TIdlePart.starReward",6,5,2,false,0,13,3)
F59D=F(2,"idleReward",".CSMsg.TIdlePart.idleReward",7,6,2,false,0,13,3)
F60D=F(2,"starNum",".CSMsg.TIdlePart.starNum",8,7,2,false,0,13,3)
F61D=F(2,"idleStar",".CSMsg.TIdlePart.idleStar",9,8,3,false,{},13,3)
F62D=F(2,"giftIdleReward",".CSMsg.TIdlePart.giftIdleReward",10,9,2,false,0,13,3)
F63D=F(2,"giftStarReward",".CSMsg.TIdlePart.giftStarReward",11,10,2,false,0,13,3)
F64D=F(2,"pals",".CSMsg.TIdlePart.pals",12,11,3,false,{},11,10)
F65D=F(2,"idleTime",".CSMsg.TIdlePart.idleTime",13,12,2,false,0,13,3)
M27G=D(1,"TIdlePart",".CSMsg.TIdlePart",false,{},{},nil,{})
F66D=F(2,"taskId",".CSMsg.TGuideData.taskId",1,0,2,false,0,13,3)
F67D=F(2,"achieveFlag",".CSMsg.TGuideData.achieveFlag",2,1,2,false,0,13,3)
M29G=D(1,"TGuideData",".CSMsg.TGuideData",false,{},{},nil,{})
F68D=F(2,"mapId",".CSMsg.TStagePlot.mapId",1,0,2,false,0,13,3)
F69D=F(2,"achieveStep",".CSMsg.TStagePlot.achieveStep",2,1,2,false,false,8,7)
M30G=D(1,"TStagePlot",".CSMsg.TStagePlot",false,{},{},nil,{})
F70D=F(2,"topicID",".CSMsg.TTopicData.topicID",1,0,2,false,0,13,3)
F71D=F(2,"data",".CSMsg.TTopicData.data",2,1,3,false,{},5,1)
M31G=D(1,"TTopicData",".CSMsg.TTopicData",false,{},{},nil,{})
F72D=F(2,"topicData",".CSMsg.TSubjectPart.topicData",1,0,3,false,{},11,10)
F73D=F(2,"guideData",".CSMsg.TSubjectPart.guideData",2,1,3,false,{},11,10)
F74D=F(2,"plotData",".CSMsg.TSubjectPart.plotData",3,2,3,false,{},11,10)
M32G=D(1,"TSubjectPart",".CSMsg.TSubjectPart",false,{},{},nil,{})
F75D=F(2,"taskId",".CSMsg.TPbTaskData.taskId",1,0,2,false,0,4,4)
F76D=F(2,"taskType",".CSMsg.TPbTaskData.taskType",2,1,1,false,0,13,3)
F77D=F(2,"status",".CSMsg.TPbTaskData.status",3,2,2,false,false,8,7)
F78D=F(2,"exStatus",".CSMsg.TPbTaskData.exStatus",4,3,2,false,false,8,7)
F79D=F(2,"rate",".CSMsg.TPbTaskData.rate",5,4,1,false,0,4,4)
F80D=F(2,"endTime",".CSMsg.TPbTaskData.endTime",6,5,1,false,0,13,3)
F81D=F(2,"updateTime",".CSMsg.TPbTaskData.updateTime",7,6,1,false,0,13,3)
F82D=F(2,"moduleId",".CSMsg.TPbTaskData.moduleId",8,7,1,false,0,13,3)
F83D=F(2,"clearType",".CSMsg.TPbTaskData.clearType",9,8,1,false,0,13,3)
F84D=F(2,"activityId",".CSMsg.TPbTaskData.activityId",10,9,1,false,0,13,3)
F85D=F(2,"beginTime",".CSMsg.TPbTaskData.beginTime",11,10,1,false,0,13,3)
M33G=D(1,"TPbTaskData",".CSMsg.TPbTaskData",false,{},{},nil,{})
F86D=F(2,"taskData",".CSMsg.TPbTaskPartData.taskData",1,0,3,false,{},11,10)
M34G=D(1,"TPbTaskPartData",".CSMsg.TPbTaskPartData",false,{},{},nil,{})
F87D=F(2,"titleid",".CSMsg.TCustomTitleContext.titleid",1,0,2,false,0,5,1)
F88D=F(2,"name",".CSMsg.TCustomTitleContext.name",2,1,2,false,"",9,9)
F89D=F(2,"backgroudidx",".CSMsg.TCustomTitleContext.backgroudidx",3,2,1,false,0,5,1)
F90D=F(2,"propidx1",".CSMsg.TCustomTitleContext.propidx1",4,3,1,false,0,5,1)
F91D=F(2,"propidx2",".CSMsg.TCustomTitleContext.propidx2",5,4,1,false,0,5,1)
F92D=F(2,"extraData",".CSMsg.TCustomTitleContext.extraData",6,5,1,false,"",9,9)
M35G=D(1,"TCustomTitleContext",".CSMsg.TCustomTitleContext",false,{},{},nil,{})
F93D=F(2,"titles",".CSMsg.TCustomTitleData.titles",1,0,3,false,{},11,10)
M36G=D(1,"TCustomTitleData",".CSMsg.TCustomTitleData",false,{},{},nil,{})
F94D=F(2,"faceDatas",".CSMsg.TCustomFaceContext.faceDatas",1,0,3,false,{},11,10)
F95D=F(2,"customFaceTime",".CSMsg.TCustomFaceContext.customFaceTime",2,1,1,false,0,13,3)
F96D=F(2,"customFaceFlag",".CSMsg.TCustomFaceContext.customFaceFlag",3,2,1,false,0,13,3)
M37G=D(1,"TCustomFaceContext",".CSMsg.TCustomFaceContext",false,{},{},nil,{})
F97D=F(2,"activeFaceID",".CSMsg.TFaceDataPart.activeFaceID",1,0,3,false,{},5,1)
F98D=F(2,"bModifyName",".CSMsg.TFaceDataPart.bModifyName",2,1,2,false,false,8,7)
F99D=F(2,"titledata",".CSMsg.TFaceDataPart.titledata",3,2,1,false,nil,11,10)
F100D=F(2,"nModifyNameTime",".CSMsg.TFaceDataPart.nModifyNameTime",4,3,2,false,0,5,1)
F101D=F(2,"nModifySexTime",".CSMsg.TFaceDataPart.nModifySexTime",5,4,2,false,0,5,1)
F102D=F(2,"customFace",".CSMsg.TFaceDataPart.customFace",6,5,1,false,nil,11,10)
M39G=D(1,"TFaceDataPart",".CSMsg.TFaceDataPart",false,{},{},nil,{})
F103D=F(2,"techInfoArr",".CSMsg.TAllianceTechPart.techInfoArr",1,0,3,false,{},11,10)
F104D=F(2,"millInfo",".CSMsg.TAllianceTechPart.millInfo",2,1,2,false,nil,11,10)
M40G=D(1,"TAllianceTechPart",".CSMsg.TAllianceTechPart",false,{},{},nil,{})
F105D=F(2,"baseLv1",".CSMsg.THaloDataPart.baseLv1",1,0,2,false,0,5,1)
F106D=F(2,"baseLv2",".CSMsg.THaloDataPart.baseLv2",2,1,2,false,0,5,1)
F107D=F(2,"baseLv3",".CSMsg.THaloDataPart.baseLv3",3,2,2,false,0,5,1)
F108D=F(2,"baseLv4",".CSMsg.THaloDataPart.baseLv4",4,3,2,false,0,5,1)
F109D=F(2,"baseLv5",".CSMsg.THaloDataPart.baseLv5",5,4,2,false,0,5,1)
F110D=F(2,"baseLv6",".CSMsg.THaloDataPart.baseLv6",6,5,2,false,0,5,1)
F111D=F(2,"activeHalo",".CSMsg.THaloDataPart.activeHalo",7,6,3,false,{},5,1)
M41G=D(1,"THaloDataPart",".CSMsg.THaloDataPart",false,{},{},nil,{})
F112D=F(2,"soilNo",".CSMsg.TFramSoil.soilNo",1,0,2,false,0,5,1)
F113D=F(2,"bLock",".CSMsg.TFramSoil.bLock",2,1,2,false,false,8,7)
F114D=F(2,"botanyId",".CSMsg.TFramSoil.botanyId",3,2,1,false,0,5,1)
F115D=F(2,"botanyLv",".CSMsg.TFramSoil.botanyLv",4,3,1,false,0,5,1)
F116D=F(2,"source",".CSMsg.TFramSoil.source",5,4,1,false,0,5,1)
F117D=F(2,"lastRemainTime",".CSMsg.TFramSoil.lastRemainTime",6,5,1,false,0,5,1)
F118D=F(2,"lastRemainSource",".CSMsg.TFramSoil.lastRemainSource",7,6,1,false,0,5,1)
F119D=F(2,"damageLv",".CSMsg.TFramSoil.damageLv",8,7,1,false,0,5,1)
M42G=D(1,"TFramSoil",".CSMsg.TFramSoil",false,{},{},nil,{})
F120D=F(2,"dwScore",".CSMsg.TFramAchvData.dwScore",1,0,2,false,0,13,3)
F121D=F(2,"dwGainFlag1",".CSMsg.TFramAchvData.dwGainFlag1",2,1,2,false,0,13,3)
F122D=F(2,"dwGainFlag2",".CSMsg.TFramAchvData.dwGainFlag2",3,2,2,false,0,13,3)
F123D=F(2,"dwUsedEnergy",".CSMsg.TFramAchvData.dwUsedEnergy",4,3,2,false,0,13,3)
M43G=D(1,"TFramAchvData",".CSMsg.TFramAchvData",false,{},{},nil,{})
F124D=F(2,"mainLv",".CSMsg.TFramDataPart.mainLv",1,0,2,false,0,5,1)
F125D=F(2,"soil",".CSMsg.TFramDataPart.soil",2,1,3,false,{},11,10)
F126D=F(2,"energy",".CSMsg.TFramDataPart.energy",3,2,2,false,0,13,3)
F127D=F(2,"addEnergyRTime",".CSMsg.TFramDataPart.addEnergyRTime",4,3,1,false,0,13,3)
F128D=F(2,"oAchvData",".CSMsg.TFramDataPart.oAchvData",5,4,1,false,nil,11,10)
F129D=F(2,"dwBuyTimes",".CSMsg.TFramDataPart.dwBuyTimes",6,5,1,false,0,13,3)
M44G=D(1,"TFramDataPart",".CSMsg.TFramDataPart",false,{},{},nil,{})
F130D=F(2,"bLock",".CSMsg.TWeaponPart.bLock",1,0,2,false,false,8,7)
F131D=F(2,"partType",".CSMsg.TWeaponPart.partType",2,1,2,false,0,5,1)
F132D=F(2,"nLv",".CSMsg.TWeaponPart.nLv",3,2,2,false,0,5,1)
M45G=D(1,"TWeaponPart",".CSMsg.TWeaponPart",false,{},{},nil,{})
F133D=F(2,"weaponId",".CSMsg.TUnlockWeapon.weaponId",1,0,2,false,0,5,1)
F134D=F(2,"bActive",".CSMsg.TUnlockWeapon.bActive",2,1,2,false,false,8,7)
F135D=F(2,"part",".CSMsg.TUnlockWeapon.part",3,2,3,false,{},11,10)
M46G=D(1,"TUnlockWeapon",".CSMsg.TUnlockWeapon",false,{},{},nil,{})
F136D=F(2,"weapon",".CSMsg.TWeaponDataPart.weapon",1,0,3,false,{},11,10)
M47G=D(1,"TWeaponDataPart",".CSMsg.TWeaponDataPart",false,{},{},nil,{})
F137D=F(2,"propId",".CSMsg.TSoulLinkPropUpdata.propId",1,0,2,false,0,5,1)
F138D=F(2,"propValue",".CSMsg.TSoulLinkPropUpdata.propValue",2,1,2,false,0,5,1)
M48G=D(1,"TSoulLinkPropUpdata",".CSMsg.TSoulLinkPropUpdata",false,{},{},nil,{})
F139D=F(2,"slotId",".CSMsg.SoulLinkSlotStat.slotId",1,0,2,false,0,5,1)
F140D=F(2,"isCooling",".CSMsg.SoulLinkSlotStat.isCooling",2,1,2,false,false,8,7)
F141D=F(2,"coolingTime",".CSMsg.SoulLinkSlotStat.coolingTime",3,2,1,false,0,5,1)
F142D=F(2,"slotIsEmpty",".CSMsg.SoulLinkSlotStat.slotIsEmpty",4,3,1,false,false,8,7)
F143D=F(2,"heroSid",".CSMsg.SoulLinkSlotStat.heroSid",5,4,1,false,0,5,1)
F144D=F(2,"heroLV",".CSMsg.SoulLinkSlotStat.heroLV",6,5,1,false,0,5,1)
M49G=D(1,"SoulLinkSlotStat",".CSMsg.SoulLinkSlotStat",false,{},{},nil,{})
F145D=F(2,"flamenSidGroup",".CSMsg.TSoulLinkMoudleDataPart.flamenSidGroup",1,0,3,false,{},5,1)
F146D=F(2,"slotStat",".CSMsg.TSoulLinkMoudleDataPart.slotStat",2,1,3,false,{},11,10)
F147D=F(2,"slotProp",".CSMsg.TSoulLinkMoudleDataPart.slotProp",3,2,3,false,{},11,10)
M50G=D(1,"TSoulLinkMoudleDataPart",".CSMsg.TSoulLinkMoudleDataPart",false,{},{},nil,{})
F148D=F(2,"techLv",".CSMsg.BattleshipTechData.techLv",1,0,3,false,{},13,3)
M51G=D(1,"BattleshipTechData",".CSMsg.BattleshipTechData",false,{},{},nil,{})
F149D=F(2,"techLvData",".CSMsg.TBattleshipTechDataPart.techLvData",1,0,3,false,{},11,10)
F150D=F(2,"techResetTimes",".CSMsg.TBattleshipTechDataPart.techResetTimes",2,1,2,false,0,13,3)
M52G=D(1,"TBattleshipTechDataPart",".CSMsg.TBattleshipTechDataPart",false,{},{},nil,{})
F151D=F(2,"id",".CSMsg.TResourcesPlanetPlanetData.id",1,0,2,false,0,13,3)
F152D=F(2,"starPirates",".CSMsg.TResourcesPlanetPlanetData.starPirates",2,1,1,false,0,13,3)
F153D=F(2,"dispatchHero",".CSMsg.TResourcesPlanetPlanetData.dispatchHero",3,2,1,false,0,13,3)
F154D=F(2,"eventid",".CSMsg.TResourcesPlanetPlanetData.eventid",4,3,1,false,0,13,3)
F155D=F(2,"masterID",".CSMsg.TResourcesPlanetPlanetData.masterID",5,4,1,false,0,13,3)
F156D=F(2,"battleshiplv",".CSMsg.TResourcesPlanetPlanetData.battleshiplv",6,5,3,false,{},13,3)
F157D=F(2,"endExploreTime",".CSMsg.TResourcesPlanetPlanetData.endExploreTime",7,6,1,false,0,13,3)
F158D=F(2,"hasGetted",".CSMsg.TResourcesPlanetPlanetData.hasGetted",8,7,1,false,false,8,7)
M53G=D(1,"TResourcesPlanetPlanetData",".CSMsg.TResourcesPlanetPlanetData",false,{},{},nil,{})
F159D=F(2,"id",".CSMsg.TVeinPlanetData.id",1,0,2,false,0,13,3)
F160D=F(2,"startOutPutTime",".CSMsg.TVeinPlanetData.startOutPutTime",2,1,2,false,0,13,3)
F161D=F(2,"lastOutputTime",".CSMsg.TVeinPlanetData.lastOutputTime",3,2,2,false,0,13,3)
M54G=D(1,"TVeinPlanetData",".CSMsg.TVeinPlanetData",false,{},{},nil,{})
F162D=F(2,"galaxyid",".CSMsg.TGalaxyPlanetData.galaxyid",1,0,2,false,0,13,3)
F163D=F(2,"positionid",".CSMsg.TGalaxyPlanetData.positionid",2,1,3,false,{},13,3)
F164D=F(2,"veinPlanetID",".CSMsg.TGalaxyPlanetData.veinPlanetID",3,2,3,false,{},11,10)
F165D=F(2,"resourcesPlanetID",".CSMsg.TGalaxyPlanetData.resourcesPlanetID",4,3,3,false,{},11,10)
M55G=D(1,"TGalaxyPlanetData",".CSMsg.TGalaxyPlanetData",false,{},{},nil,{})
F166D=F(2,"prop",".CSMsg.TSpaceExplorationProp.prop",1,0,2,false,nil,14,8)
F167D=F(2,"value",".CSMsg.TSpaceExplorationProp.value",2,1,2,false,0,5,1)
M56G=D(1,"TSpaceExplorationProp",".CSMsg.TSpaceExplorationProp",false,{},{},nil,{})
F168D=F(2,"endTime",".CSMsg.TSpaceExplorationDataPart.endTime",1,0,2,false,0,13,3)
F169D=F(2,"battleshipLv",".CSMsg.TSpaceExplorationDataPart.battleshipLv",2,1,3,false,{},13,3)
F170D=F(2,"activationGalaxy",".CSMsg.TSpaceExplorationDataPart.activationGalaxy",3,2,3,false,{},13,3)
F171D=F(2,"ExploreGalaxyID",".CSMsg.TSpaceExplorationDataPart.ExploreGalaxyID",4,3,1,false,0,13,3)
F172D=F(2,"ExploreGalaxyMapID",".CSMsg.TSpaceExplorationDataPart.ExploreGalaxyMapID",5,4,1,false,0,13,3)
F173D=F(2,"galaxyPlanetData",".CSMsg.TSpaceExplorationDataPart.galaxyPlanetData",6,5,1,false,nil,11,10)
F174D=F(2,"prop",".CSMsg.TSpaceExplorationDataPart.prop",7,6,3,false,{},11,10)
M58G=D(1,"TSpaceExplorationDataPart",".CSMsg.TSpaceExplorationDataPart",false,{},{},nil,{})
F175D=F(2,"isOpen",".CSMsg.TStarTempleDataPart.isOpen",1,0,2,false,0,13,3)
F176D=F(2,"endTime",".CSMsg.TStarTempleDataPart.endTime",2,1,2,false,0,13,3)
F177D=F(2,"hasRed",".CSMsg.TStarTempleDataPart.hasRed",3,2,2,false,false,8,7)
F178D=F(2,"beginTime",".CSMsg.TStarTempleDataPart.beginTime",4,3,1,false,0,13,3)
M59G=D(1,"TStarTempleDataPart",".CSMsg.TStarTempleDataPart",false,{},{},nil,{})
F179D=F(2,"enType",".CSMsg.TMSG_PROP_PERSONOFFLINE_NTF.TProp_PersonOffline.enType",1,0,2,false,nil,14,8)
F180D=F(2,"nValue",".CSMsg.TMSG_PROP_PERSONOFFLINE_NTF.TProp_PersonOffline.nValue",2,1,1,false,0,3,2)
F181D=F(2,"strValue",".CSMsg.TMSG_PROP_PERSONOFFLINE_NTF.TProp_PersonOffline.strValue",3,2,1,false,"",9,9)
M61G=D(1,"TProp_PersonOffline",".CSMsg.TMSG_PROP_PERSONOFFLINE_NTF.TProp_PersonOffline",false,{},{},nil,{})
F182D=F(2,"props",".CSMsg.TMSG_PROP_PERSONOFFLINE_NTF.props",1,0,3,false,{},11,10)
M60G=D(1,"TMSG_PROP_PERSONOFFLINE_NTF",".CSMsg.TMSG_PROP_PERSONOFFLINE_NTF",false,nil,{},nil,{})
F183D=F(2,"prop",".CSMsg.TMSG_PROP_CREATEENTITY_NTF.prop",1,0,3,false,{},11,10)
F184D=F(2,"name",".CSMsg.TMSG_PROP_CREATEENTITY_NTF.name",2,1,2,false,"",9,9)
F185D=F(2,"partData",".CSMsg.TMSG_PROP_CREATEENTITY_NTF.partData",3,2,3,false,{},11,10)
F186D=F(2,"roleCreateTime",".CSMsg.TMSG_PROP_CREATEENTITY_NTF.roleCreateTime",4,3,1,false,0,13,3)
F187D=F(2,"openSvrTime",".CSMsg.TMSG_PROP_CREATEENTITY_NTF.openSvrTime",5,4,1,false,0,13,3)
M63G=D(1,"TMSG_PROP_CREATEENTITY_NTF",".CSMsg.TMSG_PROP_CREATEENTITY_NTF",false,{},{},nil,{})
F188D=F(2,"sid",".CSMsg.TEntityData.sid",1,0,2,false,0,5,1)
F189D=F(2,"enType",".CSMsg.TEntityData.enType",2,1,2,false,nil,14,8)
F190D=F(2,"prop",".CSMsg.TEntityData.prop",3,2,3,false,{},11,10)
F191D=F(2,"name",".CSMsg.TEntityData.name",4,3,1,false,"",9,9)
M64G=D(1,"TEntityData",".CSMsg.TEntityData",false,{},{},nil,{})
F192D=F(2,"entitys",".CSMsg.TMSG_PROP_UPDATE_NTF.entitys",1,0,3,false,{},11,10)
F193D=F(2,"token",".CSMsg.TMSG_PROP_UPDATE_NTF.token",2,1,1,false,"",9,9)
F194D=F(2,"timeStr",".CSMsg.TMSG_PROP_UPDATE_NTF.timeStr",3,2,1,false,"",9,9)
F195D=F(2,"token1",".CSMsg.TMSG_PROP_UPDATE_NTF.token1",4,3,1,false,"",9,9)
F196D=F(2,"timeStr1",".CSMsg.TMSG_PROP_UPDATE_NTF.timeStr1",5,4,1,false,"",9,9)
F197D=F(2,"blank",".CSMsg.TMSG_PROP_UPDATE_NTF.blank",6,5,1,false,0,5,1)
M66G=D(1,"TMSG_PROP_UPDATE_NTF",".CSMsg.TMSG_PROP_UPDATE_NTF",false,{},{},nil,{})
F198D=F(2,"props",".CSMsg.TMSG_PROP_UPDATE_NTFS.props",2,0,3,false,{},11,10)
M67G=D(1,"TMSG_PROP_UPDATE_NTFS",".CSMsg.TMSG_PROP_UPDATE_NTFS",false,{},{},nil,{})
F199D=F(2,"goodsData",".CSMsg.TMSG_ROGUE_ENTITY_CREATE_NTF.goodsData",2,0,3,false,{},11,10)
F200D=F(2,"palData",".CSMsg.TMSG_ROGUE_ENTITY_CREATE_NTF.palData",3,1,3,false,{},11,10)
F201D=F(2,"skepData",".CSMsg.TMSG_ROGUE_ENTITY_CREATE_NTF.skepData",4,2,3,false,{},11,10)
F202D=F(2,"bNew",".CSMsg.TMSG_ROGUE_ENTITY_CREATE_NTF.bNew",5,3,2,false,false,8,7)
F203D=F(2,"palTrialData",".CSMsg.TMSG_ROGUE_ENTITY_CREATE_NTF.palTrialData",6,4,3,false,{},11,10)
M68G=D(1,"TMSG_ROGUE_ENTITY_CREATE_NTF",".CSMsg.TMSG_ROGUE_ENTITY_CREATE_NTF",false,{},{},nil,{})
F204D=F(2,"eType",".CSMsg.TMSG_ROGUE_ENTITY_DEL_NTF.eType",1,0,2,false,nil,14,8)
F205D=F(2,"sid",".CSMsg.TMSG_ROGUE_ENTITY_DEL_NTF.sid",2,1,3,false,{},5,1)
F206D=F(2,"goodsId",".CSMsg.TMSG_ROGUE_ENTITY_DEL_NTF.goodsId",3,2,3,false,{},5,1)
M69G=D(1,"TMSG_ROGUE_ENTITY_DEL_NTF",".CSMsg.TMSG_ROGUE_ENTITY_DEL_NTF",false,{},{},nil,{})
F207D=F(2,"topicName",".CSMsg.TMSG_PROP_UPDATE_TOPIC_NTF.topicName",1,0,2,false,0,5,1)
F208D=F(2,"topicKey",".CSMsg.TMSG_PROP_UPDATE_TOPIC_NTF.topicKey",2,1,2,false,0,5,1)
F209D=F(2,"value",".CSMsg.TMSG_PROP_UPDATE_TOPIC_NTF.value",3,2,2,false,0,5,1)
M70G=D(1,"TMSG_PROP_UPDATE_TOPIC_NTF",".CSMsg.TMSG_PROP_UPDATE_TOPIC_NTF",false,{},{},nil,{})
F210D=F(2,"topicName",".CSMsg.TopicData.topicName",1,0,2,false,0,5,1)
F211D=F(2,"topicKey",".CSMsg.TopicData.topicKey",2,1,2,false,0,5,1)
F212D=F(2,"value",".CSMsg.TopicData.value",3,2,2,false,0,5,1)
M71G=D(1,"TopicData",".CSMsg.TopicData",false,{},{},nil,{})
F213D=F(2,"topicdata",".CSMsg.TMSG_PROP_UPDATE_TOPICS_NTF.topicdata",1,0,3,false,{},11,10)
M72G=D(1,"TMSG_PROP_UPDATE_TOPICS_NTF",".CSMsg.TMSG_PROP_UPDATE_TOPICS_NTF",false,{},{},nil,{})
F214D=F(2,"timestamp",".CSMsg.TMSG_ZONE_SERVER_TIMESTAMP_NTF.timestamp",1,0,2,false,0,5,1)
M73G=D(1,"TMSG_ZONE_SERVER_TIMESTAMP_NTF",".CSMsg.TMSG_ZONE_SERVER_TIMESTAMP_NTF",false,{},{},nil,{})
F215D=F(2,"heroId",".CSMsg.tRewardRandData.heroId",1,0,2,false,0,5,1)
F216D=F(2,"count",".CSMsg.tRewardRandData.count",2,1,2,false,0,5,1)
F217D=F(2,"fWeight",".CSMsg.tRewardRandData.fWeight",3,2,2,false,.0,2,6)
M74G=D(1,"tRewardRandData",".CSMsg.tRewardRandData",false,{},{},nil,{})
F218D=F(2,"heroId",".CSMsg.tTalentCardData.heroId",1,0,2,false,0,5,1)
F219D=F(2,"talenttype",".CSMsg.tTalentCardData.talenttype",2,1,2,false,0,5,1)
F220D=F(2,"gidx",".CSMsg.tTalentCardData.gidx",3,2,2,false,0,5,1)
F221D=F(2,"triggernum",".CSMsg.tTalentCardData.triggernum",4,3,2,false,0,5,1)
F222D=F(2,"btriggered",".CSMsg.tTalentCardData.btriggered",5,4,1,false,false,8,7)
M75G=D(1,"tTalentCardData",".CSMsg.tTalentCardData",false,{},{},nil,{})
F223D=F(2,"data",".CSMsg.tRewardRandPartData.data",1,0,3,false,{},11,10)
F224D=F(2,"talentcard",".CSMsg.tRewardRandPartData.talentcard",2,1,3,false,{},11,10)
F225D=F(2,"wishlist",".CSMsg.tRewardRandPartData.wishlist",3,2,3,false,{},5,1)
F226D=F(2,"wishcard",".CSMsg.tRewardRandPartData.wishcard",4,3,1,false,0,5,1)
F227D=F(2,"lastT2heroId",".CSMsg.tRewardRandPartData.lastT2heroId",5,4,1,false,0,5,1)
M76G=D(1,"tRewardRandPartData",".CSMsg.tRewardRandPartData",false,{},{},nil,{})
F228D=F(2,"stage",".CSMsg.tDynamicDiffiPartData.stage",1,0,2,false,0,5,1)
F229D=F(2,"failNum",".CSMsg.tDynamicDiffiPartData.failNum",2,1,2,false,0,5,1)
F230D=F(2,"reduceDiffiStartTime",".CSMsg.tDynamicDiffiPartData.reduceDiffiStartTime",3,2,2,false,0,5,1)
F231D=F(2,"promoteDiffiCount",".CSMsg.tDynamicDiffiPartData.promoteDiffiCount",4,3,2,false,0,5,1)
F232D=F(2,"stalv",".CSMsg.tDynamicDiffiPartData.stalv",5,4,1,false,0,5,1)
M77G=D(1,"tDynamicDiffiPartData",".CSMsg.tDynamicDiffiPartData",false,{},{},nil,{})
F233D=F(2,"wday",".CSMsg.tSpaceDominatorDataPart.wday",1,0,2,false,0,5,1)
F234D=F(2,"challengenum",".CSMsg.tSpaceDominatorDataPart.challengenum",2,1,2,false,0,5,1)
F235D=F(2,"bhavesettle",".CSMsg.tSpaceDominatorDataPart.bhavesettle",3,2,2,false,false,8,7)
M78G=D(1,"tSpaceDominatorDataPart",".CSMsg.tSpaceDominatorDataPart",false,{},{},nil,{})
F236D=F(2,"gradeid",".CSMsg.tGoldHandBuyRecord.gradeid",1,0,2,false,0,5,1)
F237D=F(2,"cost",".CSMsg.tGoldHandBuyRecord.cost",2,1,2,false,0,5,1)
F238D=F(2,"golds",".CSMsg.tGoldHandBuyRecord.golds",3,2,2,false,0,13,3)
F239D=F(2,"rate",".CSMsg.tGoldHandBuyRecord.rate",4,3,2,false,0,5,1)
M79G=D(1,"tGoldHandBuyRecord",".CSMsg.tGoldHandBuyRecord",false,{},{},nil,{})
F240D=F(2,"refreshtime",".CSMsg.tGoldHandData.refreshtime",1,0,2,false,0,13,3)
F241D=F(2,"buyNum",".CSMsg.tGoldHandData.buyNum",2,1,2,false,0,5,1)
F242D=F(2,"gradeid",".CSMsg.tGoldHandData.gradeid",3,2,2,false,0,5,1)
F243D=F(2,"record",".CSMsg.tGoldHandData.record",4,3,1,false,nil,11,10)
F244D=F(2,"yday",".CSMsg.tGoldHandData.yday",5,4,1,false,0,5,1)
F245D=F(2,"medalBuyNum",".CSMsg.tGoldHandData.medalBuyNum",6,5,1,false,0,5,1)
M80G=D(1,"tGoldHandData",".CSMsg.tGoldHandData",false,{},{},nil,{})
F246D=F(2,"guaranttype",".CSMsg.tSupremeBoxGuaranteeData.guaranttype",1,0,2,false,0,5,1)
F247D=F(2,"triggernum",".CSMsg.tSupremeBoxGuaranteeData.triggernum",2,1,2,false,0,13,3)
M81G=D(1,"tSupremeBoxGuaranteeData",".CSMsg.tSupremeBoxGuaranteeData",false,{},{},nil,{})
F248D=F(2,"data",".CSMsg.tSupremeBoxGuarantee.data",1,0,3,false,{},11,10)
M82G=D(1,"tSupremeBoxGuarantee",".CSMsg.tSupremeBoxGuarantee",false,{},{},nil,{})
F249D=F(2,"rewardData",".CSMsg.tRolePartCommonData.rewardData",1,0,1,false,nil,11,10)
F250D=F(2,"dynamicDiffi",".CSMsg.tRolePartCommonData.dynamicDiffi",2,1,1,false,nil,11,10)
F251D=F(2,"sddata",".CSMsg.tRolePartCommonData.sddata",3,2,1,false,nil,11,10)
F252D=F(2,"ghdata",".CSMsg.tRolePartCommonData.ghdata",4,3,1,false,nil,11,10)
F253D=F(2,"sboxdata",".CSMsg.tRolePartCommonData.sboxdata",5,4,1,false,nil,11,10)
M83G=D(1,"tRolePartCommonData",".CSMsg.tRolePartCommonData",false,{},{},nil,{})
F254D=F(2,"loginKey",".CSMsg.TMSG_ROLE_LOGIN_KEY_NTF.loginKey",1,0,2,false,"",9,9)
F255D=F(2,"time1",".CSMsg.TMSG_ROLE_LOGIN_KEY_NTF.time1",2,1,2,false,0,13,3)
F256D=F(2,"worldid",".CSMsg.TMSG_ROLE_LOGIN_KEY_NTF.worldid",3,2,1,false,0,13,3)
M84G=D(1,"TMSG_ROLE_LOGIN_KEY_NTF",".CSMsg.TMSG_ROLE_LOGIN_KEY_NTF",false,{},{},nil,{})
F257D=F(2,"id",".CSMsg.TBuyData.id",1,0,2,false,0,13,3)
F258D=F(2,"cnt",".CSMsg.TBuyData.cnt",2,1,2,false,0,13,3)
M85G=D(1,"TBuyData",".CSMsg.TBuyData",false,{},{},nil,{})
F259D=F(2,"oBuyData",".CSMsg.TSevenChallengePartData.oBuyData",1,0,3,false,{},11,10)
M86G=D(1,"TSevenChallengePartData",".CSMsg.TSevenChallengePartData",false,{},{},nil,{})
F260D=F(2,"data",".CSMsg.TMSG_SEVENCHALLENGE_UPDATE_NTF.data",1,0,2,false,nil,11,10)
M87G=D(1,"TMSG_SEVENCHALLENGE_UPDATE_NTF",".CSMsg.TMSG_SEVENCHALLENGE_UPDATE_NTF",false,{},{},nil,{})
F261D=F(2,"dwRoleID",".CSMsg.TINVITED_INFO.dwRoleID",1,0,2,false,0,13,3)
F262D=F(2,"dwLv",".CSMsg.TINVITED_INFO.dwLv",2,1,2,false,0,13,3)
F263D=F(2,"dwCheckPoint",".CSMsg.TINVITED_INFO.dwCheckPoint",3,2,2,false,0,13,3)
M88G=D(1,"TINVITED_INFO",".CSMsg.TINVITED_INFO",false,{},{},nil,{})
F264D=F(2,"dwLineUpID",".CSMsg.TCfgLineUpData.dwLineUpID",1,0,2,false,0,13,3)
F265D=F(2,"dwTime",".CSMsg.TCfgLineUpData.dwTime",2,1,2,false,0,13,3)
F266D=F(2,"dwHeroIDs",".CSMsg.TCfgLineUpData.dwHeroIDs",3,2,3,false,{},13,3)
M89G=D(1,"TCfgLineUpData",".CSMsg.TCfgLineUpData",false,{},{},nil,{})
F267D=F(2,"dwDBID",".CSMsg.TCstLineUpData.dwDBID",1,0,2,false,0,13,3)
F268D=F(2,"dwTime",".CSMsg.TCstLineUpData.dwTime",2,1,2,false,0,13,3)
F269D=F(2,"name",".CSMsg.TCstLineUpData.name",3,2,2,false,"",9,9)
F270D=F(2,"dwHeroIDs",".CSMsg.TCstLineUpData.dwHeroIDs",4,3,3,false,{},13,3)
M90G=D(1,"TCstLineUpData",".CSMsg.TCstLineUpData",false,{},{},nil,{})
F271D=F(2,"szEmail",".CSMsg.TMSG_OPERATIVE_PART_DATA.szEmail",1,0,2,false,"",9,9)
F272D=F(2,"szExchangeCode",".CSMsg.TMSG_OPERATIVE_PART_DATA.szExchangeCode",2,1,2,false,"",9,9)
F273D=F(2,"nMailPrizeFlag",".CSMsg.TMSG_OPERATIVE_PART_DATA.nMailPrizeFlag",3,2,2,false,0,13,3)
F274D=F(2,"dwInviterDBID",".CSMsg.TMSG_OPERATIVE_PART_DATA.dwInviterDBID",4,3,2,false,0,13,3)
F275D=F(2,"dwInvitPrizeFlag",".CSMsg.TMSG_OPERATIVE_PART_DATA.dwInvitPrizeFlag",5,4,2,false,0,13,3)
F276D=F(2,"bySendInvitedMail",".CSMsg.TMSG_OPERATIVE_PART_DATA.bySendInvitedMail",6,5,2,false,0,13,3)
F277D=F(2,"infos",".CSMsg.TMSG_OPERATIVE_PART_DATA.infos",7,6,3,false,{},11,10)
F278D=F(2,"dwShareStartTime",".CSMsg.TMSG_OPERATIVE_PART_DATA.dwShareStartTime",8,7,2,false,0,13,3)
F279D=F(2,"cfgLineUp",".CSMsg.TMSG_OPERATIVE_PART_DATA.cfgLineUp",9,8,3,false,{},11,10)
F280D=F(2,"cstLineUp",".CSMsg.TMSG_OPERATIVE_PART_DATA.cstLineUp",10,9,3,false,{},11,10)
M91G=D(1,"TMSG_OPERATIVE_PART_DATA",".CSMsg.TMSG_OPERATIVE_PART_DATA",false,{},{},nil,{})
M92G=D(1,"TSubGiftPartData",".CSMsg.TSubGiftPartData",false,{},{},{},{})
F281D=F(2,"tbs",".CSMsg.TMSG_BATTLE_TBS_NTF.tbs",1,0,2,false,"",12,9)
M93G=D(1,"TMSG_BATTLE_TBS_NTF",".CSMsg.TMSG_BATTLE_TBS_NTF",false,{},{},nil,{})
M94G=D(1,"TMSG_PLAYER_LOGIN_FINISH_NOTIFY",".CSMsg.TMSG_PLAYER_LOGIN_FINISH_NOTIFY",false,{},{},{},{})
F282D=F(2,"levelID",".CSMsg.TPickTheRouteArchivedData.levelID",1,0,1,false,0,5,1)
F283D=F(2,"eventChooseID",".CSMsg.TPickTheRouteArchivedData.eventChooseID",2,1,3,false,{},5,1)
F284D=F(2,"eventRewardID",".CSMsg.TPickTheRouteArchivedData.eventRewardID",3,2,3,false,{},5,1)
M95G=D(1,"TPickTheRouteArchivedData",".CSMsg.TPickTheRouteArchivedData",false,{},{},nil,{})
F285D=F(2,"stLevelArchived",".CSMsg.TPickTheRoute.stLevelArchived",1,0,3,false,{},11,10)
F286D=F(2,"activityID",".CSMsg.TPickTheRoute.activityID",2,1,1,false,0,5,1)
M96G=D(1,"TPickTheRoute",".CSMsg.TPickTheRoute",false,{},{},nil,{})
F287D=F(2,"treasureID",".CSMsg.TTreasureData.treasureID",1,0,2,false,0,5,1)
F288D=F(2,"enhanceLv",".CSMsg.TTreasureData.enhanceLv",2,1,2,false,0,5,1)
F289D=F(2,"expiredTime",".CSMsg.TTreasureData.expiredTime",3,2,2,false,0,5,1)
M97G=D(1,"TTreasureData",".CSMsg.TTreasureData",false,{},{},nil,{})
F290D=F(2,"makerID",".CSMsg.TMakerData.makerID",1,0,2,false,0,5,1)
F291D=F(2,"hammerEndTime",".CSMsg.TMakerData.hammerEndTime",2,1,2,false,0,5,1)
F292D=F(2,"expiredTime",".CSMsg.TMakerData.expiredTime",3,2,2,false,0,5,1)
F293D=F(2,"specialHammer",".CSMsg.TMakerData.specialHammer",4,3,2,false,0,5,1)
F294D=F(2,"startTime",".CSMsg.TMakerData.startTime",5,4,2,false,0,5,1)
F295D=F(2,"coinID",".CSMsg.TMakerData.coinID",6,5,1,false,0,5,1)
M98G=D(1,"TMakerData",".CSMsg.TMakerData",false,{},{},nil,{})
F296D=F(2,"treasureData",".CSMsg.TTreasureRarePart.treasureData",1,0,3,false,{},11,10)
F297D=F(2,"makerData",".CSMsg.TTreasureRarePart.makerData",2,1,3,false,{},11,10)
M99G=D(1,"TTreasureRarePart",".CSMsg.TTreasureRarePart",false,{},{},nil,{})

E1M.values = {V1M,V2M,V3M,V4M}
E2M.values = {V5M,V6M,V7M,V8M,V9M,V10M,V11M,V12M,V13M,V14M,V15M,V16M,V17M,V18M,V19M,V20M,V21M,V22M,V23M,V24M,V25M,V26M,V27M,V28M,V29M,V30M,V31M,V32M,V33M,V34M,V35M,V36M,V37M,V38M,V39M,V40M,V41M,V42M,V43M,V44M,V45M,V46M,V47M,V48M,V49M,V50M,V51M,V52M,V53M,V54M,V55M,V56M,V57M,V58M,V59M,V60M,V61M,V62M,V63M,V64M,V65M,V66M,V67M,V68M,V69M,V70M,V71M,V72M,V73M,V74M,V75M,V76M,V77M,V78M,V79M,V80M,V81M,V82M,V83M,V84M,V85M,V86M,V87M,V88M,V89M,V90M,V91M,V92M,V93M,V94M,V95M,V96M,V97M,V98M,V99M,V100M,V101M,V102M,V103M,V104M,V105M,V106M,V107M,V108M,V109M,V110M,V111M,V112M,V113M,V114M,V115M,V116M,V117M,V118M,V119M,V120M,V121M,V122M,V123M,V124M,V125M,V126M,V127M,V128M,V129M,V130M,V131M,V132M,V133M,V134M,V135M,V136M,V137M,V138M,V139M,V140M,V141M,V142M,V143M,V144M,V145M,V146M,V147M,V148M,V149M,V150M,V151M,V152M,V153M,V154M,V155M,V156M,V157M,V158M,V159M,V160M,V161M,V162M,V163M,V164M,V165M,V166M,V167M,V168M,V169M,V170M,V171M,V172M,V173M}
E3M.values = {V174M,V175M,V176M,V177M,V178M,V179M,V180M,V181M,V182M,V183M,V184M,V185M,V186M,V187M,V188M,V189M,V190M,V191M,V192M,V193M,V194M,V195M,V196M,V197M,V198M,V199M,V200M,V201M,V202M,V203M,V204M,V205M,V206M,V207M,V208M,V209M,V210M,V211M,V212M}
E4M.values = {V213M,V214M,V215M,V216M,V217M,V218M,V219M,V220M,V221M,V222M,V223M,V224M,V225M,V226M,V227M,V228M,V229M,V230M,V231M,V232M,V233M,V234M,V235M,V236M,V237M,V238M,V239M,V240M,V241M,V242M,V243M,V244M,V245M,V246M,V247M,V248M,V249M,V250M,V251M,V252M,V253M,V254M,V255M,V256M,V257M,V258M,V259M,V260M,V261M,V262M,V263M,V264M,V265M,V266M,V267M,V268M,V269M,V270M,V271M,V272M,V273M,V274M,V275M,V276M,V277M,V278M,V279M,V280M,V281M,V282M,V283M,V284M,V285M,V286M,V287M,V288M,V289M,V290M,V291M,V292M,V293M,V294M,V295M,V296M,V297M,V298M,V299M,V300M}
E5M.values = {V301M,V302M,V303M,V304M,V305M,V306M,V307M,V308M,V309M,V310M,V311M,V312M,V313M,V314M,V315M,V316M,V317M,V318M,V319M,V320M,V321M,V322M,V323M,V324M,V325M,V326M,V327M,V328M,V329M,V330M,V331M,V332M,V333M,V334M,V335M,V336M,V337M,V338M,V339M,V340M,V341M,V342M,V343M,V344M,V345M,V346M,V347M,V348M,V349M,V350M,V351M,V352M,V353M,V354M,V355M,V356M,V357M,V358M,V359M,V360M,V361M,V362M,V363M,V364M,V365M,V366M,V367M,V368M,V369M,V370M,V371M,V372M,V373M,V374M,V375M,V376M,V377M,V378M,V379M,V380M,V381M,V382M,V383M,V384M,V385M,V386M,V387M,V388M,V389M,V390M,V391M,V392M,V393M,V394M,V395M,V396M,V397M,V398M,V399M,V400M,V401M,V402M,V403M,V404M,V405M,V406M,V407M,V408M,V409M,V410M,V411M,V412M,V413M,V414M,V415M,V416M,V417M,V418M,V419M,V420M,V421M,V422M,V423M}
E6M.values = {V424M,V425M,V426M,V427M,V428M,V429M,V430M,V431M,V432M,V433M,V434M,V435M,V436M,V437M,V438M,V439M,V440M,V441M,V442M,V443M,V444M,V445M,V446M,V447M,V448M,V449M,V450M,V451M,V452M,V453M,V454M,V455M,V456M,V457M,V458M,V459M,V460M,V461M,V462M,V463M,V464M,V465M,V466M,V467M,V468M,V469M,V470M,V471M,V472M,V473M,V474M,V475M,V476M,V477M}
E7M.values = {V478M,V479M,V480M,V481M,V482M,V483M,V484M,V485M,V486M,V487M,V488M,V489M,V490M,V491M,V492M,V493M,V494M,V495M,V496M,V497M,V498M,V499M,V500M,V501M,V502M,V503M,V504M,V505M,V506M,V507M,V508M,V509M,V510M,V511M,V512M,V513M,V514M,V515M,V516M,V517M,V518M,V519M,V520M,V521M,V522M,V523M,V524M,V525M,V526M,V527M,V528M,V529M,V530M,V531M,V532M,V533M,V534M,V535M,V536M,V537M,V538M,V539M,V540M,V541M,V542M,V543M,V544M,V545M,V546M,V547M,V548M,V549M,V550M,V551M,V552M,V553M,V554M,V555M,V556M,V557M,V558M,V559M,V560M,V561M,V562M,V563M,V564M,V565M,V566M,V567M,V568M,V569M,V570M,V571M,V572M}
E8M.values = {V573M,V574M}
E9M.values = {V575M,V576M,V577M,V578M,V579M,V580M,V581M,V582M,V583M,V584M,V585M,V586M,V587M,V588M,V589M,V590M,V591M,V592M,V593M,V594M,V595M,V596M,V597M,V598M,V599M,V600M,V601M,V602M,V603M,V604M,V605M}
E10M.values = {V606M,V607M,V608M,V609M,V610M,V611M,V612M,V613M,V614M}
E11M.values = {V615M,V616M,V617M,V618M,V619M,V620M,V621M,V622M,V623M,V624M,V625M,V626M,V627M,V628M,V629M,V630M,V631M,V632M,V633M,V634M}
E12M.values = {V635M,V636M,V637M,V638M,V639M,V640M,V641M,V642M,V643M,V644M,V645M}
E13M.values = {V646M,V647M,V648M,V649M,V650M}
E14M.values = {V651M,V652M,V653M,V654M,V655M}
E15M.values = {V656M,V657M,V658M,V659M,V660M,V661M,V662M,V663M,V664M,V665M,V666M,V667M,V668M,V669M,V670M}
E16M.values = {V671M,V672M,V673M,V674M,V675M,V676M,V677M,V678M,V679M,V680M,V681M}
E17M.values = {V682M,V683M,V684M}
E18M.values = {V685M,V686M,V687M}
E19M.values = {V688M,V689M,V690M,V691M,V692M,V693M,V694M,V695M,V696M,V697M,V698M,V699M,V700M,V701M,V702M,V703M,V704M,V705M,V706M,V707M,V708M,V709M,V710M,V711M,V712M,V713M,V714M,V715M,V716M,V717M,V718M,V719M,V720M,V721M,V722M,V723M,V724M,V725M,V726M,V727M,V728M,V729M,V730M,V731M,V732M,V733M,V734M}
E20M.values = {V735M,V736M,V737M,V738M,V739M,V740M,V741M,V742M}
E21M.values = {V743M,V744M,V745M,V746M,V747M,V748M,V749M,V750M,V751M,V752M,V753M}
E22M.values = {V754M,V755M,V756M,V757M,V758M}
E23M.values = {V759M,V760M,V761M,V762M,V763M}
E24M.values = {V764M,V765M,V766M,V767M,V768M,V769M,V770M,V771M,V772M,V773M,V774M}
E25M.values = {V775M,V776M,V777M,V778M,V779M,V780M,V781M,V782M,V783M,V784M,V785M,V786M,V787M,V788M,V789M,V790M,V791M,V792M}
E26M.values = {V793M,V794M,V795M}
E27M.values = {V796M,V797M,V798M,V799M,V800M,V801M,V802M,V803M,V804M,V805M,V806M,V807M,V808M,V809M,V810M,V811M,V812M,V813M,V814M,V815M,V816M,V817M,V818M,V819M,V820M,V821M,V822M,V823M,V824M,V825M,V826M,V827M,V828M,V829M,V830M,V831M,V832M,V833M,V834M,V835M,V836M,V837M,V838M,V839M,V840M,V841M,V842M,V843M,V844M,V845M,V846M,V847M,V848M,V849M,V850M,V851M,V852M,V853M,V854M,V855M,V856M,V857M,V858M,V859M,V860M,V861M,V862M,V863M,V864M,V865M,V866M,V867M,V868M,V869M,V870M,V871M,V872M,V873M,V874M,V875M,V876M,V877M,V878M}
E28M.values = {V879M,V880M,V881M,V882M,V883M,V884M,V885M,V886M,V887M,V888M,V889M,V890M,V891M,V892M,V893M,V894M,V895M,V896M,V897M,V898M,V899M,V900M,V901M,V902M,V903M,V904M,V905M,V906M,V907M,V908M,V909M,V910M}
E29M.values = {V911M,V912M,V913M,V914M}
E30M.values = {V915M,V916M,V917M,V918M,V919M,V920M,V921M,V922M,V923M,V924M,V925M,V926M,V927M,V928M,V929M}
E31M.values = {V930M,V931M,V932M,V933M,V934M,V935M,V936M,V937M,V938M,V939M,V940M,V941M,V942M,V943M,V944M,V945M,V946M,V947M,V948M,V949M,V950M,V951M,V952M,V953M,V954M,V955M,V956M,V957M,V958M,V959M}
M1G.fields={F1D, F2D}
F4D.message_type=M1G
M2G.fields={F3D, F4D}
F5D.message_type=M2G
M3G.fields={F5D}
F6D.message_type=M2G
M4G.fields={F6D}
M6G.fields={F7D, F8D, F9D, F10D, F11D}
M6G.containing_type=M5G
M7G.fields={F12D}
M7G.containing_type=M5G
F13D.message_type=M6G
F14D.message_type=M7G
M5G.nested_types={M6G, M7G}
M5G.fields={F13D, F14D}
M8G.fields={F15D, F16D}
M9G.fields={F17D, F18D}
F19D.message_type=M9G
M10G.fields={F19D}
F20D.message_type=M9G
F21D.enum_type=M12G
M11G.fields={F20D, F21D, F22D, F23D}
M13G.fields={F24D, F25D}
F28D.enum_type=M15G
M14G.fields={F26D, F27D, F28D}
M16G.fields={F29D, F30D}
M17G.fields={F31D, F32D}
F33D.enum_type=M19G
F34D.message_type=M17G
M18G.fields={F33D, F34D, F35D, F36D}
M20G.fields={F37D, F38D, F39D}
F40D.message_type=M10G
F41D.message_type=M16G
M21G.fields={F40D, F41D}
F42D.enum_type=M23G
M22G.fields={F42D, F43D}
F47D.message_type=M22G
M24G.fields={F44D, F45D, F46D, F47D}
F48D.message_type=M11G
F49D.message_type=M13G
F50D.message_type=M14G
F51D.message_type=M24G
M25G.fields={F48D, F49D, F50D, F51D}
F52D.message_type=M24G
M26G.fields={F52D}
F64D.message_type=common_new_pb.M2G
M27G.fields={F53D, F54D, F55D, F56D, F57D, F58D, F59D, F60D, F61D, F62D, F63D, F64D, F65D}
M29G.fields={F66D, F67D}
M30G.fields={F68D, F69D}
M31G.fields={F70D, F71D}
F72D.message_type=M31G
F73D.message_type=M29G
F74D.message_type=M30G
M32G.fields={F72D, F73D, F74D}
M33G.fields={F75D, F76D, F77D, F78D, F79D, F80D, F81D, F82D, F83D, F84D, F85D}
F86D.message_type=M33G
M34G.fields={F86D}
M35G.fields={F87D, F88D, F89D, F90D, F91D, F92D}
F93D.message_type=M35G
M36G.fields={F93D}
F94D.message_type=common_new_pb.M11G
M37G.fields={F94D, F95D, F96D}
F99D.message_type=M36G
F102D.message_type=M37G
M39G.fields={F97D, F98D, F99D, F100D, F101D, F102D}
F103D.message_type=M18G
F104D.message_type=M20G
M40G.fields={F103D, F104D}
M41G.fields={F105D, F106D, F107D, F108D, F109D, F110D, F111D}
M42G.fields={F112D, F113D, F114D, F115D, F116D, F117D, F118D, F119D}
M43G.fields={F120D, F121D, F122D, F123D}
F125D.message_type=M42G
F128D.message_type=M43G
M44G.fields={F124D, F125D, F126D, F127D, F128D, F129D}
M45G.fields={F130D, F131D, F132D}
F135D.message_type=M45G
M46G.fields={F133D, F134D, F135D}
F136D.message_type=M46G
M47G.fields={F136D}
M48G.fields={F137D, F138D}
M49G.fields={F139D, F140D, F141D, F142D, F143D, F144D}
F146D.message_type=M49G
F147D.message_type=M48G
M50G.fields={F145D, F146D, F147D}
M51G.fields={F148D}
F149D.message_type=M51G
M52G.fields={F149D, F150D}
M53G.fields={F151D, F152D, F153D, F154D, F155D, F156D, F157D, F158D}
M54G.fields={F159D, F160D, F161D}
F164D.message_type=M54G
F165D.message_type=M53G
M55G.fields={F162D, F163D, F164D, F165D}
F166D.enum_type=common_new_pb.E6M
M56G.fields={F166D, F167D}
F173D.message_type=M55G
F174D.message_type=M56G
M58G.fields={F168D, F169D, F170D, F171D, F172D, F173D, F174D}
M59G.fields={F175D, F176D, F177D, F178D}
F179D.enum_type=M62G
M61G.fields={F179D, F180D, F181D}
M61G.containing_type=M60G
F182D.message_type=M61G
M60G.nested_types={M61G}
M60G.fields={F182D}
F183D.message_type=M9G
F185D.message_type=M8G
M63G.fields={F183D, F184D, F185D, F186D, F187D}
F189D.enum_type=M65G
F190D.message_type=M9G
M64G.fields={F188D, F189D, F190D, F191D}
F192D.message_type=M64G
M66G.fields={F192D, F193D, F194D, F195D, F196D, F197D}
F198D.message_type=M66G
M67G.fields={F198D}
F199D.message_type=M10G
F200D.message_type=M11G
F201D.message_type=M16G
F203D.message_type=M24G
M68G.fields={F199D, F200D, F201D, F202D, F203D}
F204D.enum_type=M65G
M69G.fields={F204D, F205D, F206D}
M70G.fields={F207D, F208D, F209D}
M71G.fields={F210D, F211D, F212D}
F213D.message_type=M71G
M72G.fields={F213D}
M73G.fields={F214D}
M74G.fields={F215D, F216D, F217D}
M75G.fields={F218D, F219D, F220D, F221D, F222D}
F223D.message_type=M74G
F224D.message_type=M75G
M76G.fields={F223D, F224D, F225D, F226D, F227D}
M77G.fields={F228D, F229D, F230D, F231D, F232D}
M78G.fields={F233D, F234D, F235D}
M79G.fields={F236D, F237D, F238D, F239D}
F243D.message_type=M79G
M80G.fields={F240D, F241D, F242D, F243D, F244D, F245D}
M81G.fields={F246D, F247D}
F248D.message_type=M81G
M82G.fields={F248D}
F249D.message_type=M76G
F250D.message_type=M77G
F251D.message_type=M78G
F252D.message_type=M80G
F253D.message_type=M82G
M83G.fields={F249D, F250D, F251D, F252D, F253D}
M84G.fields={F254D, F255D, F256D}
M85G.fields={F257D, F258D}
F259D.message_type=M85G
M86G.fields={F259D}
F260D.message_type=M86G
M87G.fields={F260D}
M88G.fields={F261D, F262D, F263D}
M89G.fields={F264D, F265D, F266D}
M90G.fields={F267D, F268D, F269D, F270D}
F277D.message_type=M88G
F279D.message_type=M89G
F280D.message_type=M90G
M91G.fields={F271D, F272D, F273D, F274D, F275D, F276D, F277D, F278D, F279D, F280D}
M93G.fields={F281D}
M95G.fields={F282D, F283D, F284D}
F285D.message_type=M95G
M96G.fields={F285D, F286D}
M97G.fields={F287D, F288D, F289D}
M98G.fields={F290D, F291D, F292D, F293D, F294D, F295D}
F296D.message_type=M97G
F297D.message_type=M98G
M99G.fields={F296D, F297D}

ALLY_DEFENSE_ATK_BOOST = 217
ALLY_DEFENSE_DEF_BOOST = 218
ALLY_DEFENSE_HP_BOOST = 216
ALLY_GATHERING_ATK_BOOST = 214
ALLY_GATHERING_DEF_BOOST = 215
ALLY_GATHERING_HP_BOOST = 213
ANTI_DAMAGE_BOOST = 12
ANTI_DAMAGE_REDUCTION = 13
ATTACK_BASE_ATK_BOOST = 220
ATTACK_BASE_DEF_BOOST = 221
ATTACK_BASE_HP_BOOST = 219
BATTLE_NEW_COMBAT_POWER_DAMAGE_CAP = 26
BATTLE_NEW_COMBAT_POWER_DAMAGE_VALUE = 25
BATTLE_NEW_COUNTER_DAMAGE_INCREASE = 14
BATTLE_NEW_COUNTER_DAMAGE_REDUCTION = 15
BATTLE_NEW_CRITICAL_DAMAGE_RATE = 10
BATTLE_NEW_CRITICAL_DAMAGE_RES_RATE = 11
BATTLE_NEW_CRITICAL_HIT_RATE = 8
BATTLE_NEW_CRITICAL_RESISTANCE_RATE = 9
BATTLE_NEW_CURR_ENERGY = 6
BATTLE_NEW_CURR_HEALTH_BASE = 1
BATTLE_NEW_ENERGY_CAPACITY = 5
BATTLE_NEW_ENERGY_DAMAGE_INCREASE = 20
BATTLE_NEW_ENERGY_DAMAGE_REDUCTION = 21
BATTLE_NEW_ENERGY_RECOVER = 23
BATTLE_NEW_GENERIC_DAMAGE_INCREASE = 12
BATTLE_NEW_GENERIC_DAMAGE_REDUCTION = 13
BATTLE_NEW_INDEPENDENT_DAMAGE_VALUE = 24
BATTLE_NEW_LEVEL_DAMAGE_INCREASE = 27
BATTLE_NEW_MAXVALUE = 31
BATTLE_NEW_MAX_ENERGY_BASE = 30
BATTLE_NEW_MAX_HEALTH_BASE = 29
BATTLE_NEW_MONSTER_DAMAGE_INCREASE = 16
BATTLE_NEW_MONSTER_DAMAGE_REDUCTION = 17
BATTLE_NEW_MORAL_DAMAGE_INCREASE = 28
BATTLE_NEW_PHYSICAL_DAMAGE_INCREASE = 18
BATTLE_NEW_PHYSICAL_DAMAGE_REDUCTION = 19
BATTLE_NEW_SKILL_DAMAGE_INCREASE = 22
BATTLE_NEW_SPEED = 7
BATTLE_NEW_TOTAL_ATTACK_BASE = 2
BATTLE_NEW_TOTAL_DEFENSE_BASE = 3
BATTLE_NEW_TOTAL_HEALTH_BASE = 0
BATTLE_NEW_TOTAL_TROOPS_CAPACITY = 4
BattleshipTechData =M(M51G)
COMBAT_POWER_DAMAGE_CAP = 44
COMBAT_POWER_DAMAGE_VALUE = 43
CRITICAL_DMG_BOOST = 8
CRITICAL_DMG_RESISTANCE = 9
CRITICAL_HIT_BOOST = 6
CRITICAL_RESISTANCE = 7
DECORATE_PROP_LOCK = 9
DECORATE_PROP_MAXID = 12
DECORATE_PROP_PALSID = 10
DECORATE_PROP_STR_LV = 11
DECORATE_PROP_UNKNOW = 8
DEFEND_IN_CITY_ATK_BOOST = 223
DEFEND_IN_CITY_DEF_BOOST = 224
DEFEND_IN_CITY_HP_BOOST = 222
DRONESTAR_PROP_LOCK = 15
DRONESTAR_PROP_MAXID = 16
DRONESTAR_PROP_PRO_1 = 10
DRONESTAR_PROP_PRO_2 = 11
DRONESTAR_PROP_PRO_3 = 12
DRONESTAR_PROP_PRO_4 = 13
DRONESTAR_PROP_PRO_5 = 14
DRONESTAR_PROP_STAR = 9
DRONESTAR_PROP_UNKNOW = 8
DRONE_ATTACK = 30
DRONE_DEFENCE = 31
DRONE_HP = 29
DailyAttend_LoginNoSign = 2
DailyAttend_LoginSign = 3
DailyAttend_NoLogin = 1
ENERGY_DMG_BOOST = 18
ENERGY_DMG_REDUCTION = 19
ENERGY_INITIAL = 41
ENERGY_RECOVERY = 28
EQUIP_PROP_EXTEND_PRO_1 = 16
EQUIP_PROP_EXTEND_PRO_2 = 17
EQUIP_PROP_EXTEND_PRO_3 = 18
EQUIP_PROP_EXTEND_PRO_4 = 19
EQUIP_PROP_EXTEND_PRO_5 = 20
EQUIP_PROP_HIGH_REFORGE_COUNT = 27
EQUIP_PROP_LOCK = 15
EQUIP_PROP_MAXID = 30
EQUIP_PROP_PRO_1 = 10
EQUIP_PROP_PRO_2 = 11
EQUIP_PROP_PRO_3 = 12
EQUIP_PROP_PRO_4 = 13
EQUIP_PROP_RECAST_TIME = 29
EQUIP_PROP_RECAST_TYPE = 26
EQUIP_PROP_RESONANCE_PRO = 28
EQUIP_PROP_STR_LV = 9
EQUIP_PROP_TEMP_EXTEND_PRO_1 = 21
EQUIP_PROP_TEMP_EXTEND_PRO_2 = 22
EQUIP_PROP_TEMP_EXTEND_PRO_3 = 23
EQUIP_PROP_TEMP_EXTEND_PRO_4 = 24
EQUIP_PROP_TEMP_EXTEND_PRO_5 = 25
EQUIP_PROP_UNKNOW = 8
EQUIP_PROP_UPGRADE_EXP = 14
FOREST_HERO_ATK_BOOST = 205
FOREST_HERO_DEF_BOOST = 206
FOREST_HERO_DMG_BOOST = 20
FOREST_HERO_EXTRA_ATK = 105
FOREST_HERO_EXTRA_DEF = 106
FOREST_HERO_EXTRA_HP = 104
FOREST_HERO_HP_BOOST = 204
FOREST_HERO_TROOP_BOOST = 24
GENERIC_DMG_BOOST = 10
GENERIC_DMG_REDUCTION = 11
GENERIC_TROOP_AMOUNT = 23
GOODS_PROP_EXPIRE = 6
GOODS_PROP_ID = 1
GOODS_PROP_MAXID = 7
GOODS_PROP_QTY = 3
GOODS_PROP_SID = 2
GOODS_PROP_SKEPID = 4
GOODS_PROP_SKEP_PLACE = 5
GOODS_PROP_UNKNOW = 0
GOODS_TYPE_DECORATE = 21
GOODS_TYPE_EQUIP = 1
GOODS_TYPE_MYTHICAL = 40
GOODS_TYPE_OTHER = 4
GOODS_TYPE_PACKAGE = 5
GOODS_TYPE_PIECE = 3
GOODS_TYPE_RELIC = 9
GOODS_TYPE_SIGIL = 19
GOODS_TYPE_SKILL = 2
GOODS_TYPE_STARDIAMOND = 23
GOODS_TYPE_TRESURERARE = 22
GWBuffType_Burn = 3
GWBuffType_Feverish = 1
GWBuffType_None = 0
GWBuffType_Sheild = 2
GWMapType_Buff = 4
GWMapType_CityBuilding = 0
GWMapType_Congress = 13
GWMapType_DecorateBuild = 10
GWMapType_DressUp = 11
GWMapType_DroneCenter = 5
GWMapType_HonorWall = 12
GWMapType_MonthlyCard = 9
GWMapType_NeutralCity = 7
GWMapType_Research = 2
GWMapType_SchlossId = 8
GWMapType_Sum = 999
GWMapType_Survival = 3
GWMapType_Technology = 1
GWMapType_VIPPart = 6
HERO_BASE_ATK = 2
HERO_BASE_ATK_BOOST = 202
HERO_BASE_DEF = 3
HERO_BASE_DEF_BOOST = 203
HERO_BASE_HP = 1
HERO_BASE_HP_BOOST = 201
HERO_ENERGY_LIMIT = 4
HERO_EXTRA_ATK = 102
HERO_EXTRA_DEF = 103
HERO_EXTRA_HP = 101
HERO_SPEED = 5
HUMANOID_HERO_ATK_BOOST = 208
HUMANOID_HERO_DEF_BOOST = 209
HUMANOID_HERO_DMG_BOOST = 21
HUMANOID_HERO_EXTRA_ATK = 108
HUMANOID_HERO_EXTRA_DEF = 109
HUMANOID_HERO_EXTRA_HP = 107
HUMANOID_HERO_HP_BOOST = 207
HUMANOID_HERO_TROOP_BOOST = 25
HeroAttributes_MAXVALUE = 280
HeroOccupation_Assassin = 4
HeroOccupation_Master = 2
HeroOccupation_Priest = 5
HeroOccupation_Ranger = 3
HeroOccupation_Warrior = 1
INDEPENDENT_DAMAGE_VALUE = 42
MONSTER_DMG_BOOST = 14
MONSTER_DMG_REDUCTION = 15
ModuleOpenType_AcornPubTreasure1 = 1024
ModuleOpenType_AcornPubTreasure2 = 1025
ModuleOpenType_AllianceAutoHelp = 2105
ModuleOpenType_AllianceBattle = 1009
ModuleOpenType_ArmamentRace = 1008
ModuleOpenType_Building = 1018
ModuleOpenType_Carriage = 1507
ModuleOpenType_Chat = 1006
ModuleOpenType_CityCompetition = 1011
ModuleOpenType_Citybuilding = 1004
ModuleOpenType_Collect = 1017
ModuleOpenType_DiffServer_AllianceBattle = 1010
ModuleOpenType_Email = 1005
ModuleOpenType_HeroEquipLevel = 1301
ModuleOpenType_HeroEquipMake = 1303
ModuleOpenType_HeroEquipStrength = 1302
ModuleOpenType_HeroEquipment = 1300
ModuleOpenType_HeroRecruit = 1100
ModuleOpenType_League = 1002
ModuleOpenType_LineUpBoostHalo = 1524
ModuleOpenType_MarchingBand = 1015
ModuleOpenType_Radar = 1007
ModuleOpenType_Rank = 1022
ModuleOpenType_Reconnaissance = 1014
ModuleOpenType_SandBox = 1001
ModuleOpenType_SandBoxWarning = 1013
ModuleOpenType_SciResearch = 1003
ModuleOpenType_Search = 1016
ModuleOpenType_SurvivalRecruit = 1200
ModuleOpenType_WarZoneBattle = 1012
NIGHT_ELF_HERO_ATK_BOOST = 211
NIGHT_ELF_HERO_DEF_BOOST = 212
NIGHT_ELF_HERO_DMG_BOOST = 22
NIGHT_ELF_HERO_EXTRA_ATK = 111
NIGHT_ELF_HERO_EXTRA_DEF = 112
NIGHT_ELF_HERO_EXTRA_HP = 110
NIGHT_ELF_HERO_HP_BOOST = 210
NIGHT_ELF_HERO_TROOP_BOOST = 26
PAL_PROP_ATTACK = 28
PAL_PROP_ATTACK_RATE = 42
PAL_PROP_AWAKE_SKILLID = 113
PAL_PROP_AWAKE_SKILLLV = 114
PAL_PROP_BASE_ATTACK = 22
PAL_PROP_BASE_ATTACK_RATE = 59
PAL_PROP_BASE_DEFENCE = 23
PAL_PROP_BASE_DEFENCE_RATE = 60
PAL_PROP_BASE_ENERGY = 46
PAL_PROP_BASE_FATAL_RATE = 25
PAL_PROP_BASE_FATAL_SCALE = 26
PAL_PROP_BASE_HP = 21
PAL_PROP_BASE_HP_RATE = 58
PAL_PROP_BASE_SPEED = 24
PAL_PROP_BASE_SPEED_RATE = 61
PAL_PROP_BLESSING_LV1 = 116
PAL_PROP_BLESSING_LV2 = 117
PAL_PROP_BLESSING_LV3 = 118
PAL_PROP_BLESSING_LV4 = 119
PAL_PROP_BLESSING_LV5 = 120
PAL_PROP_BLESSING_LV6 = 121
PAL_PROP_BLESSING_LV7 = 122
PAL_PROP_BLESSING_LV8 = 123
PAL_PROP_BLESSING_LV9 = 124
PAL_PROP_BLOCK_RATE = 38
PAL_PROP_BREAK_RATE = 39
PAL_PROP_BREAK_TREND = 127
PAL_PROP_CK_DAMEGE_RATE = 56
PAL_PROP_CR_DAMEGE_RATE = 64
PAL_PROP_CURE_HP_MAX = 91
PAL_PROP_DECO_SKEPID = 115
PAL_PROP_DEFENCE = 29
PAL_PROP_DEFENCE_RATE = 43
PAL_PROP_DL_DAMEGE_RATE = 63
PAL_PROP_DODGE_RATE = 36
PAL_PROP_EFFECT_HIT_RATE = 33
PAL_PROP_EFFECT_RESIST_RATE = 34
PAL_PROP_EQUIP_POWER = 139
PAL_PROP_EQUIP_SKEPID = 14
PAL_PROP_EXTEND_STABLE_ATTACK = 84
PAL_PROP_EXTEND_STABLE_AUX_ATTACK = 88
PAL_PROP_EXTEND_STABLE_AUX_DEFENCE = 89
PAL_PROP_EXTEND_STABLE_AUX_HP = 87
PAL_PROP_EXTEND_STABLE_AUX_SPEED = 90
PAL_PROP_EXTEND_STABLE_DEFENCE = 85
PAL_PROP_EXTEND_STABLE_HP = 83
PAL_PROP_EXTEND_STABLE_SPEED = 86
PAL_PROP_EXTRA_SKILL_POINTS = 52
PAL_PROP_FATAL_DEFEND = 108
PAL_PROP_FATAL_RATE = 31
PAL_PROP_FATAL_REDUCE_RATE = 62
PAL_PROP_FATAL_SCALE = 32
PAL_PROP_FATAL_SCALE_DEFEND = 109
PAL_PROP_FS_DAMEGE_RATE = 54
PAL_PROP_GLOBAL_ATTACK_RATE = 49
PAL_PROP_GLOBAL_DEFENCE_RATE = 50
PAL_PROP_GLOBAL_HP_RATE = 48
PAL_PROP_GLOBAL_SPEED_RATE = 51
PAL_PROP_HIT_RATE = 35
PAL_PROP_HOLY_DAMAGE_RATE = 47
PAL_PROP_HONORWALL = 140
PAL_PROP_HP = 27
PAL_PROP_HP_RATE = 41
PAL_PROP_ID = 1
PAL_PROP_IMMUNE_RATE = 40
PAL_PROP_KJ_DAMEGE_RATE = 65
PAL_PROP_LV = 3
PAL_PROP_MAXID = 142
PAL_PROP_MS_DAMEGE_RATE = 57
PAL_PROP_OCCUPATION = 17
PAL_PROP_PARRY = 128
PAL_PROP_PIERCE_RATE = 37
PAL_PROP_POWER = 20
PAL_PROP_POWER_ATTACK = 111
PAL_PROP_POWER_DEFEND = 110
PAL_PROP_REMAIN_HP = 18
PAL_PROP_SID = 2
PAL_PROP_SIGIL_SKEPID = 112
PAL_PROP_SKILL_1 = 6
PAL_PROP_SKILL_2 = 7
PAL_PROP_SKILL_3 = 8
PAL_PROP_SKILL_4 = 9
PAL_PROP_SKILL_5 = 125
PAL_PROP_SKILL_DAMAGE_RATE = 45
PAL_PROP_SKILL_EX = 71
PAL_PROP_SKILL_EX2 = 92
PAL_PROP_SKILL_EX2_MAX = 99
PAL_PROP_SKILL_EX_MAX = 76
PAL_PROP_SKILL_LV_1 = 10
PAL_PROP_SKILL_LV_2 = 11
PAL_PROP_SKILL_LV_3 = 12
PAL_PROP_SKILL_LV_4 = 13
PAL_PROP_SKILL_LV_5 = 126
PAL_PROP_SKILL_LV_EX = 77
PAL_PROP_SKILL_LV_EX2 = 100
PAL_PROP_SKILL_LV_EX2_MAX = 107
PAL_PROP_SKILL_LV_EX_MAX = 82
PAL_PROP_SKILL_POINT = 5
PAL_PROP_SKILL_POWER = 138
PAL_PROP_SKILL_SKEPID = 15
PAL_PROP_SM_DAMEGE_RATE = 68
PAL_PROP_SOLDIERS_POWER = 141
PAL_PROP_SPEED = 30
PAL_PROP_SPEED_RATE = 44
PAL_PROP_STARDIAMOND_SKEPID = 129
PAL_PROP_STAR_LV = 16
PAL_PROP_STEP_LV = 4
PAL_PROP_SYNC_MAXID = 19
PAL_PROP_TS_DAMEGE_FIX_RATE = 70
PAL_PROP_TS_DAMEGE_RATE = 69
PAL_PROP_UNKNOW = 0
PAL_PROP_WEAPONDIAMOND_CURLEVEL = 137
PAL_PROP_WEAPONDIAMOND_MAXLEVEL = 136
PAL_PROP_WEAPONDIAMOND_SKILLLV_1 = 133
PAL_PROP_WEAPONDIAMOND_SKILLLV_2 = 134
PAL_PROP_WEAPONDIAMOND_SKILLLV_3 = 135
PAL_PROP_WEAPONDIAMOND_SKILL_1 = 130
PAL_PROP_WEAPONDIAMOND_SKILL_2 = 131
PAL_PROP_WEAPONDIAMOND_SKILL_3 = 132
PAL_PROP_YX_DAMEGE_RATE = 55
PAL_PROP_YZ_DAMEGE_RATE = 67
PAL_PROP_ZR_DAMEGE_RATE = 66
PAL_PROP_ZS_DAMEGE_RATE = 53
PAL_SRC_DEFAULT = 0
PAL_SRC_MAX = 4
PAL_SRC_MAZE = 1
PAL_SRC_PEAK = 2
PAL_SRC_TRIALATY = 3
PERSONPART_ACTIVITY = 16
PERSONPART_ALLIEDTECH = 15
PERSONPART_ARENAPART = 11
PERSONPART_ASHDUNGEON = 13
PERSONPART_BATTLESHIPTECH = 34
PERSONPART_CHINARED = 36
PERSONPART_CITY = 4
PERSONPART_DECORATE = 37
PERSONPART_DRONECENTER = 45
PERSONPART_ENTITY_MAXID = 48
PERSONPART_FACEDATA = 14
PERSONPART_FACTION = 27
PERSONPART_FARM = 20
PERSONPART_FRAME = 26
PERSONPART_HALO = 19
PERSONPART_IDLE = 3
PERSONPART_ILLUSIONTOWER = 8
PERSONPART_INVITEGIFT = 39
PERSONPART_LEAGUE = 17
PERSONPART_LEAGUECOMP = 18
PERSONPART_LOTTERY = 5
PERSONPART_LOTTERY_WISH = 6
PERSONPART_MATE = 22
PERSONPART_MAZE = 23
PERSONPART_MERGESERVER = 31
PERSONPART_OPERATIVE = 30
PERSONPART_PACKET = 1
PERSONPART_PAL = 2
PERSONPART_PASSPORT = 24
PERSONPART_PEAK = 28
PERSONPART_PICKTHEROUTE = 43
PERSONPART_RECHARGEGIFT = 47
PERSONPART_RETURNACT = 32
PERSONPART_SCIENTIFICRESEARCH = 44
PERSONPART_SEVENCHALLENGE = 29
PERSONPART_SHOP_DECORATE = 38
PERSONPART_SOULLINK = 25
PERSONPART_SPACEEXPLORATION = 33
PERSONPART_STARTEMPLE = 41
PERSONPART_SUBGIFT = 35
PERSONPART_SUBJECT = 7
PERSONPART_TASKMGR = 46
PERSONPART_TAVERN = 9
PERSONPART_TREASURERARE = 40
PERSONPART_TRIAL = 12
PERSONPART_WEAPON = 21
PERSONPART_XYX = 42
PERSON_ADDPROP_CARRIAGE_BASERATE = 28
PERSON_ADDPROP_CARRIAGE_EXLOOTS = 30
PERSON_ADDPROP_CARRIAGE_EXRECAS = 29
PERSON_ADDPROP_CARRIAGE_EXTRADES = 31
PERSON_ADDPROP_CARRIAGE_REINDEER = 33
PERSON_ADDPROP_CARRIAGE_SUPERDEF = 32
PERSON_ADDPROP_CITY_ACORNPUB_CAN_SEND_TASK_COUNT = 35
PERSON_ADDPROP_CITY_CAN_RECRUIT_SOLDIER_MAX_LEVEL = 26
PERSON_ADDPROP_CITY_FOOD_PROTECT = 23
PERSON_ADDPROP_CITY_GOLD_PROTECT = 25
PERSON_ADDPROP_CITY_HOSPITAL_CAPACITY = 21
PERSON_ADDPROP_CITY_IRON_PROTECT = 24
PERSON_ADDPROP_CITY_MAIN_LEVEL = 22
PERSON_ADDPROP_CITY_TRAINNINGCENTER_CAPACITY = 34
PERSON_ADDPROP_CITY_WALL_LEVEL = 20
PERSON_ADDPROP_FACESTR = 12
PERSON_ADDPROP_MODIFYNAME_TIME = 13
PERSON_ADDPROP_NAME = 1
PERSON_ADDPROP_PLATE = 57
PERSON_ADDPROP_SCHLOSS = 27
PERSON_ADDPROP_SCHLOSS_EFFECT = 36
PERSON_ADDPROP_UNKNOW = 0
PERSON_ALIANCE_TASK_FINISH = 50
PERSON_ALLIANCE_ADDTIME = 8
PERSON_ALLIANCE_AUTH = 5
PERSON_ALLIANCE_CREATETIME = 7
PERSON_ALLIANCE_EXITTIME = 10
PERSON_ALLIANCE_FIRST_JOIN_TIME = 11
PERSON_ALLIANCE_FLAG = 9
PERSON_ALLIANCE_ID = 2
PERSON_ALLIANCE_NAME = 3
PERSON_ALLIANCE_POST = 6
PERSON_ALLIANCE_SHORTNAME = 4
PERSON_ALLIANCE_TASK_PLAY_ATY_STATUS = 52
PERSON_ALLIANCE_TASK_PLAY_ATY_TIME = 53
PERSON_BATTLE_POINT = 51
PERSON_GALAXY_STAR = 15
PERSON_OFFLINE_PROP_ACCOUNTID = 504
PERSON_OFFLINE_PROP_ACORNPUB_TREASURE_FLAG = 93
PERSON_OFFLINE_PROP_ALLIANCEBOSS_REWARDINFO = 89
PERSON_OFFLINE_PROP_AREAID = 519
PERSON_OFFLINE_PROP_AUTO_SEND_MAIL_ISSEND_1 = 301
PERSON_OFFLINE_PROP_AUTO_SEND_MAIL_ISSEND_MAX = 400
PERSON_OFFLINE_PROP_AUTO_SEND_MAIL_LASTTIME_1 = 401
PERSON_OFFLINE_PROP_AUTO_SEND_MAIL_LASTTIME_MAX = 500
PERSON_OFFLINE_PROP_BUFF_ENDTIME = 528
PERSON_OFFLINE_PROP_BUFF_LAYERS = 527
PERSON_OFFLINE_PROP_BUFF_LIST = 526
PERSON_OFFLINE_PROP_BUILDING_VISITOR_LASTSENDTIME_1 = 520
PERSON_OFFLINE_PROP_CHANGEBAG_RECVERSION = 522
PERSON_OFFLINE_PROP_CHANGEBAG_REWARDVERSION = 523
PERSON_OFFLINE_PROP_CHANNELID = 503
PERSON_OFFLINE_PROP_CITY_CUR_DEFEND_VALUE = 8
PERSON_OFFLINE_PROP_CITY_MAX_TYPE_LEVEL = 249
PERSON_OFFLINE_PROP_CITY_MIN_TYPE_LEVEL = 200
PERSON_OFFLINE_PROP_CITY_POS = 94
PERSON_OFFLINE_PROP_CITY_WALL_BEGINTIME = 9
PERSON_OFFLINE_PROP_CITY_WALL_FIRE_ENDTIME = 10
PERSON_OFFLINE_PROP_COIN = 1
PERSON_OFFLINE_PROP_COUNTRY_ID = 533
PERSON_OFFLINE_PROP_CREATE_ROLE_TIME = 505
PERSON_OFFLINE_PROP_CROSS_STATUS = 7
PERSON_OFFLINE_PROP_DEVICE_LANGUAGE = 508
PERSON_OFFLINE_PROP_FOOD = 2
PERSON_OFFLINE_PROP_GAME_LANGUAGE = 515
PERSON_OFFLINE_PROP_GATHERING_Cnt = 84
PERSON_OFFLINE_PROP_GATHERING_ENDTIME = 77
PERSON_OFFLINE_PROP_GATHERING_ID = 76
PERSON_OFFLINE_PROP_GATHERING_JoinMassCnt = 86
PERSON_OFFLINE_PROP_GATHERING_LastJoinMassTime = 87
PERSON_OFFLINE_PROP_GATHERING_LastMassTime = 85
PERSON_OFFLINE_PROP_GATHERING_State = 83
PERSON_OFFLINE_PROP_GATHERING_TermID = 82
PERSON_OFFLINE_PROP_GET_REWARD_TIME_ACHIEVEMENT_1 = 150
PERSON_OFFLINE_PROP_GET_REWARD_TIME_ACHIEVEMENT_MAX = 199
PERSON_OFFLINE_PROP_INCITY_SOLDIER_LEVEL1 = 11
PERSON_OFFLINE_PROP_INCITY_SOLDIER_MAX = 30
PERSON_OFFLINE_PROP_INJURED_SOLDIER_LEVEL1 = 51
PERSON_OFFLINE_PROP_INJURED_SOLDIER_MAX = 70
PERSON_OFFLINE_PROP_ISGET_REWARD_ACHIEVEMENT_1 = 100
PERSON_OFFLINE_PROP_ISGET_REWARD_ACHIEVEMENT_MAX = 149
PERSON_OFFLINE_PROP_IS_OFFLINE_BROKENCITY_EVENT = 90
PERSON_OFFLINE_PROP_IS_OFFLINE_BROKEN_CITY = 78
PERSON_OFFLINE_PROP_KILLNUM = 6
PERSON_OFFLINE_PROP_MAX = 537
PERSON_OFFLINE_PROP_NATIONAL_FALG = 535
PERSON_OFFLINE_PROP_NATIONAL_FALG_TIME = 536
PERSON_OFFLINE_PROP_NC_OA_COUNT = 73
PERSON_OFFLINE_PROP_NC_OA_TIME = 74
PERSON_OFFLINE_PROP_OFFLINE_BROKEN_CITY_TIME = 79
PERSON_OFFLINE_PROP_OFFLINE_BROKEN_GET_REWARD_TIME = 92
PERSON_OFFLINE_PROP_OUTCITY_SOLDIER_LEVEL1 = 31
PERSON_OFFLINE_PROP_OUTCITY_SOLDIER_MAX = 50
PERSON_OFFLINE_PROP_OUTFIRE_CNT = 524
PERSON_OFFLINE_PROP_OUTFIRE_TIME = 525
PERSON_OFFLINE_PROP_PASS_MAX_STAGE = 506
PERSON_OFFLINE_PROP_PLUNDER_DAY_NUM = 71
PERSON_OFFLINE_PROP_PLUNDER_DAY_TIME = 72
PERSON_OFFLINE_PROP_POSITIONID = 501
PERSON_OFFLINE_PROP_PUSHMSG_BUTTON_1 = 510
PERSON_OFFLINE_PROP_PUSHMSG_BUTTON_2 = 511
PERSON_OFFLINE_PROP_PUSHMSG_ISFIRST = 514
PERSON_OFFLINE_PROP_PUSHMSG_ISSEND_1 = 512
PERSON_OFFLINE_PROP_PUSHMSG_ISSEND_2 = 513
PERSON_OFFLINE_PROP_PUSHMSG_MAINBUTTON = 509
PERSON_OFFLINE_PROP_R4R5TODO_REWARD = 529
PERSON_OFFLINE_PROP_R4R5TODO_TIME = 530
PERSON_OFFLINE_PROP_SANDBOXID = 5
PERSON_OFFLINE_PROP_SDKUSERID = 531
PERSON_OFFLINE_PROP_SKD_CHANNEL = 534
PERSON_OFFLINE_PROP_STAMINA = 4
PERSON_OFFLINE_PROP_STEEL = 3
PERSON_OFFLINE_PROP_TIME_ZONE = 521
PERSON_OFFLINE_PROP_TOTAL_FIVE_PAL_MAX_TROOP = 88
PERSON_OFFLINE_PROP_TROOP_MAX_POWER = 502
PERSON_OFFLINE_PROP_UNKNOW = 0
PERSON_OFFLINE_PROP_UNLOCKHONORWALL = 532
PERSON_OFFLINE_PROP_UNLOCK_TROOP = 81
PERSON_OFFLINE_PROP_UNLOCK_TRUCK = 91
PERSON_OFFLINE_PROP_USERID = 518
PERSON_OFFLINE_PROP_UUID = 507
PERSON_OFFLINE_PROP_WORLDID = 80
PERSON_OFFLINE_PROP_ZONEBATTLEDUEL_NOMATCH_REWARD = 517
PERSON_OFFLINE_PROP_ZONEBATTLEDUEL_WIN_REWARD = 516
PERSON_PROP_ACCELERATE_DASH_COUNT = 132
PERSON_PROP_ACCELERATE_POINTS_RATE = 148
PERSON_PROP_ACCELERATE_TRUCK_SPEED_RATE = 141
PERSON_PROP_AFK_OUTPUT_RATE = 40
PERSON_PROP_AFK_TIME = 39
PERSON_PROP_ALLIANCEDUEL_BATTLE_POINT = 55
PERSON_PROP_ALL_POINTS_INCREASE_RATE = 151
PERSON_PROP_AUTO_DISPATCH = 64
PERSON_PROP_AUTO_DISPATCH_MISSION = 133
PERSON_PROP_BARRACK_LIMIT_RATE = 46
PERSON_PROP_BATCH_COLLECT_RESOURCES = 136
PERSON_PROP_BATTLE_CROSS_RELOCATE = 56
PERSON_PROP_BIND_DIAMOND = 6
PERSON_PROP_BUILDING_POWER_POINTS_RATE = 152
PERSON_PROP_BUY_HERO_LIST_NUM = 7
PERSON_PROP_CAN_NOT_ADD_SHIELD = 128
PERSON_PROP_CAN_NOT_BE_ATTACKED = 129
PERSON_PROP_CAN_NOT_BE_GATHERED = 131
PERSON_PROP_CAN_NOT_BE_SPIED = 130
PERSON_PROP_CITY_MARCH_RATE = 93
PERSON_PROP_CITY_MARCH_RATE_1 = 94
PERSON_PROP_CITY_MARCH_RATE_2 = 95
PERSON_PROP_CITY_MARCH_RATE_3 = 96
PERSON_PROP_CITY_MARCH_RATE_4 = 97
PERSON_PROP_COIN = 4
PERSON_PROP_CONST_COST_REDUCE_RATE = 68
PERSON_PROP_CONST_SPEED_RATE = 67
PERSON_PROP_DAILY_TRUCK_INCREASE = 144
PERSON_PROP_DEFENSE_MARCH_RATE = 98
PERSON_PROP_DEFENSE_MARCH_RATE_1 = 99
PERSON_PROP_DEFENSE_MARCH_RATE_2 = 100
PERSON_PROP_DEFENSE_MARCH_RATE_3 = 101
PERSON_PROP_DEFENSE_MARCH_RATE_4 = 102
PERSON_PROP_DIAMOND = 5
PERSON_PROP_ENEMY_MARCH_RATE = 88
PERSON_PROP_ENEMY_MARCH_RATE_1 = 89
PERSON_PROP_ENEMY_MARCH_RATE_2 = 90
PERSON_PROP_ENEMY_MARCH_RATE_3 = 91
PERSON_PROP_ENEMY_MARCH_RATE_4 = 92
PERSON_PROP_EQUIP_COST_REDUCE_RATE = 42
PERSON_PROP_EQUIP_MAKE_SPEED_RATE = 41
PERSON_PROP_EXP = 2
PERSON_PROP_EXTRA_BARRACKS = 45
PERSON_PROP_EXTRA_FARM = 163
PERSON_PROP_EXTRA_GOLD = 165
PERSON_PROP_EXTRA_IRON = 164
PERSON_PROP_EXTRA_LOOT_ON_ATTACK = 142
PERSON_PROP_EXTRA_LOOT_ON_DEFEND = 143
PERSON_PROP_FACEID = 8
PERSON_PROP_FACEPROPID = 9
PERSON_PROP_FAST_CHALLENGE_EXPEDITION = 139
PERSON_PROP_FOOD = 25
PERSON_PROP_FOOD_GATHER_RATE = 108
PERSON_PROP_FOOD_GATHER_RATE_1 = 109
PERSON_PROP_FOOD_GATHER_RATE_2 = 110
PERSON_PROP_FOOD_GATHER_RATE_3 = 111
PERSON_PROP_FOOD_GATHER_RATE_4 = 112
PERSON_PROP_FOOD_OUTPUT_RATE = 30
PERSON_PROP_FOOD_SAFE_AMOUNT = 33
PERSON_PROP_FOOD_SAFE_INCREASE_RATE = 36
PERSON_PROP_FRAMEID = 16
PERSON_PROP_FREE_ADV_RECRUIT_CD = 43
PERSON_PROP_FREE_CONST_SPEED = 69
PERSON_PROP_FREE_SURV_RECRUIT_CD = 44
PERSON_PROP_FREE_TECH_SPEED = 72
PERSON_PROP_FREE_VIP_EXP = 22
PERSON_PROP_FROMWORLDID = 17
PERSON_PROP_GATHER_SPEED_RATE = 103
PERSON_PROP_GATHER_SPEED_RATE_1 = 104
PERSON_PROP_GATHER_SPEED_RATE_2 = 105
PERSON_PROP_GATHER_SPEED_RATE_3 = 106
PERSON_PROP_GATHER_SPEED_RATE_4 = 107
PERSON_PROP_GOLD_GATHER_RATE = 118
PERSON_PROP_GOLD_GATHER_RATE_1 = 119
PERSON_PROP_GOLD_GATHER_RATE_2 = 120
PERSON_PROP_GOLD_GATHER_RATE_3 = 121
PERSON_PROP_GOLD_GATHER_RATE_4 = 122
PERSON_PROP_GOLD_OUTPUT_RATE = 32
PERSON_PROP_GOLD_SAFE_AMOUNT = 35
PERSON_PROP_GOLD_SAFE_INCREASE_RATE = 38
PERSON_PROP_HELP_COUNT = 65
PERSON_PROP_HELP_TIME_REDUCE = 66
PERSON_PROP_HERO_RECRUIT_POINTS_RATE = 149
PERSON_PROP_HOSPITAL_CAPACITY = 49
PERSON_PROP_HOSPITAL_CAP_INCR_RATE = 50
PERSON_PROP_INCREASE_DISPATCH_QUEUE = 157
PERSON_PROP_INCREASE_SUPPLY_BOX_OUTPUT = 158
PERSON_PROP_INCREASE_TRUCK_CARRY_RATE = 140
PERSON_PROP_IRON_GATHER_RATE = 113
PERSON_PROP_IRON_GATHER_RATE_1 = 114
PERSON_PROP_IRON_GATHER_RATE_2 = 115
PERSON_PROP_IRON_GATHER_RATE_3 = 116
PERSON_PROP_IRON_GATHER_RATE_4 = 117
PERSON_PROP_IRON_OUTPUT_RATE = 31
PERSON_PROP_IRON_SAFE_AMOUNT = 34
PERSON_PROP_IRON_SAFE_INCREASE_RATE = 37
PERSON_PROP_KILL_ENEMY_POINTS_RATE = 155
PERSON_PROP_LASTONLINETIME = 162
PERSON_PROP_LINUP_POWER_1 = 75
PERSON_PROP_LOAD_BONUS_RATE = 123
PERSON_PROP_LOAD_BONUS_RATE_1 = 124
PERSON_PROP_LOAD_BONUS_RATE_2 = 125
PERSON_PROP_LOAD_BONUS_RATE_3 = 126
PERSON_PROP_LOAD_BONUS_RATE_4 = 127
PERSON_PROP_LV = 3
PERSON_PROP_MARCH_SPEED_RATE = 73
PERSON_PROP_MARCH_SPEED_RATE_1 = 74
PERSON_PROP_MARCH_SPEED_RATE_2 = 75
PERSON_PROP_MARCH_SPEED_RATE_3 = 76
PERSON_PROP_MARCH_SPEED_RATE_4 = 77
PERSON_PROP_MAXID = 168
PERSON_PROP_MAX_ATTACK_FAILURE_LIMIT = 145
PERSON_PROP_MAX_SOLDIERS_INCR_NUM = 160
PERSON_PROP_NATIONALFLAGID = 23
PERSON_PROP_PARADE_CAP_INCR_RATE = 54
PERSON_PROP_PARADE_GROUND = 53
PERSON_PROP_PDBID = 1
PERSON_PROP_PHOTO_STICKER_UNLOCK = 134
PERSON_PROP_PLATE = 167
PERSON_PROP_POSITIONID = 166
PERSON_PROP_POWER = 161
PERSON_PROP_RADAR_MISSION_TREASURE_BOX = 159
PERSON_PROP_RADAR_TASK_POINTS_RATE = 147
PERSON_PROP_RECHARGE_RMB = 18
PERSON_PROP_RECHARGE_RMBREAL = 21
PERSON_PROP_RECHARGE_USD = 19
PERSON_PROP_RESOURCE_MARCH_RATE = 83
PERSON_PROP_RESOURCE_MARCH_RATE_1 = 84
PERSON_PROP_RESOURCE_MARCH_RATE_2 = 85
PERSON_PROP_RESOURCE_MARCH_RATE_3 = 86
PERSON_PROP_RESOURCE_MARCH_RATE_4 = 87
PERSON_PROP_SCHLOSS = 27
PERSON_PROP_SCHLOSS_EFFECT = 29
PERSON_PROP_SEX = 10
PERSON_PROP_SHAKE_COLLECT_RESOURCES = 135
PERSON_PROP_SHOW_VIP_LEVEL_IN_CHAT = 137
PERSON_PROP_STEEL = 26
PERSON_PROP_SUPER_MODE_STEALTH_SQUAD = 138
PERSON_PROP_TECH_COST_REDUCE_RATE = 71
PERSON_PROP_TECH_POWER_POINTS_RATE = 153
PERSON_PROP_TECH_SPEED_RATE = 70
PERSON_PROP_TRADE_CONTRACT_REINDEER = 146
PERSON_PROP_TRAINING_COST_REDUCE_RATE = 48
PERSON_PROP_TRAINING_SPEED_RATE = 47
PERSON_PROP_TRAIN_SOLDIER_POINTS_RATE = 154
PERSON_PROP_TREATMENT_COST_RATE = 52
PERSON_PROP_TREATMENT_SPEED_RATE = 51
PERSON_PROP_UDBID = 13
PERSON_PROP_UNIT_ATT_BONUS = 56
PERSON_PROP_UNIT_ATT_INCR_RATE = 59
PERSON_PROP_UNIT_DEF_BONUS = 57
PERSON_PROP_UNIT_DEF_INCR_RATE = 60
PERSON_PROP_UNIT_HP_BONUS = 55
PERSON_PROP_UNIT_HP_INCR_RATE = 58
PERSON_PROP_UNIT_LOAD_INCR_RATE = 62
PERSON_PROP_UNIT_MORALE = 61
PERSON_PROP_UNKNOW = 0
PERSON_PROP_UNLOCK_REWARDS_TIER_4_TO_6 = 150
PERSON_PROP_UNLOCK_REWARDS_TIER_7_TO_9 = 156
PERSON_PROP_UNLOCK_T10_SOLDIERS = 63
PERSON_PROP_VALUABLE_COIN = 20
PERSON_PROP_VIP_EXP = 12
PERSON_PROP_VIP_EXPIRED = 28
PERSON_PROP_VIP_EXP_UPDATE_TIME = 24
PERSON_PROP_VIP_LV = 11
PERSON_PROP_ZOMBIE_MARCH_RATE = 78
PERSON_PROP_ZOMBIE_MARCH_RATE_1 = 79
PERSON_PROP_ZOMBIE_MARCH_RATE_2 = 80
PERSON_PROP_ZOMBIE_MARCH_RATE_3 = 81
PERSON_PROP_ZOMBIE_MARCH_RATE_4 = 82
PERSON_UNIVERSE_STAR = 14
PERSON_ZONE_BATTLE_SCORE_NUM = 54
PHYSICAL_DMG_BOOST = 16
PHYSICAL_DMG_REDUCTION = 17
PULL_TRUCK_ATTACK_BOOST = 225
PULL_TRUCK_DEFENSE_BOOST = 226
PropType_Person = 1
PropType_PersonAdditional = 2
PropType_PersonOffline = 3
PropType_Unknow = 0
RoleProp =M(M2G)
SIGIL_PROP_EXTEND_PRO_1 = 14
SIGIL_PROP_EXTEND_PRO_2 = 15
SIGIL_PROP_EXTEND_PRO_3 = 16
SIGIL_PROP_EXTEND_PRO_4 = 17
SIGIL_PROP_MAXID = 18
SIGIL_PROP_PRO_1 = 10
SIGIL_PROP_PRO_2 = 11
SIGIL_PROP_PRO_3 = 12
SIGIL_PROP_PRO_4 = 13
SIGIL_PROP_STR_LV = 9
SIGIL_PROP_UNKNOW = 8
SKEP_TYPE_ASHDUNGEON = 9
SKEP_TYPE_DRONESTAR = 13
SKEP_TYPE_IDLEPACK = 4
SKEP_TYPE_MAX = 14
SKEP_TYPE_MAZE = 5
SKEP_TYPE_MAZETEMP = 6
SKEP_TYPE_PACK = 1
SKEP_TYPE_PALDECORATE = 11
SKEP_TYPE_PALEQUIP = 2
SKEP_TYPE_PALSIGIL = 10
SKEP_TYPE_PALSKILL = 3
SKEP_TYPE_PEAK = 7
SKEP_TYPE_PEAKTEMP = 8
SKEP_TYPE_STARDIAMOND = 12
SKEP_TYPE_UNKNOW = 0
SKILL_DMG_BOOST = 27
SKILL_PROP_HERO_SKEPID_1 = 21
SKILL_PROP_HERO_SKEPID_2 = 22
SKILL_PROP_HERO_SKEPID_3 = 23
SKILL_PROP_HERO_SKEPID_4 = 24
SKILL_PROP_HERO_SKEPID_5 = 25
SKILL_PROP_HERO_SKEPID_6 = 26
SKILL_PROP_MAXID = 27
SKILL_PROP_PRO_1 = 11
SKILL_PROP_PRO_2 = 12
SKILL_PROP_PRO_3 = 13
SKILL_PROP_PRO_4 = 14
SKILL_PROP_PRO_5 = 15
SKILL_PROP_PRO_VALUE_1 = 16
SKILL_PROP_PRO_VALUE_2 = 17
SKILL_PROP_PRO_VALUE_3 = 18
SKILL_PROP_PRO_VALUE_4 = 19
SKILL_PROP_PRO_VALUE_5 = 20
SKILL_PROP_STR_LV = 9
SKILL_PROP_UNKNOW = 8
SKILL_PROP_USE_COUNT = 10
STARDIAMOND_PROP_LOCK = 9
STARDIAMOND_PROP_MAXID = 12
STARDIAMOND_PROP_PALSID = 10
STARDIAMOND_PROP_STR_LV = 11
STARDIAMOND_PROP_UNKNOW = 8
SoulLinkSlotStat =M(M49G)
TActorFormData =M(M5G)
TActorFormData.TFormBase =M(M6G)
TActorFormData.TFormEquip =M(M7G)
TAllianceTechPart =M(M40G)
TBattleshipTechDataPart =M(M52G)
TBuyData =M(M85G)
TCfgLineUpData =M(M89G)
TCstLineUpData =M(M90G)
TCustomFaceContext =M(M37G)
TCustomTitleContext =M(M35G)
TCustomTitleData =M(M36G)
TEntityData =M(M64G)
TFaceDataPart =M(M39G)
TFramAchvData =M(M43G)
TFramDataPart =M(M44G)
TFramSoil =M(M42G)
TGalaxyPlanetData =M(M55G)
TGoodsData =M(M10G)
TGuideData =M(M29G)
THaloDataPart =M(M41G)
TINVITED_INFO =M(M88G)
TIdlePart =M(M27G)
TMSG_BATTLE_TBS_NTF =M(M93G)
TMSG_OPERATIVE_PART_DATA =M(M91G)
TMSG_PLAYER_LOGIN_FINISH_NOTIFY =M(M94G)
TMSG_PROP_CREATEENTITY_NTF =M(M63G)
TMSG_PROP_PERSONOFFLINE_NTF =M(M60G)
TMSG_PROP_PERSONOFFLINE_NTF.TProp_PersonOffline =M(M61G)
TMSG_PROP_UPDATE_NTF =M(M66G)
TMSG_PROP_UPDATE_NTFS =M(M67G)
TMSG_PROP_UPDATE_TOPICS_NTF =M(M72G)
TMSG_PROP_UPDATE_TOPIC_NTF =M(M70G)
TMSG_ROGUE_ENTITY_CREATE_NTF =M(M68G)
TMSG_ROGUE_ENTITY_DEL_NTF =M(M69G)
TMSG_ROLEPROP_LOGIN_NTF =M(M4G)
TMSG_ROLEPROP_UPDATE_NTF =M(M3G)
TMSG_ROLE_LOGIN_KEY_NTF =M(M84G)
TMSG_SEVENCHALLENGE_UPDATE_NTF =M(M87G)
TMSG_TRIALHERO_UPDATE_NTF =M(M26G)
TMSG_ZONE_SERVER_TIMESTAMP_NTF =M(M73G)
TMakerData =M(M98G)
TMapTypeData =M(M1G)
TMillData =M(M20G)
TPacketPart =M(M21G)
TPalData =M(M11G)
TPalHBookData =M(M13G)
TPalPart =M(M25G)
TPalRolePlot =M(M14G)
TPartData =M(M8G)
TPbTaskData =M(M33G)
TPbTaskPartData =M(M34G)
TPickTheRoute =M(M96G)
TPickTheRouteArchivedData =M(M95G)
TProfessionTechData =M(M18G)
TPropData =M(M9G)
TResourcesPlanetPlanetData =M(M53G)
TSevenChallengePartData =M(M86G)
TSkepData =M(M16G)
TSoulLinkMoudleDataPart =M(M50G)
TSoulLinkPropUpdata =M(M48G)
TSpaceExplorationDataPart =M(M58G)
TSpaceExplorationProp =M(M56G)
TStagePlot =M(M30G)
TStarTempleDataPart =M(M59G)
TSubGiftPartData =M(M92G)
TSubjectPart =M(M32G)
TTechData =M(M17G)
TTopicData =M(M31G)
TTreasureData =M(M97G)
TTreasureRarePart =M(M99G)
TTrialPal =M(M24G)
TTrialPalBattleTimes =M(M22G)
TUnlockWeapon =M(M46G)
TVeinPlanetData =M(M54G)
TWeaponDataPart =M(M47G)
TWeaponPart =M(M45G)
TeamX_AttackBase_AttackBoost = 275
TeamX_AttackBase_DefenseBoost = 276
TeamX_AttackBase_HealthBoost = 274
TeamX_DefendCity_AttackBoost = 278
TeamX_DefendCity_DefenseBoost = 279
TeamX_DefendCity_HealthBoost = 277
TeamX_HeroAttack = 272
TeamX_HeroDefense = 273
TeamX_HeroHealth = 271
TopicData =M(M71G)
TrialHeroBattleType_AshDungeon = 7
TrialHeroBattleType_ChinaRed = 3
TrialHeroBattleType_CrystalCrown = 9
TrialHeroBattleType_Friend = 8
TrialHeroBattleType_IdlePassStage = 4
TrialHeroBattleType_IllusionTower = 5
TrialHeroBattleType_LeaActivityBoss = 6
TrialHeroBattleType_Legend = 10
TrialHeroBattleType_Max = 11
TrialHeroBattleType_Maze = 1
TrialHeroBattleType_Peak = 2
emHeroCamp_Abyss = 3
emHeroCamp_Dark = 5
emHeroCamp_Dim = 1
emHeroCamp_Forest = 4
emHeroCamp_Fort = 2
emHeroCamp_Light = 6
emHeroCamp_Max = 7
emHeroCamp_min = 0
emTechAddCamp_Abyss = 3
emTechAddCamp_AllDepartment = 9
emTechAddCamp_Dark = 5
emTechAddCamp_DarkLight = 8
emTechAddCamp_Dim = 1
emTechAddCamp_Forest = 4
emTechAddCamp_Fort = 2
emTechAddCamp_FourDepartment = 7
emTechAddCamp_Light = 6
emTechAddCamp_Max = 10
emTechAddCamp_min = 0
enFuncPro_Carriage_BaseRotio = 1000
enFuncPro_Max = 1100
enGoods = 3
enHero = 2
enPerson = 1
enPro_Attack = 2
enPro_Attack_Rate = 16
enPro_Base_Attack_Rate = 33
enPro_Base_Defend_Rate = 34
enPro_Base_HP_Rate = 32
enPro_Base_Speed_Rate = 35
enPro_Block = 12
enPro_Break = 13
enPro_Break_Trend = 53
enPro_CK_Damage_Rate = 30
enPro_CR_Damege_Rate = 38
enPro_DL_Damege_Rate = 37
enPro_Defend = 3
enPro_Defend_Rate = 17
enPro_Dodge = 10
enPro_Effect_Hit = 7
enPro_Effect_Resist = 8
enPro_Energy = 20
enPro_Ex_Attack = 50
enPro_Ex_Defence = 51
enPro_Ex_Hp = 49
enPro_Ex_Speed = 52
enPro_Extra_Skill_Points_Rate = 26
enPro_FS_Damage_Rate = 28
enPro_Fatal = 5
enPro_Fatal_Defend = 45
enPro_Fatal_Reduce_Rate = 36
enPro_Fatal_Scale = 6
enPro_Fatal_Scale_Defend = 46
enPro_Global_Attack_Rate = 23
enPro_Global_Defend_Rate = 24
enPro_Global_HP_Rate = 22
enPro_Global_Speed_Rate = 25
enPro_HP = 1
enPro_HP_Rate = 15
enPro_Hit = 9
enPro_Holy_Damage_Rate = 21
enPro_Immune = 14
enPro_KJ_Damege_Rate = 39
enPro_MS_Damage_Rate = 31
enPro_Parry = 54
enPro_Pierce = 11
enPro_Power_Attack = 48
enPro_Power_Defend = 47
enPro_SM_Damege_Rate = 42
enPro_Skill_Damage_Rate = 19
enPro_Speed = 4
enPro_Speed_Rate = 18
enPro_TS_Damege_Fix_Rate = 44
enPro_TS_Damege_Rate = 43
enPro_YX_Damage_Rate = 29
enPro_YZ_Damege_Rate = 41
enPro_ZR_Damege_Rate = 40
enPro_ZS_Damage_Rate = 27
enProdPro_AFK_Output_Rate = 308
enProdPro_AFK_Time = 307
enProdPro_Accelerate_Dash_Count = 601
enProdPro_Accelerate_Points_Rate = 709
enProdPro_Accelerate_Truck_Speed_Rate = 702
enProdPro_All_Points_Increase_Rate = 712
enProdPro_Auto_Dispatch = 501
enProdPro_Auto_Dispatch_Mission = 602
enProdPro_Barrack_Limit_Rate = 401
enProdPro_Batch_Collect_Resources = 605
enProdPro_Building_Power_Points_Rate = 713
enProdPro_Can_Not_Add_Shield = 521
enProdPro_Can_Not_Be_Attacked = 522
enProdPro_Can_Not_Be_Gathered = 524
enProdPro_Can_Not_Be_Spied = 523
enProdPro_City_March_Rate = 514
enProdPro_Const_Cost_Reduce_Rate = 505
enProdPro_Const_Speed_Rate = 504
enProdPro_Daily_Truck_Increase = 705
enProdPro_Defense_March_Rate = 515
enProdPro_DesertStromm_Auto_Cure_Soilder = 1003
enProdPro_DesertStromm_MoveCity_Speed_Rate = 1001
enProdPro_DesertStromm_Score_Speed_Rate = 1002
enProdPro_Enemy_March_Rate = 513
enProdPro_Equip_Cost_Reduce_Rate = 310
enProdPro_Equip_Make_Speed_Rate = 309
enProdPro_Extra_Barracks = 313
enProdPro_Extra_Farm = 317
enProdPro_Extra_Gold = 319
enProdPro_Extra_Iron = 318
enProdPro_Extra_Loot_On_Attack = 703
enProdPro_Extra_Loot_On_Defend = 704
enProdPro_Fast_Challenge_Expedition = 608
enProdPro_Food_Gather_Rate = 517
enProdPro_Food_Output_Rate = 301
enProdPro_Food_Safe_Amount = 314
enProdPro_Food_Safe_Increase_Rate = 304
enProdPro_Free_Adv_Recruit_CD = 311
enProdPro_Free_Const_Speed = 506
enProdPro_Free_Surv_Recruit_CD = 312
enProdPro_Free_Tech_Speed = 509
enProdPro_Gather_Speed_Rate = 516
enProdPro_Gold_Gather_Rate = 519
enProdPro_Gold_Output_Rate = 303
enProdPro_Gold_Safe_Amount = 316
enProdPro_Gold_Safe_Increase_Rate = 306
enProdPro_Help_Count = 502
enProdPro_Help_Time_Reduce = 503
enProdPro_Hero_Recruit_Points_Rate = 710
enProdPro_Hospital_Cap_Incr_Rate = 405
enProdPro_Hospital_Capacity = 404
enProdPro_Increase_Dispatch_Queue = 718
enProdPro_Increase_Supply_Box_Output = 719
enProdPro_Increase_Truck_Carry_Rate = 701
enProdPro_Iron_Gather_Rate = 518
enProdPro_Iron_Output_Rate = 302
enProdPro_Iron_Safe_Amount = 315
enProdPro_Iron_Safe_Increase_Rate = 305
enProdPro_Kill_Enemy_Points_Rate = 716
enProdPro_Load_Bonus_Rate = 520
enProdPro_March_Speed_Rate = 510
enProdPro_Max_Attack_Failure_Limit = 706
enProdPro_Max_Soldiers_Incr_Num = 419
enProdPro_Parade_Cap_Incr_Rate = 409
enProdPro_Parade_Ground = 408
enProdPro_Photo_Sticker_Unlock = 603
enProdPro_Radar_Mission_Treasure_Box = 720
enProdPro_Radar_Mission_Treasure_Box_Id = 722
enProdPro_Radar_Mission_Treasure_Box_Max = 721
enProdPro_Radar_Task_Points_Rate = 708
enProdPro_Resource_March_Rate = 512
enProdPro_Shake_Collect_Resources = 604
enProdPro_Show_VIP_Level_In_Chat = 606
enProdPro_Super_Mode_Stealth_Squad = 607
enProdPro_Tech_Cost_Reduce_Rate = 508
enProdPro_Tech_Power_Points_Rate = 714
enProdPro_Tech_Speed_Rate = 507
enProdPro_Trade_Contract_Reindeer = 707
enProdPro_Train_Soldier_Points_Rate = 715
enProdPro_Training_Cost_Reduce_Rate = 403
enProdPro_Training_Speed_Rate = 402
enProdPro_Treatment_Cost_Rate = 407
enProdPro_Treatment_Speed_Rate = 406
enProdPro_Unit_Att_Bonus = 411
enProdPro_Unit_Att_Incr_Rate = 414
enProdPro_Unit_Def_Bonus = 412
enProdPro_Unit_Def_Incr_Rate = 415
enProdPro_Unit_HP_Bonus = 410
enProdPro_Unit_HP_Incr_Rate = 413
enProdPro_Unit_Load_Incr_Rate = 417
enProdPro_Unit_Morale = 416
enProdPro_Unlock_Rewards_Tier_4_to_6 = 711
enProdPro_Unlock_Rewards_Tier_7_to_9 = 717
enProdPro_Unlock_T10_Soldiers = 418
enProdPro_Zombie_March_Rate = 511
enRolePlot_HaveUnlockHaveAward = 3
enRolePlot_HaveUnlockedNoAward = 2
enRolePlot_NotUnlock = 1
enTask_AllianceDuel = 18
enTask_ArmsRace = 6
enTask_BranchTask = 4
enTask_CampTrial = 5
enTask_ChapterTask = 2
enTask_CommanderWeek = 12
enTask_Competition = 20
enTask_DailyTask = 1
enTask_FirstBuy = 37
enTask_LoginReward = 26
enTask_Lord_Tour = 22
enTask_MainTask = 3
enTask_MonstersApproaching = 35
enTask_RankTask = 24
enTask_SkyFall = 36
enTask_WorldBossTask = 13
enTask_ZombieTreasure = 38
enTask_ZoneBattleDuelTask = 29
tDynamicDiffiPartData =M(M77G)
tGoldHandBuyRecord =M(M79G)
tGoldHandData =M(M80G)
tRewardRandData =M(M74G)
tRewardRandPartData =M(M76G)
tRolePartCommonData =M(M83G)
tSpaceDominatorDataPart =M(M78G)
tSupremeBoxGuarantee =M(M82G)
tSupremeBoxGuaranteeData =M(M81G)
tTalentCardData =M(M75G)

