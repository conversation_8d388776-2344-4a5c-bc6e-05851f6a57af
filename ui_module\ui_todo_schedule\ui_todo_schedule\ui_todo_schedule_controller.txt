local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local os = os
local string = string
local table = table
local newClass = newclass
local type = type

local player_prefs = require "player_prefs"
local todo_schedule_define = require "todo_schedule_define"
local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"

--region Controller Life
module("ui_todo_schedule_controller")
local controller = nil
local UIController = newClass("ui_todo_schedule_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.CData = {
        showListType = todo_schedule_define.ShowListType.none,
        isTodayList = true,
        isServerTime = false,
        isInit = false,
    }
    self.__base.Init(self, view_name, controller_name) 
    self:SetListData()
    self:TriggerUIEvent("RefreshTogShow")
    
    --保存打开时间
    player_prefs.SetCacheData(todo_schedule_define.SCHEDULE_OPEN_TIME, os.server_time())
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close(data)   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end

    local todo_schedule_mgr = require "todo_schedule_mgr"
    local _, list = todo_schedule_mgr.GetCurShowList(todo_schedule_define.ShowListType.todayActivity)
    if list then
        local curTime = os.server_time()
        for i, v in ipairs(list) do
            if v.stateType == todo_schedule_define.ActivityStateType.doing then
                local atyId = v.cfg.atyId
                --存
                local key = string.format("%s_%d",todo_schedule_define.SCHEDULE_ACTIVITY_TODAY_IS_LOOK,atyId)
                player_prefs.SetCacheData(key, curTime)
            end
        end
    end
    todo_schedule_mgr.UpdateTaskRedByAlliance()
end

function UIController:AutoSubscribeEvents() 
    local taskListUpdate = function()
        local todo_schedule_mgr = require "todo_schedule_mgr"
        if self.CData.showListType == todo_schedule_define.ShowListType.R4R5Task then
            if not todo_schedule_mgr.GetIsShowR4R5Page() then
                --任务页签关闭
                self.CData.isInit = false
                self.CData.showListType = todo_schedule_define.ShowListType.none
            end
            self:SetListData()
        end
        self:TriggerUIEvent("RefreshTogShow")
    end
    self:RegisterEvent(todo_schedule_define.UPDATE_SCHEDULE_R4R5_TASK, taskListUpdate)
end

function UIController:AutoUnsubscribeEvents() 
end
--endregion

--region Controller Logic

function UIController:SetListData()
    local todo_schedule_mgr = require "todo_schedule_mgr"
    local list
    if self.CData.showListType == todo_schedule_define.ShowListType.none then
        self.CData.showListType, list = todo_schedule_mgr.GetCurShowList()
    else
        _,list = todo_schedule_mgr.GetCurShowList(self.CData.showListType)
    end
    if not list then
        return
    end
    if not self.CData.isInit then
        self.CData.isInit = true
        self:TriggerUIEvent("SetPageBtn", self.CData.showListType)
    end
    self:TriggerUIEvent("SetListData", list, self.CData.showListType == todo_schedule_define.ShowListType.R4R5Task)
end

function  UIController:OnTogTodayListValueChange(state)
    if state then
        self.CData.showListType = todo_schedule_define.ShowListType.todayActivity
        self:SetListData()
    end
end

function  UIController:OnTogTaskListValueChange(state)
    if state then
        self.CData.showListType = todo_schedule_define.ShowListType.R4R5Task
        self:SetListData()
    end
end

function  UIController:OnTogNextDayListValueChange(state)
    if state then
        self.CData.showListType = todo_schedule_define.ShowListType.nextDayActivity
        self:SetListData()
    end
end

function UIController:OnBtnTipBtnClickedProxy()
    local ui_help = require "ui_help"
    ui_help.ShowWithDate(10098)
end

function  UIController:OnBtnChangeTimeTypeClickedProxy()
    self.CData.isServerTime = not self.CData.isServerTime
    local langID = self.CData.isServerTime and 1004227 or 1004228
    local flow_text = require "flow_text"
    flow_text.Add(lang.Get(langID))
    self:TriggerUIEvent("SetCurTimeType", self.CData.isServerTime)
end


function  UIController:OnBtnCloseBtnClickedProxy()
    self:CloseView()
end

--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
