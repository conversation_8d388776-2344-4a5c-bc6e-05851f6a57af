local require = require
local util = require "util"
local ReviewingUtil 	= require "ReviewingUtil"
local IsReviewing = ReviewingUtil.IsReviewing()
local math = math
local OldResVersionManager = CS.War.Base.OldResVersionManager
local SplitServerResVerMgr = CS.War.Base.SplitServerResVerMgr

module("split_server_res_ver_mgr")
local cache = {}
local requestTimeout = 21

function GetServerMaxResVer(serverId)
    return cache.serverResVerList[serverId].minVersion
end

function GetServerMinResVer(serverId)
    return cache.serverResVerList[serverId].maxVersion
end

function IsCanUseSplitServerRes()
    return not IsReviewing and IsUseOldResVersion() and IsEnableSplitServerRes()
end

--是否开启分服热更
function IsEnableSplitServerRes()
    if cache.isEnableSplitServerRes == nil then
        cache.isEnableSplitServerRes = util.IsCSharpClass(SplitServerResVerMgr) and SplitServerResVerMgr.IsOpenSplitServerRes == true
    end
    return cache.isEnableSplitServerRes
end

--是否使用老版本
function IsUseOldResVersion()
    if cache.isUseOldResVersion == nil then
        cache.isUseOldResVersion = util.IsCSharpClass(OldResVersionManager) and OldResVersionManager.IS_OLD_RES_VERSION
    end
    return cache.isUseOldResVersion
end

function GetCurUseFilesVer()
    cache.curOldResVersion = cache.curOldResVersion or OldResVersionManager.currentOldResVersion
    return cache.curOldResVersion
end

function GetRemoteFilesVer()
    cache.remoteFilesUrlVer = cache.remoteFilesUrlVer or OldResVersionManager.remoteFilesUrlVer
    return cache.remoteFilesUrlVer
end

--更新远端版本
function UpdateRemoteFilesVer(version)
    cache.remoteFilesUrlVer = version
end

function GetStreamingLocalFilesVer()
    cache.streamingLocalFilesVer = cache.streamingLocalFilesVer or OldResVersionManager.streamingLocalFilesVer
    return cache.streamingLocalFilesVer
end

function GetUpdateJsonUrl()
    local files_version_mgr = require "files_version_mgr"
    return files_version_mgr.GetUpdateJsonUrl()
end

function GetHashRemoteVirtualVerList()
    if cache.hashRemoteVirtualVerList == nil then
        cache.hashRemoteVirtualVerList = SplitServerResVerMgr.GetHashRemoteVirtualVerList()
    end
    return cache.hashRemoteVirtualVerList
end

function AddDownloadFinishVersion(version)
    if not util.IsObjNull(cache.hashRemoteVirtualVerList) then
        cache.hashRemoteVirtualVerList:Add(version)
    end
end

--后台下载
function BgDownloadCurServerRes()
    if not IsCanUseSplitServerRes() then
        return
    end
    local setting_server_data = require"setting_server_data"
    local curServerId = setting_server_data.GetLoginWorldID()
    local downloadVersion = GetCurUseFilesVer()

    local split_define = require "split_server_res_define"
    local verTaskType = split_define.VerTaskType.BgDownload
    local split_server_res_download_mgr = require"split_server_res_download_mgr"
    split_server_res_download_mgr.AddVersionTaskToList(downloadVersion, curServerId, verTaskType)
end

--预下载
function PreDownloadCurServerRes(preDownloadVersion)
    if not IsCanUseSplitServerRes() then
        return
    end

    local curServerId = setting_server_data.GetLoginWorldID()
    local split_define = require "split_server_res_define"
    local verTaskType = split_define.VerTaskType.PreDownload
    local split_server_res_download_mgr = require"split_server_res_download_mgr"
    split_server_res_download_mgr.AddVersionTaskToList(preDownloadVersion, curServerId, verTaskType)
end

--切服下载
function DownloadSwitchServerRes(severId, downloadVersion)
    if not IsCanUseSplitServerRes() then
        return 
    end
    local remoteFilesUrlVer = GetRemoteFilesVer()
    downloadVersion = math.min(downloadVersion, remoteFilesUrlVer)

    local split_define = require "split_server_res_define"
    local verTaskType = split_define.VerTaskType.SwitchServerResDownload
    local split_server_res_download_mgr = require"split_server_res_download_mgr"
    split_server_res_download_mgr.AddVersionTaskToList(downloadVersion, severId, verTaskType)
end

function CheckSwitchServerIsNeedDownload(severId)
    if not IsCanUseSplitServerRes() then
        return false
    end
    local index = SplitServerResVerMgr.GetIndexInSplitServerInfo(severId)
    local serverMinResVer = SplitServerResVerMgr.GetServerMinResVer(index)
    local serverMaxResVer = SplitServerResVerMgr.GetServerMaxResVer(index)
    local isExist, version = CheckServerResVersionsIsExist(serverMinResVer, serverMaxResVer)
    if not isExist then
        return true, version
    end
    return false
end

--检测本地是否存在服务器资源版本区间中的某个版本
function CheckServerResVersionsIsExist(minVersion, maxVersion)
    if minVersion == nil then
        return false
    end

    local remoteFilesUrlVer = GetRemoteFilesVer()
    maxVersion = maxVersion or minVersion
    maxVersion = math.min(maxVersion, remoteFilesUrlVer)

    local isExist, existMaxVersion = IsExistInRemoteVirtual(minVersion, maxVersion)
    if isExist and existMaxVersion > 0 then
        return true, existMaxVersion
    end

    local  streamingLocalFilesVer = GetStreamingLocalFilesVer()
    if minVersion <= streamingLocalFilesVer and streamingLocalFilesVer <= maxVersion then
        return true, streamingLocalFilesVer
    end
    return false
end

--检测本地是否存在服务器资源版本区间中的最大版本号 
function CheckServerMaxResVersionIsExist(maxVersion)
    if maxVersion == nil then
        return false
    end

    local remoteFilesUrlVer = GetRemoteFilesVer()
    maxVersion = math.min(maxVersion, remoteFilesUrlVer)

    local isExist, existMaxVersion = IsExistInRemoteVirtual(maxVersion, maxVersion)
    if isExist and existMaxVersion > 0 then
        return true
    end

    local  streamingLocalFilesVer = GetStreamingLocalFilesVer()
    if minVersion <= streamingLocalFilesVer and streamingLocalFilesVer <= maxVersion then
        return true
    end

    return false
end

function IsExistInRemoteVirtual(minVersion, maxVersion)
    local remoteVirtualVerList = GetHashRemoteVirtualVerList()
    local isExistInRemoteVir = false
    local existMaxVersion = 0
    maxVersion = maxVersion or minVersion
    
    for k = maxVersion, minVersion, -1 do
        for i = 0, remoteVirtualVerList.Count - 1 do
            if remoteVirtualVerList[i] == k then
                existMaxVersion = k
                isExistInRemoteVir = true
                break
            end
        end
    end
    return isExistInRemoteVir, existMaxVersion
end

function Init()
    if cache.isInit then
        return
    end
    cache.isInit = true

    if IsCanUseSplitServerRes() then
        cache.isGainRemoteConfig = not util.IsObjNull(SplitServerResVerMgr.RemoteSplitServerInfo)
        if not IsGainRemoteConfig() then
            RequestSplitServerInfo()
            cache.reqTimeoutCheckTimer = util.DelayCall(requestTimeout, function()
                if not IsGainRemoteConfig() then
                    RequestSplitServerInfo()
                    return requestTimeout
                end
            end)
        else
            StartPredownloadCheck()
        end
    end
end

function RequestSplitServerInfo()
    SplitServerResVerMgr.RequestSplitServerInfo(function(obj)
        if not util.IsObjNull(obj) then
            RemoveReqTimeoutCheckTimer()
            StartPredownloadCheck()
            cache.isGainRemoteConfig = true
        end
    end)
end

function RemoveReqTimeoutCheckTimer()
    if cache.reqTimeoutCheckTimer then
        util.RemoveDelayCall(cache.reqTimeoutCheckTimer)
        cache.reqTimeoutCheckTimer = nil
    end
end

function IsGainRemoteConfig()
    return cache.isGainRemoteConfig
end

function StartPredownloadCheck(isImmediate)
    if isImmediate then
        local split_server_res_predownload_checker = require"split_server_res_predownload_checker"
        split_server_res_predownload_checker.RequestUpdateJson()
    else
        local split_define = require "split_server_res_define"
        util.DelayCallOne(split_define.predownloadStartCheckDelay, function()
            local split_server_res_predownload_checker = require"split_server_res_predownload_checker"
            split_server_res_predownload_checker.StartCheck()
        end)
    end
end

Init()