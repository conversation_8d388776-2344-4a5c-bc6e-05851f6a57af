---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by linsh22022102.
--- DateTime: 2024/7/6 14:37


--初始化数据

local gw_admin = require "gw_admin"
local gw_sand_event_define = require "gw_sand_event_define"
local event_alliance_define = require "event_alliance_define"
local gw_march_common_util = require "gw_march_common_util"
local flow_text = require "flow_text"
local sand_ui_data = require "sand_ui_data"
local sand_loot_data = require "sand_loot_data"
local gw_ed = require("gw_ed")
local event = require "event"
local sand_ui_event_define = require "sand_ui_event_define"
local net_sandbox_module = require "net_sandbox_module"
local sandbox_pb = require "sandbox_pb"
local sand_reinforce_data = require "sand_reinforce_data"
local evt_sourceSupply_define = require "evt_sourceSupply_define"
local gw_const = require "gw_const"
local topic_pb = require("topic_pb")

local ipairs = ipairs
local pairs = pairs
local math = math
local next = next
local string = string
local util = require "util"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"

local common_pb = require "common_pb"
local game_scheme = require "game_scheme"
local Edump = Edump

local ui_war_rally_mgr = require "ui_war_rally_mgr"

local M = {}

local tipsTable = {
    [gw_const.ESEntityResViewType.Base] = "ui_sand_player_tips",
    [gw_const.ESEntityResViewType.NeutralCity] = "ui_sand_city_tips",
    [gw_const.ESEntityResViewType.FixedMonster] = "ui_sand_monster_tips",
    [gw_const.ESEntityResViewType.Resource] = "ui_sand_resource_tips",
    [gw_const.ESEntityResViewType.WonderMonster] = "ui_sand_wonder_monster_tips",

    [gw_const.ESEntityResViewType.RadarDemonCastle] = "ui_radar_demon_tips", --雷达恶魔城堡任务 
    [gw_const.ESEntityResViewType.RadarBeastInvasion] = "ui_radar_beast_invasion_tips", --雷达野兽入侵
    [gw_const.ESEntityResViewType.RadarEnvironmentExplorate] = "ui_radar_environment_tips", --雷达任务-环境勘探
    [gw_const.ESEntityResViewType.RadarCollection] = "ui_radar_resource_tips", --雷达采集
    [gw_const.ESEntityResViewType.RadarChallenge] = "ui_radar_challenge_tips", --雷达挑战任务
    [gw_const.ESEntityResViewType.RadarTreasure] = "ui_radar_dig_treasures_tips", --雷达探宝
    [gw_const.ESEntityResViewType.WorldBoss] = "ui_sand_world_boss_tips", --世界boss
    [gw_const.ESEntityResViewType.ZombiesAttackBoss] = "ui_zombies_attack_sand_monster_tip", --宝藏亡灵首领
    [gw_const.ESEntityResViewType.Carriage] = "ui_sand_trucks_tip", -- 城际货车
    [gw_const.ESEntityResViewType.AllianceTrain] = "ui_sand_alliancetrain_tip", --联盟火车

    --[gw_const.ESEntityResViewType.ComActivityMonster] = "ui_activity_MonsterTip", --通用活动怪物
    [gw_const.ESEntityResViewType.GeneralTrialSingle] = "ui_activity_MonsterTip", --将军试炼个人怪弹窗，将军试炼占用
    [gw_const.ESEntityResViewType.GeneralTrialLeague] = "ui_activity_MonsterTip", --将军试炼同盟，将军试炼占用
    [gw_const.ESEntityResViewType.AllianceBoss] = "ui_sand_alliance_boss_tips", --同盟bossTips
    [gw_const.ESEntityResViewType.TavernTask] = "ui_tavern_tips", --酒馆任务Tips

    [gw_const.ESEntityResViewType.BigGun] = "ui_sand_big_gun_tip", -- 巨炮点击面板
    ----沙漠风暴
    [gw_const.ESEntityResViewType.StormBuild] = "ui_storm_build_tips", --沙漠风暴通用建筑
    [gw_const.ESEntityResViewType.StormOil] = "ui_storm_oil_tips", --沙漠风暴油田
    [gw_const.ESEntityResViewType.StormBox] = "ui_storm_box_tips", --沙漠风暴通用宝箱  

    [gw_const.ESEntityResViewType.ZombieStorm] = "ui_zombie_storm_base_tips",

    [gw_const.ESEntityResViewType.Camp] = "ui_sand_camp_tips", --营寨

}

local PopWindowTable = {
    ["ShowSpeedUp"] = "ui_sand_marching_acceleration",
    ["ShowPlayerInfo"] = "ui_view_info",
    ['ShowBaseOperate'] = 'ui_sand_advanced_relocation',
    ['ShowHeroSelectPanel'] = "ui_new_hero_select_panel",
}

local ResetLevelWindowTable = {
    ["ui_sand_attack"] = true,
}

---污染之地打不开的雷达实体的详情配置(true 为打不开)
local SandRadarEntityType = {
    [gw_const.ESEntityResViewType.RadarDemonCastle] = true,
    [gw_const.ESEntityResViewType.RadarBeastInvasion] = true,
    [gw_const.ESEntityResViewType.RadarEnvironmentExplorate] = true,
    [gw_const.ESEntityResViewType.RadarCollection] = true,
    [gw_const.ESEntityResViewType.RadarChallenge] = true,
    [gw_const.ESEntityResViewType.RadarTreasure] = true,
}

-- region 私有方法
local logger = require("logger").new("sw_log_sandbox_ui_mgr", 0)
local LogWarning = function(...)
    logger.Warning(2, "[sandbox_ui_mgr]", ...)
end

local Log = function(...)
    --logger.lprint("[sandbox_ui_mgr]", ...)
end

local Error = function(...)
    logger.Warning0(1, "[sandbox_ui_mgr]", ...)
end

local function GetDataByTopicKey(topicData, topicKey)
    return topicData[topicKey + 1]
end

local function RegisterGWMsg(msg, func)
    gw_ed.mgr:Register(msg, func)
end

local function RegisterEvent(msg, func)
    event.Register(msg, func)
end

local function ShowModule(moduleName, data, onShow, onHide)
    return ui_window_mgr:ShowModule(moduleName, onShow, onHide, data)
end

local function UnloadModule(moduleName, data, bDealHide)
    ui_window_mgr:UnloadModule(moduleName, bDealHide, data)
end
--endregion

--region 生命周期

function M.Init()
    M.RegisterMsgs()
    require "sandbox_gather_mgr"

end


--自动注册服务器的消息 gw_ed.GW_SAND_NET_EVENT
function M.OnSandNetEvent(msgName, funcName, ...)
    if funcName and M[funcName] then
        M[funcName](...)
    end
end



--监听事件打开各种UI
function M.RegisterMsgs()
    LogWarning("RegisterMsgs")

    RegisterEvent(event.SERVER_CROSS_DAY, M.OnServerCrossDay)

    RegisterGWMsg(gw_ed.GW_SAND_NET_EVENT, M.OnSandNetEvent)

    RegisterGWMsg(gw_ed.GW_SAND_CLICK_SCENE_EVENT, M.GW_SAND_CLICK_SCENE_EVENT)
    RegisterGWMsg(gw_ed.GW_SAND_SHOW_MODULE, M.GW_SAND_SHOW_MODULE)
    RegisterGWMsg(gw_ed.GW_SAND_VIEW_CENTER_CHANGE, M.GW_SAND_VIEW_CENTER_CHANGE)

    RegisterEvent(sand_ui_event_define.GW_CLICK_EMPTY, M.OnGW_CLICK_EMPTY)
    RegisterEvent(sand_ui_event_define.GW_CLOSE_SAND_UI, M.CloseSandBoxUI)

    RegisterEvent(event.LANGUAGE_SETTING_CHANGED, M.OnLanguageSettingChanged)

    RegisterEvent(event_alliance_define.UPDATE_ALLIANCE_MARK, M.OnSandBoxUnionMarkNtf)
    RegisterEvent(event_alliance_define.EXIT_ALLIANCE, M.ClearAllianceMarkInfoList)

    -- 移动输入事件
    RegisterEvent(gw_sand_event_define.GW_SAND_INPUT_MOVE, M.CloseSandBoxUIAndOpenMain)
end

function M.GW_SAND_VIEW_CENTER_CHANGE()
    event.Trigger(sand_ui_event_define.REFRESH_ENTITY_TIP)
end

-- 层级缩放界面控制,目前控制小地图和主界面切换
function M.GW_SAND_VIEW_LEVEL_CHANGE(level)
    if level <= gw_const.SandMinimapLevel then
        --event.Trigger(sand_ui_event_define.GW_CLOSE_SAND_MAIN)
        event.Trigger(sand_ui_event_define.GW_CLOSE_SAND_UI)
        if not ui_window_mgr:IsModuleShown("ui_sand_minimap") then
            ShowModule("ui_sand_minimap")
        end
    elseif level > gw_const.SandMinimapLevel then
        --event.Trigger(sand_ui_event_define.GW_OPEN_SAND_MAIN)
        if ui_window_mgr:IsModuleShown("ui_sand_minimap") then
            local main_slg_mgr = require "main_slg_mgr"
            main_slg_mgr.SetUIMainSlgShowType(1)
            UnloadModule("ui_sand_minimap")
            main_slg_mgr.SetUIMainSlgShowType(0)
        end
    end
end

function M.GW_SAND_SHOW_MODULE(_, event_name, msg)
    if PopWindowTable[event_name] then
        if event_name == "ShowPlayerInfo" then
            if msg then
                local mgr_personalInfo = require "mgr_personalInfo"
                mgr_personalInfo.ShowRoleInfoView(msg:GetRoleId())
            end
        elseif event_name == "ShowHeroSelectPanel" then
            local data = {}
            data.index = msg
            data.showMainPanel = true
            ShowModule(PopWindowTable[event_name], data)
            -- 取消相机的跟随
            gw_march_common_util.SetCameraFollowMarchBySid(0)
        else
            local data = {}
            if msg then
                data.index = msg
            end
            ShowModule(PopWindowTable[event_name], data)
        end
    end
end

--服务器跨天处理
function M.OnServerCrossDay()
    --刷新体力次数
    sand_ui_data.ResetCrossDay()
end
--endregion

--region 操作界面及弹窗
--点击沙盘空白处需要关闭的界面
local EmptyClickCloseList = {
    "ui_sand_operate",
    "ui_object_overlap_list_show",
    "ui_sand_attack",
    "ui_sand_reinforce",

    -- 沙漠风暴

}

--通知集结主界面按钮数据
function M.OnSandBoxMassMainIconNtf(data)
    local sand_gather_data = require "sand_gather_data"
    sand_gather_data.SetMainMassListInfo(data)
end

function M.OnSandMoveBack(msg)
    event.Trigger(sand_ui_event_define.GW_CLOSE_SAND_UI)
    --event.Trigger(sand_ui_event_define.GW_OPEN_SAND_MAIN)
end

function M.OnReEnterSandBox()
    local gw_camera_const = require "gw_camera_const"
    M.GW_SAND_VIEW_LEVEL_CHANGE(gw_camera_const.SDefaultViewLevel)

    event.Trigger(sand_ui_event_define.GW_CLOSE_SAND_UI, nil, true)
    event.Trigger(sand_ui_event_define.GW_OPEN_SAND_MAIN)
end

function M.CloseSandBoxUI(_, excludeName, closeAll)
    --关闭界面,点击打开的tips
    for _, viewName in pairs(tipsTable) do
        if viewName ~= excludeName then
            ui_window_mgr:UnloadModule(viewName)
        end
    end

    --额外其他界面
    for _, viewName in ipairs(EmptyClickCloseList) do
        if viewName ~= excludeName then
            ui_window_mgr:UnloadModule(viewName)
        end
    end

    --pop出来的界面
    if closeAll then
        for _, viewName in pairs(PopWindowTable) do
            if viewName ~= excludeName then
                ui_window_mgr:UnloadModule(viewName)
            end
        end
    end
end
--endregion

--region 体力系统
--更新体力数据
function M.UpdateStaminaDataValueByKey(key, value)
    local stamina = key == topic_pb.ETopicKey_RoleStamina_Vlaue and value
    local gettime = key == topic_pb.ETopicKey_RoleStamine_GetTime and value
    local getcount = key == topic_pb.ETopicKey_RoleStamine_GetCount and value
    local buycount = key == topic_pb.ETopicKey_RoleStamine_BuyCount and value
    local begintime = key == topic_pb.ETopicKey_RoleStamine_RecoverBeginTime and value

    local updateData = {
        stamina = stamina,
        gettime = gettime,
        getcount = getcount,
        buycount = buycount,
        begintime = begintime
    }
    Log("更新体力数据", "value", Edump(updateData))
    sand_ui_data.SetTaskData(updateData)
end
--每秒派发体力基础恢复
local lastRecoveryValue = 0
util.IntervalCall(1, function()
    if lastRecoveryValue ~= sand_ui_data.GetStaminaBaseRecovery() then
        lastRecoveryValue = sand_ui_data.GetStaminaBaseRecovery()
        event.Trigger(sand_ui_event_define.GW_STAMINA_BASE_RECOVER_CHANGE)
        event.DelayTrigger(sand_ui_event_define.GW_STAMINA_CHANGE)
    end
end)

--登录下发沙盘体力数据
function M.UpdateStaminaData(topicData)

    local stamina = GetDataByTopicKey(topicData, topic_pb.ETopicKey_RoleStamina_Vlaue)
    local gettime = GetDataByTopicKey(topicData, topic_pb.ETopicKey_RoleStamine_GetTime)
    local getcount = GetDataByTopicKey(topicData, topic_pb.ETopicKey_RoleStamine_GetCount)
    local buycount = GetDataByTopicKey(topicData, topic_pb.ETopicKey_RoleStamine_BuyCount)
    local begintime = GetDataByTopicKey(topicData, topic_pb.ETopicKey_RoleStamine_RecoverBeginTime)
    local updateData = {
        stamina = stamina,
        gettime = gettime,
        getcount = getcount,
        buycount = buycount,
        begintime = begintime
    }
    Log("登录下发体力数据", Edump(updateData))
    sand_ui_data.SetTaskData(updateData)
end

--免费获得体力请求
function M.StaminaOperate_FreeGet()
    local sandbox_pb = require "sandbox_pb"
    local msg = sandbox_pb.TMSG_STAMINA_OPER_REQ()

    msg.type = sandbox_pb.enStaminaOperType_FreeGet
    msg.operParam = 0
    net_sandbox_module.MSG_STAMINA_OPER_REQ(msg)
end

--购买体力请求
function M.StaminaOperate_Buy()
    local sandbox_pb = require "sandbox_pb"
    local msg = sandbox_pb.TMSG_STAMINA_OPER_REQ()

    msg.type = sandbox_pb.enStaminaOperType_BuyRecovery
    msg.operParam = 0
    net_sandbox_module.MSG_STAMINA_OPER_REQ(msg)
end

--使用道具体力请求
function M.StaminaOperate_UseItem(id)
    local sandbox_pb = require "sandbox_pb"
    local msg = sandbox_pb.TMSG_STAMINA_OPER_REQ()

    msg.type = sandbox_pb.enStaminaOperType_UsrItem
    msg.operParam = id
    net_sandbox_module.MSG_STAMINA_OPER_REQ(msg)
end

--体力操作
function M.OnStaminaOperate(msg)
    if msg.type == sandbox_pb.enStaminaOperType_FreeGet then
        local powerRecoveryNum = game_scheme:GWMapConstant_0(9).szParam.data[0]
        flow_text.Add(string.format2(lang.Get(560360), powerRecoveryNum))     --您获得了{%s1}点体力
    elseif msg.type == sandbox_pb.enStaminaOperType_BuyRecovery or msg.type == sandbox_pb.enStaminaOperType_UsrItem then
        local powerNum = msg.curStamina - sand_ui_data.GetStamina()
        flow_text.Add(string.format2(lang.Get(560360), powerNum))     --您获得了{%s1}点体力
    end

    local stamina = msg.curStamina
    local begintime = msg.recoveryBTime
    local updateData = {
        stamina = stamina,
        begintime = begintime
    }
    sand_ui_data.SetTaskData(updateData)
end

--endregion

--region 迁城

--@description 判断并显示有迁城道具缺少的界面提示
function M.IsShowRelocationGoodLack()
    if sand_ui_data.GetRelocationGoodNumById() == 0 then
        --没有迁城道具
        local data = {
            list = {
                {
                    goodsId = sand_ui_data.GetRelocationGoodID(),
                    needNum = 1
                }
            }
        }
        event.Trigger(evt_sourceSupply_define.Evt_ShowSupplyPanel, data)
        return true
    end
    return false
end
--点击沙盘（打开迁城界面）
function M.GW_SAND_CLICK_SCENE_EVENT(_, event_name, data1, data2, data3, data4)
    if PopWindowTable[event_name] then
        --这里需要先判断迁城类型，类型
        local gw_admin = require "gw_admin"
        local fit = gw_admin.SandRelocationEntityUtil.GetReadyRelocationEntityState()
        --打开迁城界面 
        if fit then
            --有迁城道具
            local data = {
                canMove = data1,
                gridPos = data2,
                moveEntitySid = data3
            }
            ShowModule(PopWindowTable[event_name], data)
            --关闭主界面
            --event.Trigger(sand_ui_event_define.GW_CLOSE_SAND_MAIN)
        end
    elseif event_name == "ShowClickList" then
        local listObj = data1
        local datalist = {}
        local specialList = {}
        local hasSpecial = false
        local entityGather = require "gw_sand_entity_gather"
        for _, obj in ipairs(listObj) do
            local data = {}
            local entity = obj.serData
            --local viewLevelType = sand_ui_data.GetSandViewLevelType(entity)
            local entityType = entityGather.GetEntityGather(entity)
            if entityType then
                data.objectName = entityType.GetTipDes(entity)
                local resCfg = entity.resProps and entity.resProps.resCfg
                if resCfg then
                    data.objectIcon = resCfg.minIcon1
                    data.onBtnClicked = obj.onClick
                    if resCfg.overlapping == 1 then
                        hasSpecial = true
                        table.insert(specialList, data)
                    else
                        table.insert(datalist, data)
                    end
                end
            end
        end
        local curList = hasSpecial and specialList or datalist
        if #curList > 1 then
            ShowModule("ui_object_overlap_list_show", curList)
        elseif #curList == 1 then
            curList[1].onBtnClicked()
        end
    end
end

function M.OnSandMoveCity(msg)
    if not sand_ui_data.GetIsSandBoxScene() then
        local gw_common_util = require "gw_common_util"
        gw_common_util.SwitchToSand(function()
            GWSandMgr.JumpToTargetGrid(msg.pos)
            --TODO 播放特效

        end)
    end
end

--endregion

--region 战利品

function M.OnUpdateLootCount(msg)
    local loot_data = sand_loot_data.GetLootData()
    loot_data.totalCount = msg.maxIndex
    Log("OnUpdateLootCount", msg.maxIndex)
    event.DelayTrigger(sand_ui_event_define.GW_LOOT_TOTAL_CHANGE)
end


--一键领取战利品请求
function M.SandGetLootReq()
    local count = sand_loot_data.GetRequestedLootCount()
    if count == 0 then
        count = sand_loot_data.GetLootData().totalCount
    end
    if count ~= 0 then
        net_sandbox_module.MSG_SANDBOX_GET_LOOT_REQ(count)
    else
        LogWarning("没有可以领取的战利品")
    end
end


--战利品一键领取回复
function M.OnSandGetLoot(msg)
    --显示奖励获得弹窗
    M.ShowGetSpoils(msg.getCount)
    --根据getcount领取数量清除列表,因为可能背包满了
    if msg.getCount ~= sand_loot_data.GetRequestedLootCount() then
        LogWarning("OnSandGetLoot 领取部分!!", msg.getCount, sand_loot_data.GetRequestedLootCount())
    else
        LogWarning("OnSandGetLoot", msg.getCount)
        --关闭战利品奖励界面
    end
    sand_loot_data.DelLootListByCount(msg.getCount, msg.maxIndex)
    event.DelayTrigger(sand_ui_event_define.GW_LOOT_TOTAL_CHANGE)

end

--领取战利品显示
function M.ShowGetSpoils(getCount)
    local SandLootData = require "sand_loot_data"
    local item_data = require "item_data"
    local reward_mgr = require("reward_mgr")
    local list = SandLootData.GetLootData().lootList
    local rewardList = {}
    if list then
        for i = 1, getCount do
            local item = list[i]
            local curRewardList = {}
            --合并奖励列表
            if item and item.targetType ~= gw_const.ESEntityType.Resource and
                    not (item.targetType == gw_const.ESEntityType.Base) then
                --非资源类型的战利品才显示奖励弹窗
                local rewardsNum = #item.rewards
                for i = 1, rewardsNum do
                    local RewardID = item.rewards[i].RewardID
                    curRewardList = reward_mgr.GetRewardGoodsList(RewardID, curRewardList)
                end
                local resourcRWNum = #item.resourcRW
                if resourcRWNum >= 0 then
                    for i = 1, resourcRWNum do
                        local good = {
                            id = item.resourcRW[i].ResourceType,
                            num = item.resourcRW[i].ResourceCount,
                            nType = item_data.Reward_Type_Enum.Item
                        }
                        table.insert(rewardList, good)
                    end
                end
                M.GetSpoilHeroAddition(item, curRewardList)
                rewardList = reward_mgr.MergeRewardList(rewardList, curRewardList)
            end
        end
    end

    if rewardList and #rewardList > 0 then
        reward_mgr.ShowReward(rewardList, nil, nil, true)
        --[[local listData = { title = "", dataList = rewardList, useItemAnim = true }
        local showData = {}
        table.insert(showData, listData)
        local ui_reward_result = require "ui_reward_result_new"
        ui_reward_result.SetInputParam(showData)
        ui_window_mgr:ShowModule("ui_reward_result_new")]]
    end
end

--计算战利品英雄加成
function M.GetSpoilHeroAddition(data, goodsList)
    --新增英雄加成
    local cfg = game_scheme:HeroResourceIncrease(1)
    local heroSkillID = cfg.heroSkillID
    local heroSkillLevel = data.HeroSkillLevel
    local heroStar = data.HeroStar
    if data.targetType == gw_const.ESEntityType.FixedMonster or data.targetType == gw_const.ESEntityType.WonderMonster then
        --怪物类型显示
        if not heroStar or heroStar == -1 then
            LogWarning("heroStar错误", heroStar)
            return
        end
        if not heroSkillLevel or heroSkillLevel == -1 then
            LogWarning("heroSkillLevel错误", heroSkillLevel)
            return
        end

        local monsterData = game_scheme:SandMapMonster_0(data.targetId)
        if not monsterData then
            LogWarning("SandMapMonster配置monsterData错误", data.targetId)
            return
        end

        local monsterType = monsterData.type --怪物类型
        local additionCfg = game_scheme:HeroResourceIncrease_0(heroSkillID, math.floor(heroStar / 5))
        if not additionCfg then
            LogWarning("HeroResourceIncrease配置additionCfg错误", heroSkillID, math.floor(heroStar / 5))
            return
        end

        local skillBase = 0
        local skillIncrease = 0
        local rewardAdditionList = {}
        local monsterTypeList = additionCfg.monsterType
        for i = 0, monsterTypeList.count - 1 do
            if monsterTypeList.data[i] == monsterType then
                rewardAdditionList = additionCfg.itemID
                skillIncrease = additionCfg.skillIncrease
                skillBase = additionCfg.skillBase
                for k = 0, rewardAdditionList.count - 1 do
                    for key, good in ipairs(goodsList) do
                        local id = good.goodId or good.id
                        if id == rewardAdditionList.data[k] then
                            local count = good.RewardCount or good.num
                            count = count * (1 + (skillBase + skillIncrease * heroSkillLevel) / 10000)
                            goodsList[key].RewardCount = count
                            goodsList[key].num = count
                            break
                        end
                    end
                end
                break
            end
        end
    end
    return goodsList
end

--初始需要请求的个数,to一定是最新的
function M.RequestNewLootData(showCount)
    local loot_data = sand_loot_data.GetLootData()
    showCount = showCount < loot_data.totalCount and showCount or loot_data.totalCount
    --请求当前最后一个战利品
    local to = loot_data.totalCount
    M.RequestOldLootData(showCount, to)
end

--滑动以后需要再请求的个数,to不变
function M.RequestOldLootData(count, to)
    local loot_data = sand_loot_data.GetLootData()
    --从后往前判断有没有数据是没有的,从前往后判断有没有数据是没有的,最终请求该区间内的数据
    local emptyTo = to or loot_data.to
    local emptyFrom = math.max(emptyTo - count + 1, 1)
    while emptyTo >= emptyFrom do
        if loot_data.lootList[emptyTo] ~= nil then
            emptyTo = emptyTo - 1
        end
        if loot_data.lootList[emptyFrom] ~= nil then
            emptyFrom = emptyFrom + 1
        end
        if loot_data.lootList[emptyTo] == nil and loot_data.lootList[emptyFrom] == nil and emptyTo >= emptyFrom then
            LogWarning("RequestLootData", emptyFrom, emptyTo)
            net_sandbox_module.MSG_SANDBOX_LOOT_INFO_REQ(emptyFrom, emptyTo)
            break
        end
    end

end
--请求战利品信息回复
function M.OnSandLootInfo(msg)
    --根据fromIndex跟toIndex,设置lootList
    sand_loot_data.SetLootData(msg.fromIndex, msg.toIndex, msg.maxIndex, msg.lootList)
end
--endregion

--region 主界面

--一键上阵，参数为当前需要一键上阵的队列序号
--[[
        otmHero.numProp =
        {
            lv = hero.numProp.lv,
            starLv = hero.numProp.starLv
        }
        otmHero.rarityType = hero.rarityType
        otmHero.type = hero.type
        otmHero.heroID = hero.heroID
        otmHero.heroSid = hero.heroSid
        otmHero.hasFinish = hero.hasFinish
        otmHero.isTrial = isTrial and 1 or 0
        otmHero.type = heroCfg.type
        otmHero.teamIndex = nil
        otmHero.hasTeamIndex = false
        table.insert(validHero, otmHero )
]]
function M.QuickFightClicked(teamId)
    local sand_team_data = require "sand_team_data"
    local teamState = sand_team_data.GetTeamStateByIndex(teamId)
    if teamState == 0 then
        --服务器推送无效状态时，客户端不做拦截
        teamState = common_pb.enSandboxTeamState_Idle
    end
    if teamState ~= 0 then
        --状态值有效
        if teamState ~= common_pb.enSandboxTeamState_Idle then
            flow_text.Add(lang.Get(601396))
            return
        end
    end

    local msg = {};
    --msg.teamIndex = curEnemyIndex;
    msg.lineUp = {};
    local tempLine = {};
    local HeroData = sand_team_data.GetUnselectHerodata() --获取所有未上阵英雄
    local selectHero = {}
    local lines = sand_team_data.GetAllTeamEntityList(false)
    local selectedHero = {}
    local _power = 0
    if lines and lines[teamId] then
        tempLine.order = lines[teamId].order or teamId
        if lines[teamId].usedWeapon then
            tempLine.weaponId = lines[teamId].usedWeapon.weaponId
        else
            tempLine.weaponId = 0
        end
        local log = require "log"
        for i = 1, 5 do
            if not lines[teamId].team.pals[i] then
                for j, k in pairs(HeroData) do
                    if k.teamIndex == nil then
                        local heroCfg = game_scheme:Hero_0(k.heroID)
                        if not selectHero[heroCfg.HerohandBookID] then
                            selectedHero[i] = k
                            table.remove(HeroData, j)
                            selectHero[heroCfg.HerohandBookID] = true
                            break ;
                        end
                    end
                end
            else
                selectedHero[i] = {
                    battleProp = {
                        power = lines[teamId].team.pals[i].heroPower
                    },
                    heroSid = lines[teamId].team.pals[i].heroSid
                }
            end
        end
    end

    local homeland_mgr = require "homeland_mgr"

    --window:UpdateGrindState(_power)
    local weaponPower = 0

    weaponPower = lines[teamId].usedWeapon and homeland_mgr.GetWeaponPowerByID(lines[teamId].usedWeapon["weaponId"]) or 0

    _power = _power + weaponPower

    tempLine.palList = {}
    local battle_data = require "battle_data"
    for i = 1, 5 do
        if selectedHero[i] ~= nil then
            local row, col = battle_data.GetRowAndColByIndex(i)

            local temp = {
                palId = selectedHero[i].heroSid,
                row = row,
                col = col,
            }

            _power = _power + selectedHero[i].battleProp.power
            table.insert(tempLine.palList, temp);
        end
    end
    tempLine.ce = _power;
    msg.lineUp = tempLine;
    msg.teamIndex = teamId
    local net_city_module = require "net_city_module"
    net_city_module.MSG_CITY_SAVE_TROOP_REQ(msg); --保存队伍请求

end

--endregion

--region 标记
local sand_mark_data = require "sand_mark_data"
local gw_sand_mark_mgr = require "gw_sand_mark_mgr"
--收到所有个人标记信息
function M.OnSandBoxMarkListNtf(msg)
    sand_mark_data.SetPersonalMarkInfoList(msg.infoList)

end

--收到联盟标记信息
function M.OnSandBoxUnionMarkNtf(_, msg)
    if msg.MarkInfo then
        sand_mark_data.SetAllianceMarkInfoList(msg)
        gw_sand_mark_mgr.UpdateUnionMark()
    end
    --[[if msg:HasField("MarkInfo") then
        
    end]]
end

--语言切换刷新标记文本
function M.OnLanguageSettingChanged()
    gw_sand_mark_mgr.UpdateUnionMark(nil,true)
end

--清空联盟标记
function M.ClearAllianceMarkInfoList()
    sand_mark_data.ClearAllianceMarkInfoList()
    gw_sand_mark_mgr.UpdateUnionMark()
end

--标记操作请求 
---@description operate number (添加0/删除1/修改2) info table (标记信息)
function M.SandBoxMarkOperateReq(operate, info)
    local function ReqMarkByType(data)
        if data.info.nType == 4 then
            --联盟标记类型
            local net_alliance_module = require "net_alliance_module"
            net_alliance_module.MSG_ALLIANCE_MARK_REQ(data)
        else
            net_sandbox_module.MSG_SANDBOX_MARK_REQ(data)
        end
    end
    local deleteInfo
    --标记数量已满，但是仍想添加个人标记，先删除最早添加的
    if info.nType ~= 4 and operate == 0 then
        deleteInfo = sand_mark_data.IsListMarkFull(info) or nil
        if deleteInfo then
            local deleteData = {}
            deleteData.operate = 1
            deleteData.info = deleteInfo
            ReqMarkByType(deleteData)
        end
    end

    --联盟标记
    if info.nType == 4 then
        if operate == 2 or operate == 0 then
            deleteInfo = sand_mark_data.FindMarkExistBySymbol(info.nSymbol) or nil
        end
        if deleteInfo then
            if info.nIndex ~= deleteInfo.nIndex then
                --修改图标
                if operate == 0 then
                    --添加(替换）
                    operate = 2
                    info.nIndex = deleteInfo.nIndex
                elseif operate == 2 then
                    if deleteInfo then
                        local deleteData = {}
                        deleteData.operate = 1
                        deleteData.info = deleteInfo
                        ReqMarkByType(deleteData)
                    end
                end
            end
        end
    end

    local data = {}
    data.operate = operate
    data.info = info
    ReqMarkByType(data)
end


--标记操作回复
function M.OnSandBoxMarkRsp(msg)
    -- 添加0/删除1/修改2
    if msg.info.nType == 4 and msg.info.nIndex == 1 then
        --修改同盟集结点
        flow_text.Add(lang.Get(560294))
    else
        if msg.operate == 1 then
            flow_text.Add(lang.Get(560216))
        elseif msg.operate == 0 or operate == 2 then
            flow_text.Add(lang.Get(560215))
        end

        if msg.info.nType then
            --更新个人标记(场景)
            GWSandMarkMgr.UpdateServerPersonalMark(msg)
        end
    end
    sand_mark_data.ChangeMarkListData(msg.operate, msg.info)
end
--endregion

--region 联盟信息

function M.OnSandBoxMemberRsp(msg)
    -- 本来准备加判空，但是想到如果退出联盟，也要清理这一块的data数据，就让他可以=nil吧
    sand_ui_data.SetMemberInfo(msg.info)
    event.Trigger(sand_ui_event_define.GW_MEMBER_SAND_CHANGE)
end

--endregion

--region 搜索
function M.SearchEntity(sid)
    local entity = sand_ui_data.GetEntityBySid(sid)
    if entity.type == gw_const.ESEntityType.FixedMonster then
        local targetID, searchID = sand_ui_data.GetSearchIndexEntity(entity.cfg.type, entity:GetMonsterFirstLevel() + 1)
        M.RequestSandSearchNew(searchID, targetID)
    end
end

function M.OnSandBoxFirstKillNtf(msg)
    sand_ui_data.SetFirstKillMonster(msg.monsterLevel)
end

local repCallBack = nil
function M.RequestSandSearchNew(searchID, targetID, _repCallBack)
    local msg = sandbox_pb.TMSG_SANDBOX_SEARCH_REQ()
    msg.entityType = 1
    msg.targetType = 1
    msg.targetLevel = 1
    msg.searchID = searchID
    msg.targetID = targetID
    net_sandbox_module.MSG_SANDBOX_SEARCH_REQ(msg)
    repCallBack = _repCallBack
end

function M.OnSandBoxSearchRsp(msg)
    if msg.pos then
        local main_slg_mgr = require "main_slg_mgr"
        main_slg_mgr.SetDelayMainTime("ui_sand_search", true, 0.2)
        ui_window_mgr:UnloadModule("ui_sand_search")

        event.Trigger(sand_ui_event_define.GW_CLOSE_SAND_UI)
        --event.Trigger(sand_ui_event_define.GW_OPEN_SAND_MAIN)
        M.CloseActivityUI()

        local sand_operate_command = require "sand_operate_command"
        sand_operate_command.OpenSearchArrowTipEvent(msg.pos)

        local gw_common_util = require "gw_common_util"
        gw_common_util.JumpToGrid(msg.pos, function()
            M.OpenSandboxEntityDetailView(msg.sid)
        end)
        main_slg_mgr.DelaySlgMainUI(false, 0)
    end
    if repCallBack then
        repCallBack(msg)
        repCallBack = nil
    end
end

--其他运营活动套用搜索怪物协议时,关闭对应界面
--目前已扩展 集结大作战
function M.CloseActivityUI()
    --集结大作战
    if ui_war_rally_mgr.CheckActivtyState() then
        ui_window_mgr:UnloadModule("ui_festival_activity_center")
    end
end

--请求游荡怪位置后跳转并打开详情
function M.SEARCH_MONSTER_DATA_Get_Func(eventName, msg)
    if msg.err == 0 and msg.sid then
        if msg.pos then
            GWSandMgr.JumpToTargetGrid(msg.pos)
        end
        net_sandbox_module.MSG_SANDBOX_GET_DETAIL_REQ(msg.sid)
        ui_window_mgr:UnloadModule("ui_sand_search")
    else
        --提示游荡怪已经死亡
        flow_text.Add(lang.Get(100000 + msg.err))
    end
end

--请求游荡怪位置回复
function M.OnSandBoxWonderPosRsp(msg)
    event.Trigger(sand_ui_event_define.GW_SAND_SEARCH_MONSTER_DATA_Get, msg)
end

--请求游荡怪位置回复
function M.OnSandBoxCarriagePosRsp(msg)
    event.Trigger(sand_ui_event_define.GW_SAND_SEARCH_CARRIAGE_DATA_Get, msg)
end

--请求指定sid联盟火车坐标返回
function M.OnSandBoxAllianceTrainPosRsp(msg)
    event.Trigger(sand_ui_event_define.GW_SAND_SEARCH_ALLIANCE_TRAIN_POS, msg)
end

--请求行军实体数据
function M.ReqMarchEntityData(data, callback)
    net_sandbox_module.MSG_SANDBOX_ALONE_MARCH_DATA_REQ(data, callback)
end

--endregion

--region侦查
function M.OnSandBoxDetectRsp(msg)
    event.Trigger(sand_ui_event_define.GW_CLOSE_SAND_UI)
    --event.Trigger(sand_ui_event_define.GW_OPEN_SAND_MAIN)
end
--endregion

--region 增援
--请求查看列表
function M.OnSandBoxReinforceListRsp(msg)
    sand_reinforce_data.UpdatePlayerData(msg)
    event.Trigger(sand_ui_event_define.GW_SAND_REINFORCE_DATA_CHANGE)
end

--请求撤回增援回复
function M.OnSandBoxReinForceBackRsp(msg)
    sand_reinforce_data.DeleteReinforceData(msg)
end
--endregion

--region  分享
function M.ShowShareMass(channel, sType, data)
    local net_chat_module_new = require "net_chat_module_new"
    net_chat_module_new.Send_CHAT_SPEAK(channel, sType, nil, nil, nil, nil, nil, nil, data)
end
--endregion

--region 城池击飞
function M.OnSandBoxDefFailNtf(msg)
    sand_ui_data.SetBaseDefFailData(msg)
    -- 城堡沦陷触发
    local gw_popups_trigger_mgr = require "gw_popups_trigger_mgr"
    local event_define = require "event_define"
    event.Trigger(event_define.EVENT_COMMON_POPUP_TRIGGER, gw_popups_trigger_mgr.TriggerType.CastleFall)
end
--endregion

--region todo yuannan 重构方向
--1. 需要记录打开的实体sid和实体数据的dataEntity
--2. 把部分逻辑关联在这边处理，例如相机锁定和解锁，跟随和不跟随
--3. 界面的数据独立控制，分析一下是否需要缓存

--region 模块懒加载
-- 模块缓存
local _moduleCache = {}

local function _requireModule(name)
    if not _moduleCache[name] then
        _moduleCache[name] = require(name)
    end
    return _moduleCache[name]
end

local function _getSandData()
    return _requireModule("gw_sand_data")
end

local function _getSandCameraMgr()
    return _requireModule("gw_sand_camera_mgr")
end
--endregion

local newUIData = true
local uiRuntime = {}

function M.GetNewUIDataSwitch()
    return newUIData
end

--相机跟随偏移
local SandMarchCameraOffsetY = {
    [sandbox_pb.enSandboxEntity_Carriage] = 2,
    [sandbox_pb.enSandboxEntity_AllianceTrain] = 2,
}

local SandEntityCameraOffsetY = {
    [gw_const.ESEntityResViewType.NeutralCity] = 3,
    [gw_const.ESEntityResViewType.FixedMonster] = 3,
    [gw_const.ESEntityResViewType.WorldBoss] = 3,
    [gw_const.ESEntityResViewType.AllianceBoss] = 1,
    [gw_const.ESEntityResViewType.BigGun] = 0,
}

function M.OnSetJumpEntityGridState(state)
    uiRuntime.jumpEntityGrid = state
end

--- 创建沙盘的实体数据
local function createSandboxEntityDetailData(msg)
    local entityDetailData = nil
    if newUIData then
        if gw_march_common_util.IsMarchType(msg.type) and (not msg.marchData or msg.marchData.lineSid == 0) then
            flow_text.Add(lang.Get(560368))
            return nil, true
        end

        if (msg.marchData and msg.marchData.lineSid > 0) then
            local gw_march_entity = _requireModule("gw_march_entity")
            entityDetailData = gw_march_entity:CreateInstance()
            entityDetailData:Create(msg.marchData)
            entityDetailData:UpdateDetailData(msg)
        elseif (msg.entityData and msg.entityData.sid > 0) then
            local gw_comp_entity = _requireModule("gw_comp_entity")
            entityDetailData = gw_comp_entity:CreateInstance()
            entityDetailData:Create(msg.entityData)
            entityDetailData:UpdateDetailData(msg)
        end
        return entityDetailData, true
    else
        -- 如果服务器异常没返回数据,则直接在沙盘实体里面找
        local entity = _requireModule("gw_map_util").GetServerDataBySid((msg.lineSid and msg.lineSid > 0) and msg.lineSid or msg.sid)
        if entity then
            entity:UpdateDetailData(msg)
        end
        return entity, false
    end
end

function M.InitSand()
    M.RegisterSandboxEvent()
end

function M.InitSandScene()
    event.Trigger(sand_ui_event_define.GW_OPEN_SAND_MAIN)
end

function M.Dispose()
    uiRuntime = {}
    M.UnRegisterSandboxEvent()
end

function M.RegisterSandboxEvent()
    -- 界面打开和关闭事件
    event.Register(event.UI_MODULE_INIT, M.OnUIModuleCreate)
    event.Register(event.UI_MODULE_SHOW, M.OnUIModuleShow)
    event.Register(event.UI_MODULE_CLOSE, M.OnUIModuleClose)
end

function M.UnRegisterSandboxEvent()
    event.Unregister(event.UI_MODULE_INIT, M.OnUIModuleCreate)
    event.Unregister(event.UI_MODULE_SHOW, M.OnUIModuleShow)
    event.Unregister(event.UI_MODULE_CLOSE, M.OnUIModuleClose)
end

--- 沙盘事件处理中心
function M.SandEventCenter(_, funcName, ...)
    local luaFunction = M[funcName]
    if luaFunction and type(luaFunction) == "function" then
        xpcall(luaFunction, Error, ...)
    end
end

-- 沙盘实体的详情界界面
function M.OpenSandboxEntityDetailView(sid, lineSid)
    if not sid then
        Error("OpenSandboxEntityTipsView func, sid is nil!!")
        return
    end

    local gw_sand_data = _getSandData()
    local sandboxSid = gw_sand_data.selfData.GetVisualSandBoxSid()
    if not sandboxSid then
        Error("OpenSandboxEntityTipsView func, not enter to the sandbox or sandbox data error!!")
        return
    end
    if sid ~= uiRuntime.entityDetailSid then
        net_sandbox_module.MSG_SANDBOX_GET_DETAIL_REQ(sid, lineSid)
    end
end

-- 酒馆任务怪物处理
local function HandleTavernTaskMonster(entityData, tipsWindName)
    local ui_tavern_mgr = _requireModule("ui_tavern_mgr")
    if entityData.props.tavernDoneTime > 0 then
        if entityData.props.tavernDoneTime < os.server_time() and entityData:GetIfSelf() then
            ui_tavern_mgr.MSG_ACORNPUB_BATCHGETREWARD_REQ(true, { entityData.props.tavernTaskSID })
        else
            local window = ShowModule(tipsWindName, entityData, function(uiModuleName)
                local window = ui_window_mgr:GetWindowObj(uiModuleName)
                if window and util.IsObjNull(window.UIRoot) then
                    local gw_common_util = require "gw_common_util"
                    local scale = gw_common_util.GetSandHudUICanvasScaleHeight()
                    window.UIRoot.transform.localScale = { x = scale, y = scale, z = 1 }
                end
            end)
            return window
        end
    elseif entityData.props.tavernDoneTime == 0 and entityData:GetIfSelf() then
        local window = ShowModule("ui_tavern_dispatch_tip", {
            cfgIndex = ui_tavern_mgr.GetCfgIDbySID(entityData.props.tavernTaskSID),
            taskSID = entityData.props.tavernTaskSID
        })
        return window
    end
end

-- 各实体类型处理器
local EntityProcessors = {
    -- 通用怪物
    [sandbox_pb.enSandboxEntity_CommonMonster] = function(msg, entityData, tipsWindName)
        if entityData:IsTavernTaskMonster() then
            return HandleTavernTaskMonster(entityData, tipsWindName)
        elseif entityData:IsWarRallyMonster() then
            tipsWindName = tipsTable[gw_const.ESEntityResViewType.FixedMonster]
        end
        local window = ShowModule(tipsWindName, entityData)
        return window
    end,
    -- 沙漠风暴
    [sandbox_pb.enSandboxEntity_DesertStromEntity] = function(msg, entityData, tipsWindName)
        local cfg = entityData.Cfg
        if cfg then
            local hudType = require("gw_storm_cfg").GetBuildBudType(cfg.BuildingTypes)
            local stormTypes = require("event_DesertStrom_define").STORM_BUILDING_HUD_TYPE
            tipsWindName = hudType == stormTypes.Oil
                    and tipsTable[gw_const.ESEntityResViewType.StormOil]
                    or tipsTable[gw_const.ESEntityResViewType.StormBox]
        end
        local window = ShowModule(tipsWindName, entityData)
        return window
    end,
    -- 默认处理器
    __default = function(msg, entityData, tipsWindName)
        local window = nil
        if entityData.type == sandbox_pb.enSandboxEntity_RadarTreasure or msg.type == sandbox_pb.enSandboxEntity_Carriage
                or msg.type == sandbox_pb.enSandboxEntity_AllianceTrain then
            window = ShowModule(tipsWindName, { entity = entityData, msg = msg })
        else
            window = ShowModule(tipsWindName, entityData)
        end
        return window
    end
}

local EntityHandlers = {
    -- 前置校验处理器
    PreCheckHandler = {
        check = function(msg, entityData)
            -- 雷达任务污染之地检查
            local gw_sand_external_mgr = _requireModule("gw_sand_external_mgr")
            if SandRadarEntityType[msg.type] and gw_sand_external_mgr.IsInPolluteZoneBuff1() then
                flow_text.Add(lang.Get(652116))
                return false
            end

            if not entityData then
                return false
            end

            local viewType = entityData:GetFinalKey2()
            local gw_sand_data = _getSandData()
            -- 联盟宝藏检查
            if viewType == gw_const.ESEntityResViewType.RadarTreasure
                    and not entityData:IsAllianceTreasure() then
                flow_text.Add(lang.Get(652087))
                return false
            elseif viewType == gw_const.ESEntityResViewType.AllianceBoss and gw_sand_data.selfData.IsVisualCrossServiceState() then
                return false
            end
            return true
        end
    },

    -- 通用界面处理器
    CommonUIHandler = {
        process = function(msg, entityData)
            -- 关闭其他界面
            local shouldClose = not entityData:IsTavernTaskMonster() or entityData.props.tavernDoneTime ~= 0
            if shouldClose then
                event.Trigger(sand_ui_event_define.GW_CLOSE_SAND_UI)
                event.Trigger(sand_ui_event_define.GW_CLOSE_SAND_MAIN_NO_TOP)
            end

            -- 获取提示窗口名
            local viewLevelType = entityData:GetFinalKey2()
            local tipsWindName = tipsTable[viewLevelType]
            if not tipsWindName then
                Error("tipsWindName is nil, viewLevelType", viewLevelType)
                return false
            end
            return true, tipsWindName
        end
    },

    -- 特殊实体处理器
    SpecialEntityHandler = {
        process = function(msg, entityData, tipsWindName)
            -- 行军单位相机跟随
            if msg.type == sandbox_pb.enSandboxEntity_WonderMonster
                    or msg.type == sandbox_pb.enSandboxEntity_Carriage
                    or msg.type == sandbox_pb.enSandboxEntity_AllianceTrain then
                gw_march_common_util.SetCameraFollowMarchBySid(
                        entityData.marchSid or entityData.sid,
                        SandMarchCameraOffsetY[msg.type]
                )
            end

            -- 各类型特殊处理
            local handler = EntityProcessors[entityData.type] or EntityProcessors.__default
            local window = handler and handler(msg, entityData, tipsWindName) or nil
            if window then
                window.delayOpenMain = window.delayOpenMain or 0.2
                window.delayCloseMain = window.delayCloseMain or 0
            end
            return true, tipsWindName
        end
    },

    -- 操作界面处理器
    OperateUIHandler = {
        process = function(msg, entityData)
            -- 显示操作界面
            local sand_operate_factory = _requireModule("sand_operate_factory")
            local operateList = sand_operate_factory.GetOperateList(entityData)
            if operateList and #operateList > 0 then
                ShowModule("ui_sand_operate", entityData)
            end
        end
    }
}

-- 主入口函数
function M.OnSandEntityDetail(msg)
    if msg.sid == uiRuntime.entityDetailSid then
        return
    end

    if uiRuntime.blurBgUITable and next(uiRuntime.blurBgUITable) then
        uiRuntime.entityDetailSid = nil
        return
    end

    -- 创建实体数据
    local entityData, notMapEntity = createSandboxEntityDetailData(msg)
    if not entityData or not entityData.cfg then
        LogWarning("OnSandEntityDetail not get entityData or not entityData cfg!!")
        return
    end

    if not EntityHandlers.PreCheckHandler.check(msg, entityData) then
        return
    end

    uiRuntime.entityDetailSid = entityData.sid
    if notMapEntity then
        uiRuntime.entityDetailData = entityData
    end
    local ok, tipsWindName = EntityHandlers.CommonUIHandler.process(msg, entityData)
    if not ok then
        return
    end

    local viewType = entityData:GetFinalKey2()
    if SandEntityCameraOffsetY[viewType] or uiRuntime.jumpEntityGrid then
        local offset = SandEntityCameraOffsetY[viewType] or 0
        local pos = entityData.MarchEntityInScene and _requireModule("gw_march_common_util").GetMarchMovePosition(entityData.dotList_txy) or
                { x = entityData.pos.x, y = entityData.pos.y + offset }
        _getSandCameraMgr().OnJumpToGrid(pos, function()
            ok, tipsWindName = EntityHandlers.SpecialEntityHandler.process(msg, entityData, tipsWindName)
            if not ok then
                return
            end
            EntityHandlers.OperateUIHandler.process(msg, entityData)
        end, 100)
        uiRuntime.jumpEntityGrid = false
    else
        ok, tipsWindName = EntityHandlers.SpecialEntityHandler.process(msg, entityData, tipsWindName)
        if not ok then
            return
        end
        EntityHandlers.OperateUIHandler.process(msg, entityData)
    end
end

function M.OnSandEntityRemove(removeSids)
    if uiRuntime.entityDetailSid and removeSids then
        for k, v in ipairs(removeSids) do
            if v == uiRuntime.entityDetailSid then
                M.CloseSandBoxUIAndOpenMain()
                return
            end
        end
    end
end

function M.CloseSandBoxUIAndOpenMain()
    _getSandCameraMgr().SetFollowStatus(false)

    local main_slg_mgr = require "main_slg_mgr"
    if main_slg_mgr.blockOpenMain then
        return
    end
    event.Trigger(sand_ui_event_define.GW_CLOSE_SAND_UI)
    -- local main_slg_data = require "main_slg_data"
    -- if main_slg_data.GetCloseState() ~= 1 and not ui_window_mgr:IsModuleShown(PopWindowTable["ShowBaseOperate"])
    --         and _getSandCameraMgr().GetViewLevel() > gw_const.SandMinimapLevel then
    --     main_slg_mgr.BlockOpenMain(function()
    --         event.Trigger(sand_ui_event_define.GW_OPEN_SAND_MAIN)
    --     end)
    -- end
    uiRuntime.entityDetailSid = nil
end
---@public 是否全屏
function M.IsFullUIBy(moduleName)
    return ui_window_mgr.IsFullUIBy(moduleName)
end

function M.OnUIModuleCreate(_, moduleName)
    local winObj = ui_window_mgr:GetWindowObj(moduleName)
    if winObj and (winObj.isBlurBg or winObj:GetCloseBtn() or M.IsFullUIBy(moduleName)) then
        uiRuntime.blurBgUITable = uiRuntime.blurBgUITable or {}
        uiRuntime.blurBgUITable[moduleName] = true

        _getSandCameraMgr().SetCanZoom(false)
        --Error("OnUIModuleCreate create [", moduleName, "] set camera SetCanZoom, state : false")
    end
end

local sandUIShowFilter = {
    ["ui_sand_search"] = true,
    ["ui_sand_attack"] = true,
    ["ui_package_gw"] = true,
    ["ui_first_recharge"] = true,
    ["ui_chat_main_new"] = true,
}

function M.OnUIModuleShow(_, moduleName)
    if sandUIShowFilter[moduleName] then
        uiRuntime.blurBgUITable = uiRuntime.blurBgUITable or {}
        uiRuntime.blurBgUITable[moduleName] = true

        _getSandCameraMgr().SetCanZoom(false)
        --Error("OnUIModuleShow show [", moduleName, "] set camera SetCanZoom, state : false")
    end
end

function M.OnUIModuleClose(_, moduleName)
    if uiRuntime.blurBgUITable and uiRuntime.blurBgUITable[moduleName] then
        uiRuntime.blurBgUITable[moduleName] = nil

        if next(uiRuntime.blurBgUITable) then
            return
        end
        _getSandCameraMgr().SetCanZoom(true)
        --Error("OnUIModuleClose create [", moduleName, "] set camera SetCanZoom, state : true")
    end
end

function M.OnGW_CLICK_EMPTY()
    M.CloseSandBoxUIAndOpenMain()
end

--region 出征
function M.OpenAttackDetailView(sandEntity, attackType)
    uiRuntime.attackDetailEntityData = sandEntity

    local sand_attack_const = require "sand_attack_const"
    net_sandbox_module.MSG_SANDBOX_EXPEDITION_DETAIL_REQ(sandEntity.sid, attackType, nil, nil, sand_attack_const.AttackActionType.attack)
end

function M.CloseAttackDetailView()
    uiRuntime.attackDetailEntityData = nil
end

--攻打返回时间
function M.OnSandExpeditionDetail(msg)
    if uiRuntime.attackDetailEntityData and msg.sid ~= uiRuntime.attackDetailEntityData.sid then
        return
    end

    --打开出征界面
    local sandbox_gather_mgr = require "sandbox_gather_mgr"
    if msg.attackType and sandbox_gather_mgr.IsGatherAttackType(msg.attackType) then
        --if msg.attackType then
        if not ui_window_mgr:IsModuleExist("ui_sand_attack") then
            event.Trigger(sand_ui_event_define.GW_CLOSE_SAND_UI)
        end
        if uiRuntime.blurBgUITable and next(uiRuntime.blurBgUITable) then
            uiRuntime.attackDetailEntityData = nil
            return
        end
        event.Trigger(sand_ui_event_define.GW_SAND_GROUP_ATTACK_DETAIL, msg)
    else
        if ui_window_mgr:IsModuleExist("ui_sand_attack") then
            event.Trigger(sand_ui_event_define.GW_TEAM_MOVE_TIME_CHANGE, msg)
        else
            event.Trigger(sand_ui_event_define.GW_CLOSE_SAND_UI)

            if uiRuntime.blurBgUITable and next(uiRuntime.blurBgUITable) then
                uiRuntime.attackDetailEntityData = nil
                return
            end

            local sand_attack_mgr = require "sand_attack_mgr"
            local entity = uiRuntime.attackDetailEntityData
            if not entity then
                return
            end
            local data = {
                sid = msg.sid,
                duration = msg.duration,
                notSDayReward = msg.notSDayReward,
                attackType = msg.attackType,
                resourceAsTarget = msg.resourceAsTarget,
                entity = sand_attack_mgr.GetEntityData(entity)
            }
            if data.entity then
                ShowModule("ui_sand_attack", data)
            end
        end
    end
end
--endregion

function M.OnSandEntityNtf(msg)
    --缩放等级小于3不提示跨区提示
    local sandCameraMgr = _requireModule("gw_sand_camera_mgr")
    local level = sandCameraMgr.GetViewLevel() or 0
    if level < gw_const.SandCrossAreaTipsLevel then
        return
    end
    if msg.neutralCityID and msg.neutralCityID > 0 then
        event.Trigger(sand_ui_event_define.GW_SAND_AREA_DATA_CHANGE, msg)
        if not ui_window_mgr:IsModuleShown("ui_sand_move_server_tip") then
            ShowModule("ui_sand_move_server_tip", msg)
        end
    end
end

--endregion

M.Init()

return M