local require = require
local typeof = typeof

local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text
local Slider = CS.UnityEngine.UI.Slider
local SpriteSwitcher = CS.War.UI.SpriteSwitcher
local Animator = CS.UnityEngine.Animator
local GameObject = CS.UnityEngine.GameObject
local ScrollRect = CS.UnityEngine.UI.ScrollRect
local RectTransform = CS.UnityEngine.RectTransform
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable


module("ui_land_revival_day_task_binding")

UIPath = "ui/prefabs/gw/gw_landrevival/uilandrevivaldaytask.prefab"

WidgetTable ={
	btn_help = { path = "topArea/btn_help", type = Button, event_name = "OnBtnHelpClickedProxy"},
	txt_time = { path = "topArea/bgTime/txt_time", type = Text, },
	sld_leftSlider = { path = "topArea/activityProgess/sld_leftSlider", type = Slider, value_changed_event = "OnSliderLeftSliderValueChange"},
	txt_sldNum = { path = "topArea/activityProgess/sld_leftSlider/txt_sldNum", type = Text, },
	ss_BoxIcon = { path = "topArea/activityProgess/ss&btn&ator_BoxIcon", type = SpriteSwitcher, },
	btn_BoxIcon = { path = "topArea/activityProgess/ss&btn&ator_BoxIcon", type = Button, event_name = "OnBtnBoxIconClickedProxy"},
	ator_BoxIcon = { path = "topArea/activityProgess/ss&btn&ator_BoxIcon", type = Animator, },
	item_boxBubble = { path = "topArea/activityProgess/ss&btn&ator_BoxIcon/item_boxBubble", type = GameObject, },
	item_dayBtn = { path = "item_dayBtn", type = GameObject, },
	sr_dayScrollList = { path = "taskList/sr_dayScrollList", type = ScrollRect, },
	rtf_dayBtnParent = { path = "taskList/sr_dayScrollList/Viewport/rtf_dayBtnParent", type = RectTransform, },
	item_upHammerToggle1 = { path = "taskList/ToggleGroup/item_upHammerToggle1", type = GameObject, },
	item_upHammerToggle2 = { path = "taskList/ToggleGroup/item_upHammerToggle2", type = GameObject, },
	item_upHammerToggle3 = { path = "taskList/ToggleGroup/item_upHammerToggle3", type = GameObject, },
	srt_taskContent = { path = "taskList/Viewport/srt_taskContent", type = ScrollRectTable, },

}
