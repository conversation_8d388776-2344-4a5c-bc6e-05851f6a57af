local require = require
local util = require "util"
local table = table
local typeof = typeof
local pairs = pairs
local ipairs = ipairs
local Time = CS.UnityEngine.Time
local PingCheck = CS.War.Script.PingCheck
local GameObject = CS.UnityEngine.GameObject
local AssetBundleManager = CS.War.Base.AssetBundleManager
local SplitServerHashChecker = CS.War.Res.SplitServerHashChecker
local SplitServerSumChecker = CS.War.Res.SplitServerSumChecker
local SplitServerFinishFileRecorder = CS.War.Res.SplitServerFinishFileRecorder
local split_define = require "split_server_res_define"
local event = require"event"
local Warning = split_define.logger.Warning
local DownloadErrorCode = split_define.DownloadErrorCode

module("split_server_res_download_mgr")

local state = 
{
    none = 1,
    downloading = 2,
    finish = 3,
}
local cache = {}

function Init()
    if cache.isInit then
        return
    end
    
    cache.isInit = true
    cache.processingCount = 0
    cache.failTimes = 0
    cache.tickTime = 0
    cache.tickInterval = 0
    cache.lastCheckPingTime = 0
    cache.curState = state.none
    cache.downloadingPathsForDnsSwitch = {}
    
    SplitServerHashChecker.IsEnableSaveTemp = split_define.isEnableSaveTemp
    event.Register(event.CSUpdateEx, Tick)
end

function AddVersionTaskToList(version, serverId, verTaskType)
    local split_server_res_ver_mgr = require "split_server_res_ver_mgr"
    if not split_server_res_ver_mgr.IsCanUseSplitServerRes() then
        Warning(0, "Add task fail, must Use OldResVersion and Enable SplitServerRes")
        return
    end
    Init()
    
    cache.versionTaskList = cache.versionTaskList or {}
    local versionTask = cache.versionTaskList[version]
    if versionTask then
        Warning(0, "version task is exist")
        versionTask:AddServerId(serverId)
        event.Trigger(split_define.TaskListChangeEvent)
        return
    end
    
    local taskHandler = split_define.GetResVerTaskClass(verTaskType)
    versionTask = taskHandler.New(version, serverId)
    cache.versionTaskList[version] = versionTask
    cache.versionQueue = cache.versionQueue or {}
    table.insert(cache.versionQueue, version)

    if cache.curState ~= state.downloading then
        StartDownload()
    end
    event.Trigger(split_define.TaskListChangeEvent)
end

function StartDownload()
    if not cache.versionQueue or #cache.versionQueue == 0 then
        return
    end
    local version = cache.versionQueue[1]
    local versionTask = cache.versionTaskList[version]
    cache.curVersionTask = versionTask
    cache.curVersionTask:StartTask()
    cache.curState = state.downloading
    cache.tickInterval = 0
end

function SwitchDownloadVersion(version)
    if cache.curVersionTask then
        cache.curVersionTask:PauseTask()
    end

    local index = 0
    for i = 1, #cache.versionQueue do
        if cache.versionQueue[i] == version then
            index = i
            break
        end
    end
    if index > 0 then
        table.remove(cache.versionQueue, index)
        table.insert(cache.versionQueue, 1, version)
    else
        Warning(0, "version is not exist in versionQueue")
        return
    end
    StartDownload()
end

function PauseDownload()
    if cache.curVersionTask then
        cache.curVersionTask:PauseTask()
    end
end

function ResumeDownload()
    if cache.curVersionTask then
        cache.curVersionTask:ResumeTask()
    end
end

function CheckToNextVersionTask()
    local versionCount = #cache.versionQueue
    if versionCount > 0 then
        local version = table.remove(cache.versionQueue, 1)
        cache.versionTaskList[version] = nil
        
        versionCount = versionCount - 1
        if versionCount > 0 then
            StartDownload()
        else
            cache.curState = state.finish
            Dispose()
        end
    end
end

function GetProcessingCount()
    return cache.processingCount
end

function AddProcessingCount()
    cache.processingCount = cache.processingCount + 1
end

function RedProcessingCount()
    cache.processingCount = cache.processingCount - 1
end

function IsSwitchDns()
   return cache.isSwitchDns
end

function GetProcessingTask(savePath)
    return cache.processingTasks and cache.processingTasks[savePath]
end

function RemoveProcessingTask(savePath)
    cache.processingTasks[savePath] = nil
    RedProcessingCount()
end

function RequestTask(task, hashRemote)
    cache.processingTasks = cache.processingTasks or {}
    if cache.processingTasks[task.savePath] then
        Warning(1, "task is in processing", task.savePath)
        return
    end
    
    if split_define.isEnableDnsSwitch then
        cache.downloadingPathsForDnsSwitch[task.savePath] = true 
    end
    local split_server_res_download_way_chooser = require"split_server_res_download_way_chooser"
    split_server_res_download_way_chooser.DownloadFile(task, hashRemote)

    AddProcessingCount()
    cache.processingTasks[task.savePath] = task
end

function SetTickInterval(tickInterval)
    cache.tickInterval = tickInterval
end

function Tick(_, deltaTime)
    if not cache.isInit then
        return
    end
    
    cache.tickTime = cache.tickTime + deltaTime or Time.deltaTime
    if cache.tickTime < cache.tickInterval then
        return
    end
    cache.tickTime = 0

    if cache.curVersionTask then
        cache.curVersionTask:Tick(deltaTime) 
    end
    CheckDownloadingTaskTimeout()
    CheckFileRenameTick()
end

function CheckDownloadingTaskTimeout()
    if not cache.processingTasks then return end
    for _, task in pairs(cache.processingTasks) do
        if task and task.timeoutCheckTime and Time.realtimeSinceStartup > task.timeoutCheckTime then
            task.timeoutCheckTime = nil
            local split_server_res_download_way_chooser = require"split_server_res_download_way_chooser"
            split_server_res_download_way_chooser.CancelDownloadOnTimeout(task)
        end
    end
end

function CheckFileSum(task)
    cache.processingTasks = cache.processingTasks or {}
    if cache.processingTasks[task.savePath] then
        Warning(1, "Task is in processing", task.savePath)
        return
    end

    AddProcessingCount()
    cache.processingTasks[task.savePath] = task

    if not task.isCheckSum then
        OnCheckSumResult(task.savePath, true)
        return
    end
    if split_define.checkSumIsAsync then
        SplitServerSumChecker.CheckFileSumAsync(task.taskFileInfo, function(result)
            OnCheckSumResult(task.savePath, result)
        end)
    else
        local result = SplitServerSumChecker.CheckFileSum(task.taskFileInfo)
        OnCheckSumResult(task.savePath, result)
    end
end

function CheckFileRenameTick()
    if not cache.fileRenamingTasks then return end
    local renameCount = 0
    for _, task in pairs(cache.fileRenamingTasks) do
        if task and (not task.renameRetryTime or Time.realtimeSinceStartup > task.renameRetryTime) then
            local isSuccess = SplitServerFinishFileRecorder.FileRename(task.taskFileInfo)
            if isSuccess then
                OnRenameResult(task.savePath, true)
            else
                task.renameRetryTime = Time.realtimeSinceStartup + split_define.fileRenameRetryTime
                task.renameFailCount = task.renameFailCount + 1
                if task.renameFailCount >= split_define.maxRenameRetryTime then 
                    OnRenameResult(task.savePath, false) 
                end
            end

            renameCount = renameCount + 1
            if renameCount >= split_define.maxRenamePreFrame then
                break
            end
        end
    end
end

function OnCheckSumResult(savePath, result)
    if result then
        OnProcessTaskFinish(savePath)
    else
        OnProcessTaskError(savePath, nil, DownloadErrorCode.CheckSumFail, "CheckSumFail")
    end
end

function OnRenameResult(savePath, result)
    local task = cache.fileRenamingTasks[savePath]
    if task then
        if result then
            task.isRenameSuccess = true
            OnProcessTaskFinish(savePath)
        else
            OnProcessTaskError(savePath, nil, DownloadErrorCode.RenameFail, "RenameFail")
        end
    end
    cache.fileRenamingTasks[savePath] = nil
end

function OnProcessTaskError(savePath, _, errorCode, errorDes)
    local task = cache.processingTasks[savePath]
    if task then
        Warning(2,"OnProcessTaskError", savePath, errorCode, errorDes)
        cache.processingTasks[savePath] = nil
        RedProcessingCount()
        for _, versionTask in pairs(cache.versionTaskList) do
            if versionTask then
                versionTask:OnTaskError(savePath, errorCode, errorDes) 
            end
        end

        if split_define.isEnableDnsSwitch and cache.downloadingPathsForDnsSwitch[savePath] then
            cache.failTimes = cache.failTimes + 1
            if cache.failTimes > split_define.nextDnsFailTimes then
                Warning(0,"OnProcessTaskError", task.abName, task.downloadUrl, errorCode, errorDes)
                cache.failTimes = 0
                if AssetBundleManager.NEXT_DNS then   
                    AssetBundleManager.NEXT_DNS()
                    cache.isSwitchDns = true
                end
                cache.downloadingPathsForDnsSwitch = {}
            else
                Warning(2,"OnProcessTaskError", task.abName, task.downloadUrl, errorCode, errorDes)
                cache.downloadingPathsForDnsSwitch[savePath] = nil
            end
        end
    end
end

function OnProcessTaskFinish(savePath)
    local task = cache.processingTasks[savePath]
    if task then

        if split_define.isEnableSaveTemp and not task.isRenameSuccess then     --是否修改名字
            Warning(2,"WaitingRename", savePath)
            task.renameFailCount = 0
            task.renameRetryTime = Time.realtimeSinceStartup
            cache.fileRenamingTasks = cache.fileRenamingTasks or {}
            cache.fileRenamingTasks[savePath] = task
            cache.downloadingPathsForDnsSwitch[savePath] = nil
        else
            Warning(2,"OnProcessTaskFinish", savePath)
            for _, versionTask in pairs(cache.versionTaskList) do
                if versionTask and not versionTask:IsFinish() then
                    versionTask:OnTaskFinish(savePath)
                end
            end
            cache.processingTasks[savePath] = nil
            RedProcessingCount()
            cache.downloadingPathsForDnsSwitch[savePath] = nil
        end
    end
end

function OnProcessTaskCancel(savePath)
    local taskList = cache.processingTasks[savePath]
    if taskList then
        Warning(2,"OnProcessTaskCancel", savePath)
        cache.processingTasks[savePath] = nil
        RedProcessingCount()
        cache.downloadingPathsForDnsSwitch[savePath] = nil
        
        for _, versionTask in pairs(cache.versionTaskList) do
            if versionTask then
                versionTask:OnTaskCancel(savePath)
            end
        end
    end
end

--超时检测网络, 网络不通延长tick间隔
function CheckPingNet()
    if cache.lastCheckPingTime and Time.realtimeSinceStartup < cache.lastCheckPingTime + 1 then
        return
    end
    cache.lastCheckPingTime = Time.realtimeSinceStartup
    
    cache.pingCheck = cache.pingCheck or GameObject.Find("/Engine"):GetComponent(typeof(PingCheck))
    if cache.pingCheck then
        local pingData = cache.pingCheck:GetPingData()
        if pingData.minPing > 0 and pingData.avgPing > 0 then
            SetTickInterval(0)
            return true
        end

        SetTickInterval(30)
        if not cache.pingCheckTimer then
            cache.pingCheckTimer = util.DelayCallOnce(1, function()
                cache.lastCheckPingTime = Time.realtimeSinceStartup
                pingData = cache.pingCheck:GetPingData()
                if pingData.minPing > 0 and pingData.avgPing > 0 then
                    SetTickInterval(0)
                    return -1
                else
                    SetTickInterval(30)
                    return 1
                end
            end)
        end
    end
    return false
end

--是否开始了SplitServerRes下载
function IsStartSplitServerResDownload()
    return cache.curState ~= nil
end

function IsShowStartPause()
    if cache.curVersionTask then 
        --后台下载才显示暂停按钮
       return cache.curVersionTask.verTaskType == split_define.VerTaskType.BgDownload
    end
    return false
end

function Report(eName, obj)
    if cache.curVersionTask then
        cache.curVersionTask:Report(eName, obj)
    end
end

function GetResVersionTasks()
    return cache.versionQueue, cache.versionTaskList
end

function GetDownloadParam()
    if cache.curVersionTask then
        return cache.curVersionTask:GetDownloadParam()
    end
    return 0, 0, false
end

function Dispose()
    event.Unregister(event.CSUpdateEx, Tick)
    
    if cache.pingCheckTimer then
        util.RemoveDelayCall(cache.pingCheckTimer)
        cache.pingCheckTimer = 0
    end
    
    cache.isInit = false
    cache.processingCount = 0
    cache.failTimes = 0
    cache.tickTime = 0
    cache.tickInterval = 0
    cache.lastCheckPingTime = 0
    cache.processingTasks = {}
    cache.downloadingPathsForDnsSwitch = {}
    cache.isSwitchDns = false
    cache.curVersionTask = nil
    cache.versionQueue = {}
    cache.curState = state.none

    for _, versionTask in pairs(cache.versionTaskList) do
        versionTask:Dispose()
    end
    cache.versionTaskList = {}

    util.DelayCallOnce(5, function()
        SplitServerFinishFileRecorder.StopWriter()  
    end)
    SplitServerHashChecker.ClearPool()

    local split_server_res_download_way_chooser = require"split_server_res_download_way_chooser"
    split_server_res_download_way_chooser.Dispose()
end 