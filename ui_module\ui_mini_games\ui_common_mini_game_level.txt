--@region FileHead
-- ui_common_mini_game_level.txt ---------------------------------
-- author:  何绿叶
-- date:    2/24/2021 11:56:51 AM
-- ver:     1.0
-- desc:    解密小游戏关卡页面
-------------------------------------------------
--@endregion 

--@region Require
local print     = print
local require   = require
local pairs     = pairs
local ipairs    = ipairs
local typeof    = typeof
local string    = string
local table     = table
local dump      = dump
local math      = math
local type = type
local next = next
local os = os
local Button        = CS.UnityEngine.UI.Button
local Text          = CS.UnityEngine.UI.Text
local Image         = CS.UnityEngine.UI.Image
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable
local RectTransform = CS.UnityEngine.RectTransform
local Canvas = CS.UnityEngine.Canvas
local PlayerPrefs = CS.UnityEngine.PlayerPrefs
local ScrollRectItem = CS.UI.UGUIExtend.ScrollRectItem
local Slider             = CS.UnityEngine.UI.Slider
local ScrollRect        = CS.UnityEngine.UI.ScrollRect
local unforced_guide_mgr = require "unforced_guide_mgr"
local ReviewingUtil 	= require "ReviewingUtil"
local SortingGroup  = CS.UnityEngine.Rendering.SortingGroup
local ParticleSystemRenderer = CS.UnityEngine.ParticleSystemRenderer
local LayoutRebuilder = CS.UnityEngine.UI.LayoutRebuilder
local HorizontalLayoutGroup = typeof(CS.UnityEngine.UI.HorizontalLayoutGroup)
local IsReviewing = ReviewingUtil.IsReviewing()
local ScrollRect    = CS.UnityEngine.UI.ScrollRect
local GameObject = CS.UnityEngine.GameObject
local UIUtil        = CS.Common_Util.UIUtil
local LeanTween         = CS.LeanTween
local CanvasGroup = typeof(CS.UnityEngine.CanvasGroup)
local screen_util = require"screen_util"
local time_util = require"time_util"
local CModelViewer 	        = require "modelviewer"
local event					= require "event"
local class                 = require "class"
local ui_base               = require "ui_base"
local module_scroll_list    = require "scroll_list"
local net_route             = require "net_route"
local log                   = require "log"
local flow_text             = require "flow_text"
local util                  = require "util"
local event                 = require "event"
local lang                  = require "lang"
local class                 = require "class"
local ui_base               = require "ui_base"
local ui_window_mgr         = require "ui_window_mgr"
local game_scheme           = require "game_scheme"
local BaseGameObject        = require "base_game_object"
-- local peak_mgr              = require "peak_mgr"
local puzzlegame_mgr       = require "puzzlegame_mgr"
local multply_item          = require "multply_item"
local IOSystem              = require "iosystem_load"
local player_mgr            = require "player_mgr"
local ui_pointing_target    = require "ui_pointing_target"
local reward_mgr            = require "reward_mgr"
local goods_item            = require "goods_item_new"
local iui_item_detail       = require "iui_item_detail"
local item_data             = require "item_data"
local sort_order            = require "sort_order"
local laymain_data          = require "laymain_data"
local gw_independent_game_mgr = require "gw_independent_game_mgr"
local gw_independent_game_data = require "gw_independent_game_data"
local minigame_select_manager = require "minigame_select_manager"
local gw_independent_game_const = require "gw_independent_game_const"
local TinyStaticDataCenter = require("tiny_static_data_center")
local red_const = require"red_const"
local Transform = CS.UnityEngine.Transform
local color_palette = require"color_palette"
local tostring = tostring
local GWG = GWG
local GWAdmin = GWAdmin

--@endregion 

--@region ModuleDeclare
module("ui_common_mini_game_level")
--local interface = require "iui_puzzle_game_level"

local window = nil
local M = {}

local curChapterId = 0
local lastGameType = 0
-- local maxLevel = 5
local closeCallback = nil
local normalColor = "FFFFFF"
local lockColor = "7D7D7D"
local chapterLevelReward = {}
local effbig = "art/effects/effects/effect_ui_puzzle_fade_big/prefabs/effect_ui_puzzle_fade_big.prefab"
local effsmall = "art/effects/effects/effect_ui_puzzle_fade_small/prefabs/effect_ui_puzzle_fade_small.prefab"
local unlockEff = nil
local effChapterId = 0
local effChapterObj = nil
local TOP_LEVEL_COUNT = 4
local MAX_CHAPTER_LEVEL_COUNT = 16
local MAX_REPEAT_LEVEL_COUNT = 12
local MAX_CACHE_LEVEL_COUNT = 20
local REPEAT_PAGE_HEIGHT = 1440
local EXT_TOP_HEIGHT = 720
local EXT_BOTTOM_HEIGHT = 350
local CHAPTER_PAGE_HEIGHT = 2160
local lastNotFinishLevel = 0
--遮罩8A8A8A
--普通亮、普通暗、普通遮罩、boss亮、boss暗、boss遮罩
local LevelImgResTable = {
    [gw_independent_game_const.BomberType] = {norL="qxtw_img_djzz_level1",norG="qxtw_img_djzz_level2",norM="qxtw_img_djzz_zz1",bossL="qxtw_img_djzz_level3",bossG="qxtw_img_djzz_level4",bossM="qxtw_img_djzz_zz2",outlineColorGray=color_palette.HexToColor("#343434"),textColorGray=color_palette.HexToColor("#FFFFFF"),outlineColorBlue=color_palette.HexToColor("#611900"),textColorBlue=color_palette.HexToColor("#FFFFE8")},--炮弹人
    [gw_independent_game_const.SaveDogType] = {norL="qxtw_img_zjgg_level1",norG="qxtw_img_zjgg_level2",norM="qxtw_img_sbtj_zz1",bossL="qxtw_img_zjgg_level3",bossG="qxtw_img_zjgg_level4",bossM="qxtw_img_sbtj_zz2",outlineColorGray=color_palette.HexToColor("#343434"),textColorGray=color_palette.HexToColor("#FFFFFF"),outlineColorBlue=color_palette.HexToColor("#611900"),textColorBlue=color_palette.HexToColor("#FFFFE8")},--救狗
    [gw_independent_game_const.SoldiersSortieType] = {norL="qxtw_img_sbtj_level1",norG="qxtw_img_sbtj_level2",norM="qxtw_img_sbtj_zz1",bossL="qxtw_img_sbtj_level3",bossG="qxtw_img_sbtj_level4",bossM="qxtw_img_sbtj_zz2",outlineColorGray=color_palette.HexToColor("#343434"),textColorGray=color_palette.HexToColor("#FFFFFF"),outlineColorBlue=color_palette.HexToColor("#611900"),textColorBlue=color_palette.HexToColor("#FFFFE8")},--士兵突击
    [gw_independent_game_const.NoTraceLeft] = {norL="qxtw_img_pjbl_level1",norG="qxtw_img_pjbl_level2",norM="qxtw_img_sbtj_zz1",bossL="qxtw_img_pjbl_level3",bossG="qxtw_img_pjbl_level4",bossM="qxtw_img_sbtj_zz2",outlineColorGray=color_palette.HexToColor("#343434"),textColorGray=color_palette.HexToColor("#FFFFFF"),outlineColorBlue=color_palette.HexToColor("#611900"),textColorBlue=color_palette.HexToColor("#FFFFE8")},--士兵突击
    [gw_independent_game_const.EightKnivesPerSecond] = {norL="qxtw_img_ymbd_level1",norG="qxtw_img_ymbd_level2",norM="qxtw_img_sbtj_zz1",bossL="qxtw_img_ymbd_level3",bossG="qxtw_img_ymbd_level4",bossM="qxtw_img_sbtj_zz2",outlineColorGray=color_palette.HexToColor("#343434"),textColorGray=color_palette.HexToColor("#FFFFFF"),outlineColorBlue=color_palette.HexToColor("#611900"),textColorBlue=color_palette.HexToColor("#FFFFE8")},--士兵突击
    [gw_independent_game_const.HotPursuit] = {norL="qxtw_img_jjdb_level1",norG="qxtw_img_jjdb_level2",norM="qxtw_img_sbtj_zz1",bossL="qxtw_img_jjdb_level3",bossG="qxtw_img_jjdb_level4",bossM="qxtw_img_sbtj_zz2",outlineColorGray=color_palette.HexToColor("#343434"),textColorGray=color_palette.HexToColor("#FFFFFF"),outlineColorBlue=color_palette.HexToColor("#611900"),textColorBlue=color_palette.HexToColor("#FFFFE8")},--紧急追捕
}
--@endregion 

--@region WidgetTable
M.widget_table = {
    --@region User
    closeBtn = {path = "bg/closeBtn", type = "Button",event_name="OnCloseEvent"},
    Viewport = {path = "Scene/Viewport", type = "RectTransform" },
    BgContent = {path = "Scene/Viewport/Content", type = "RectTransform" },
    ScrollRect = {path = "Scene", type = ScrollRect },
    ContRoot = {path = "Scene/Viewport/Content/Root", type = "RectTransform" },
    canvasGroup = {path = "Scene/Viewport/Content/Root", type = CanvasGroup },
    levelItem = {path = "Scene/Viewport/levelItem", type = "RectTransform" },
    levelItem1 = {path = "Scene/Viewport/levelItem1", type = "RectTransform" },
    title = {path = "bg/map_title/title", type = "Text" },

    uiCanvas = {path = "bg",type = Canvas},
    SortingGroup = {path = "bg",type = SortingGroup},
    tabList = {path = "bg/BtnGroup/Content", type = ScrollRectTable },

    --新增章节阶段通关奖励
    levelReward = {path = "bg/levelReward",type = "RectTransform" },
    PassText = {path = "bg/levelReward/Image/PassText",type = Text},
    progress = {path = "bg/levelReward/Activity/ScrollView/Viewport/Content/Progress",type = Slider},
    Rtst_progress = {path = "bg/levelReward/Activity/ScrollView/Viewport/Content/Progress",type = RectTransform},
    listItem = {path = "bg/levelReward/Activity/ScrollView/Viewport/Content/ListItem",type = "RectTransform"},
    rewardContent = {path = "bg/levelReward/Activity/ScrollView/Viewport/Content/Content",type = HorizontalLayoutGroup},
    rewardRoot = {path = "bg/levelReward/Activity/ScrollView/Viewport/Content",type = "RectTransform"},
    rewardScrollView = {path = "bg/levelReward/Activity/ScrollView",type = ScrollRect},
    --未解锁弹窗
    unlockTip  = {path = "bg/unlockTip",type = "RectTransform"},
    unlockDes = {path = "bg/unlockTip/unlockDes",type = "Text"},
    unlockDesIntroduce = {path = "bg/unlockTip/text",type = "Text"},
    GoHuntBtn = {path = "bg/unlockTip/GoHuntBtn",type = "Button",event_name="GoHuntEvent"},
    unlockCloseBtn = {path = "bg/unlockTip/closeBtn",type = "Button",event_name="OnCloseUnlockTipEvent"},
    gotoCurrentBtn1 = {path = "bg/gotoCurrentBtn1",type = "Button", event_name="GotoCurrentBtnEvent"},
    gotoCurrentBtn2 = {path = "bg/gotoCurrentBtn2",type = "Button", event_name="GotoCurrentBtnEvent"},

    preChapterBtn = {path = "bg/preChapterBtn",type = "Button", event_name="preChapterBtnEvent"},
    nextChapterBtn = {path = "bg/nextChapterBtn",type = "Button", event_name="nextChapterBtnBtnEvent"},

    soldierBtn  = {path = "bg/soldierRoot",type = "Button", event_name="soldierBtnEvent"},
    soldierRoot  = {path = "bg/soldierRoot",type = "RectTransform"},
    soldierNum = {path = "bg/soldierRoot/soldierNum",type = "Text"},
    soldierIcon = {path = "bg/soldierRoot/icon",type = "Image"},
    --minigameSelectRectTransform = { path = "bg/minigameSelect", type = "RectTransform" },
    --minigameSelectButton = { path = "bg/minigameSelect", type = "Button" , event_name = "ShowMinigameSelect" },
    --
    --minigameSelectRedDotCanvas = { path = "bg/minigameSelect/redDot", type = Canvas },
    --edit_hejigameTransform = { path = "bg/minigameSelect/edit_hejigame", type = Transform },
    --@endregion 
}
--@endregion 

--@region WindowCtor
function M:ctor(selfType)
    self.__base:ctor(selfType)
    -- 移动到了主界面底部条，加入缓存
    -- self.recycle_ui = true
    -- self.matchHomeIndicator = true
    --@region User
    --@endregion 
end --///<<< function

--@endregion 

--@endregion 

--@region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function M:OnShow()

    event.Trigger(event.RESET_MENUTOP_ORDER)
    event.Trigger(event.RESET_MENUBOT_ORDER)
    event.Trigger(event.ENABLE_ROW_BUTTON,true)
    self.gotoCurrentBtn1.gameObject:SetActive(false)
    self.gotoCurrentBtn2.gameObject:SetActive(false)
    self:ChangeGame(lastGameType)
    if check then
        util.RemoveDelayCall(check)
    end
    check = util.IntervalCall(0.2, function()
        if not self or not self:IsValid() then
            util.RemoveDelayCall(check)
        end
        --  --print("self.ContRoot.childCount:",self.ContRoot.childCount)
        if self.ContRoot and self.ContRoot.childCount > 8 then
            if not self or not self:IsValid() then return end
            util.RemoveDelayCall(check)
            check = nil
        end

        --UpdateScrollEnable()
    end)
    --@region User
    --@endregion 
end --///<<< function

function UpdateScrollEnable()
    if window and window:IsValid() then
        window.ScrollRect.enabled = true
    end
end

function OnItemDispose(scroll_rect_item, index)
    if scroll_rect_item.data and scroll_rect_item.data.item then
        scroll_rect_item.data.item:Dispose()
        scroll_rect_item.data.item = nil
    end
    UnLoadEffect(scroll_rect_item)
end

-- 更新章节阶段奖励UI详情
function M:UpdatePassReward()
    local rewardDatas = {}
    local stageNums = {}
    local passCount = 0


    if curChapterId <= 0 then return end
    passCount = gw_independent_game_mgr.GetPassCountByChapterId(curChapterId)

    local cfg = game_scheme:MiniGameControl_0(curChapterId)
    if not cfg then return end
    self.levelReward:SetActive(cfg.StageNum.count > 0)
    if cfg.StageNum.count <= 0 then
        return
    end
    for i=0, #cfg.StageReward.data do
        table.insert(rewardDatas, cfg.StageReward.data[i])
    end
    for i=0, #cfg.StageNum.data do
        table.insert(stageNums, cfg.StageNum.data[i])
    end

    local value = 0
    if passCount <= 0 then
        value = 0
    else
        if cfg and cfg.StageNum and stageNums and #stageNums > 0 then
            if passCount >= stageNums[#stageNums] then
                value = 1
            else
                local avg =  1 / #stageNums
                --  --print("avg:",avg)
                for i=1,#stageNums do
                    if passCount <= stageNums[i] then
                        local temp = stageNums[i] + 1 - passCount
                        --  --print("temp:",temp,"avg:",avg)
                        value = avg / temp + (avg * (i-1))
                        break
                    end
                end
            end
        end
    end
    self.progress.value = value
    self.PassText.text = string.format( lang.Get(2736),table.concat({passCount,"/",cfg.arrLevel.count}))

    local maxIsGetIndex = 0
    local OnItemRender = function(scroll_rect_item, index, dataItem)
        local child = scroll_rect_item.gameObject
        local rttf = child:GetComponent(typeof(RectTransform))
        local pos = rttf.anchoredPosition3D
        pos.x = pos.x + 25
        rttf.anchoredPosition3D = pos
        local i = index
        scroll_rect_item.data = scroll_rect_item.data or {}
        child:SetActive(true)
        local scr = child:GetComponent(typeof(ScrollRectItem))
        local Icon = scr:Get("Icon")
        local Count = scr:Get("Count")
        local red = scr:Get("red")

        local stageCount = stageNums[i]
        Count.text = stageCount

        local rewardID = rewardDatas[i]
        local rewardData = reward_mgr.GetRewardGoods(rewardID)
        if not scroll_rect_item.data.item then
            scroll_rect_item.data.item = goods_item.CGoodsItem()
            scroll_rect_item.data.item:Init(Icon,function()
            end,0.55)
            scroll_rect_item.data.item:SetCountEnable(true)
        end
        local isget = gw_independent_game_data.GetChapterStageReward(curChapterId,stageCount)
        local isCanGet = passCount >= stageCount and not isget
        maxIsGetIndex = isget and index or maxIsGetIndex
        red:SetActive(isCanGet)
        if isCanGet then
            LoadCanSigninEffect(scroll_rect_item, Icon, 0.7)   --加载光效
            -- chapterLevelReward[i]:
        end
        if isget then
            UnLoadEffect(scroll_rect_item)
        end
        scroll_rect_item.data.item:SetBattleMaskEnable(isget)
        scroll_rect_item.data.item:SetMaskEnable(isget)
        scroll_rect_item.data.item:SetGoods(nil, rewardData.id, rewardData.num,function (  )
            OnClickStageRewardItem(i,rewardData,true)
        end)
        if not scr.InvokeFunc then
            scr.InvokeFunc = function(funcname)
                if funcname == "onClick" then
                    OnClickStageRewardItem(i,rewardData)
                end
            end
        end

        if i >= #rewardDatas then
            LayoutRebuilder.ForceRebuildLayoutImmediate(self.rewardRoot)
            local pos = self.rewardRoot.anchoredPosition3D
            pos.x = 0
            if maxIsGetIndex >= 2 and #rewardDatas > 4 then
                local child = self.rewardContent.transform:GetChild(maxIsGetIndex-1):GetComponent(typeof(RectTransform))
                pos.x = -child.anchoredPosition3D.x
                pos.x = pos.x + self.rewardContent.spacing * 3 + 6
                pos.x = pos.x + child.sizeDelta.x
            end
            self.rewardRoot.anchoredPosition3D = {x=pos.x, y=pos.y, z=pos.z}
            util.DelayCall(0, function()
                LayoutRebuilder.ForceRebuildLayoutImmediate(self.rewardRoot)
            end)
        end
    end

    if self.Scrs then
        for index,scroll_rect_item in pairs(self.Scrs) do
            OnItemDispose(scroll_rect_item,index)
        end
    end
    self.Scrs = {}
    multply_item.Exe(#rewardDatas,function(i,ch)
        local scroll_rect_item = ch:GetComponent(typeof(ScrollRectItem))
        self.Scrs[i] = scroll_rect_item
        OnItemRender(scroll_rect_item, i)
    end,self.rewardContent,self.listItem)
    self.rewardScrollView.horizontal = #rewardDatas > 4
    LayoutRebuilder.ForceRebuildLayoutImmediate(self.rewardContent.transform)
end

function OnClickStageRewardItem(rewardIndex,rewardData,showItem)
    local passCount = gw_independent_game_mgr.GetPassCountByChapterId(curChapterId)
    local cfg = game_scheme:MiniGameControl_0(curChapterId)

    if not cfg then return end
    local stageCount = cfg.StageNum.data[rewardIndex-1]
    local isget = gw_independent_game_data.GetChapterStageReward(curChapterId,stageCount)
    local isCanGet = passCount >= stageCount and not isget
    if isCanGet then
        local rewardInfos = {}
        for i = 1,cfg.StageNum.count do
            stageCount = cfg.StageNum.data[i-1]
            isget = gw_independent_game_data.GetChapterStageReward(curChapterId,stageCount)
            isCanGet = passCount >= stageCount and not isget
            if isCanGet then
                table.insert(rewardInfos, {chapterid=curChapterId,stageNum=stageCount,ChaptersNum=cfg.ChaptersNum,nGameType = cfg.AdvertisingID})
            end
        end
        local net_independent_game = require "net_independent_game"
        net_independent_game.Send_XYX_STAGE_REWARD_MULTI_RSP(rewardInfos)
    elseif showItem then
        event.Trigger(event.CLICK_CHATPER_REWARD)
        iui_item_detail.Show(rewardData.id, nil, item_data.Item_Show_Type_Enum.Reward_Interface,nil, nil, nil,nil,nil,nil,nil,nil,function ()
            event.Trigger(event.CLOSE_CHATPER_REWARD)
        end)
    end
end

--@endregion 

--@region WindowOnHide
--[[界面隐藏时调用]]
function M:OnHide()
    --@region User
    --@endregion 
end --///<<< function

--@endregion 

--@region WindowSetInputParam
--[[设置窗口的输入参数。该参数通常是由其它模块或者外部设置进来。需要注意的是，
当调用这个函数的时候，窗口资源可能还是没有加载完成的。
@param p 参数表
]]
function M:SetInputParam(p)
    self.inputParam = p

    --@region User
    --@endregion 
end --///<<< function

--@endregion 



--@endregion 


--///tabList
function M:CreateTab()
    self.gameTabClick = function (eName,scroll_rect_item)
        if scroll_rect_item.data.typeId == lastGameType then
            return
        end
        self.curToggleIndex = scroll_rect_item.data.toggleIndex
        if unforced_guide_mgr.GetCurGuide() == 27 then
            unforced_guide_mgr.CloseGuide()
        end
        --之前为了兼容救狗原来的显示，现在不需要
        local cachedIndex = 2--lastGameType == gw_independent_game_const.SaveDogType and 1 or 2
        if self.cachedItems and self.cachedItems[cachedIndex]then
            for k,v in pairs(self.cachedItems[cachedIndex]) do
                self.OnLevelItemDispose(k, v)
            end
        end
        lastGameType = scroll_rect_item.data.typeId

        self:ChangeGame(lastGameType)
        scroll_rect_item:Get("Toggle").isOn = true
    end

    -- 游戏切页UI详情设置
    self.tabList.onItemRender=function(scroll_rect_item,index,dataItem  )
        scroll_rect_item.data = dataItem
        scroll_rect_item.InvokeFunc = self.gameTabClick

        local Background = scroll_rect_item:Get("Background")
        local name = scroll_rect_item:Get("name")
        local SelText = scroll_rect_item:Get("SelText")
        local red = scroll_rect_item:Get("red")
        window:BindUIRed(Background.transform,red_const.Enum.MiniGame_Tab_Red,{dataItem.typeId},{pos = { x = 46.18, y = 50.9 },redPath = red_const.Type.New})
        if dataItem.icon then
            Background.sprite = dataItem.icon
            Background:SetNativeSize()
        else
            self:CreateSubSprite("CreateXyxCardAsset", Background,dataItem.typeId,function ()
                if self:IsValid() then
                    Background:SetNativeSize()
                end
            end)
        end
        name.text =  lang.Get(dataItem.name)
        SelText.text =  lang.Get(dataItem.name)
        name:SetActive(dataItem.typeId ~= lastGameType)
        SelText:SetActive(dataItem.typeId == lastGameType)
        scroll_rect_item:Get("Toggle").isOn = (dataItem.typeId == lastGameType)

    end
    self.tabList.onItemDispose=function(scroll_rect_item,index)
        if scroll_rect_item.data then
            scroll_rect_item.InvokeFunc = nil
        end
    end
end

function M:DisposeTab()
    if self.tabList then
        self.tabList:ItemsDispose()
    end
end

--游戏签数据构建
function M:BuildTabListData()
    local _gameList = gw_independent_game_mgr.GetSortGameList()
    local listData = {}
    local data = {}
    local chaptersCfg = nil
    local toggleIndex = 1
    for i, v in ipairs(_gameList) do
        local gameInfo = gw_independent_game_mgr.GetGameDataByType(v)
        if gameInfo and gameInfo.isOpen then
            chaptersCfg = gw_independent_game_data.GetChaptersDataByType(gameInfo.TypeId)
            if chaptersCfg then
                data  = {
                    icon = gameInfo.Icon,
                    typeId = gameInfo.TypeId,
                    name = chaptersCfg[1].selectName,
                    toggleIndex = toggleIndex
                }
                if lastGameType == gameInfo.TypeId then
                    self.curToggleIndex = toggleIndex
                end
                toggleIndex = toggleIndex + 1
                table.insert(listData,data)
            end
        end
    end
    return listData
end

-- 检查 章节按钮 是否需要显示红点
function M:CheckChapterBtnRedBoll(cId)
    local chapterCfg = self:GetTinyGameChapter(cId)
    if chapterCfg == nil then
        return false
    end
    local listDatas = self:BuildLevelsData(chapterCfg)
    for i = 1, #listDatas do
        local data = listDatas[i]
        local levelCfg = GetTinyGameLevelInfo(data.levelId)
        local state = data.state
        if levelCfg ~= nil then
            local isshow = (state == gw_independent_game_const.LevelType.Unlock) and (levelCfg.iPreLevel == 0 or gw_independent_game_mgr.GetIsFinishByLevelId(levelCfg.iPreLevel or gw_independent_game_mgr.GetIsSkipByLevelId(levelCfg.iPreLevel)))
            if isshow then
                return true
            end
        end
    end
    return false
end

--切换游戏
function M:ChangeGame(gameType)
    --获取当前游戏的最新章节
    self.ScrollRect.enabled = false--gameType == gw_independent_game_const.SaveDogType
    local chapterID,currentLevel = gw_independent_game_mgr.GetLatestChapterDataByType(gameType)
    self.currentLevel = currentLevel
    self.currentChapterArr = gw_independent_game_data.GetChaptersDataByType(gameType)
    if self.currentChapterArr and self.currentChapterArr[1] then
        self.title.text = lang.Get(self.currentChapterArr[1].selectName)
    end
    self.currentChapterIndex = 0
    for k,v in ipairs(self.currentChapterArr) do
        if chapterID == v.ChaptersID then
            self.currentChapterIndex = k
        end
    end
    if self.currentChapterIndex == 0 then
        log.Error("当前章节索引错误","gameType",gameType,"chapterID",chapterID)
    end
    self.tabList:Refresh(0, -1)
    self:ChangePage(chapterID)
end

function M:UpdateSoliderRoot()
    local num = gw_independent_game_mgr.GetXYXRewardNum()
    local hasReward = false
    if num and num > 0 then
        hasReward = true
        self.soldierNum.text = tostring(num)
        local gw_home_soldier_data = require "gw_home_soldier_data"
        local _,cfgID = gw_home_soldier_data.GetCampusSoldierLevel()
        if cfgID == 0 then--没建兵营加容错
            cfgID = 311
        end
        self:CreateSubSprite("CreateSoldierHeadAsset", self.soldierIcon,tostring(cfgID),function ()

        end)
    end
    UIUtil.SetActive(self.soldierRoot,hasReward)
end

-- 切换章节
function M:ChangePage(cId)
    self.gameInfo = gw_independent_game_mgr.GetGameDataByType(lastGameType)
    if unforced_guide_mgr.GetCurGuide() == 27 and unforced_guide_mgr.GetCurStep() then
        unforced_guide_mgr.CloseGuide()
    end
    self:PlayAnimation()
    UIUtil.SetActive(self.preChapterBtn,self.currentChapterIndex > 1)
    --UIUtil.SetActive(self.nextChapterBtn,self.currentChapterIndex < #self.currentChapterArr)
    curChapterId = cId
    local chapterCfg = game_scheme:MiniGameControl_0(cId)

    if chapterCfg == nil then return end

    self:UpdatePassReward()
    local preChaptersID = chapterCfg.preChaptersID
    local preLevelCount = 0

    local chapterDates = gw_independent_game_data.GetChaptersDataByType(chapterCfg.AdvertisingID)
    --查找章节数量
    if chapterDates then
        local len = #chapterDates
        for _ = 1, len do
            if preChaptersID <= 0 then
                break
            end
            local c = game_scheme:MiniGameControl_0(preChaptersID)
            if c then
                if c.preChaptersID == preChaptersID then
                    log.Error("前置章节配置错误", preChaptersID)
                    break
                end
                preLevelCount = preLevelCount + c.arrLevel.count
                preChaptersID = c.preChaptersID
            else
                break
            end
        end
    else
        log.Error("获取章节数据失败", chapterCfg.AdvertisingID)
    end

    self.BgContent.sizeDelta =  {x=0,y=CHAPTER_PAGE_HEIGHT}
    self.inited = false
    self.gotoCurrentBtn1.gameObject:SetActive(false)
    self.gotoCurrentBtn2.gameObject:SetActive(false)


    --bg Image
    self:LoadImage(chapterCfg.imageResPath,self.BgContent,function ()
        if window and window:IsValid() then
            window:LoadChapter(chapterCfg, preLevelCount)
        end
    end)

    self:UpdateSoliderRoot()
end

--创角提示飘字
function M:FlowLevelUnLockTip(endTimestamp)
    local diff = endTimestamp
    if diff <= 0 then
        return
    end

    local hour = math.floor(diff / 3600)
    local min = math.floor((diff % 3600) / 60)
    local sec = diff % 60

    if diff > 3600 then
        return flow_text.Add(string.format2(lang.Get(607403), hour, min))
    else
        return flow_text.Add(string.format2(lang.Get(607404), min, sec))
    end
end

function M:LoadChapter(chapterCfg,preLevelCount)
    if not self.chRoot then return end

    --self:UpdateSelectEnter()

    local chRootTf = self.chRoot.transform
    chRootTf:SetAsFirstSibling()

    self.levelItemArr = self.levelItemArr or {}

    self.pages = {}
    local page1 = chRootTf:Find("page1")
    local page2 = chRootTf:Find("page2")
    local page3 = chRootTf:Find("page3")
    if page1 then
        self.pages[1] = page1:GetComponent(typeof(RectTransform))
    end
    if page2 then
        self.pages[2] = page2:GetComponent(typeof(RectTransform))
    end
    if page3 then
        self.pages[3] = page3:GetComponent(typeof(RectTransform))
    end

    local listDatas = self:BuildLevelsData(chapterCfg)
    self.levelDatas = listDatas
    self.levelCount = #self.levelDatas

    if not self:CompatibleMode() then
        self.pageDatas = self:BuildPageDatas()
    end

    -- 点击关卡
    self.OnLevelClick = self.OnLevelClick or function (eName,scri)
        if (unforced_guide_mgr.GetCurGuide() == 9 or unforced_guide_mgr.GetCurGuide() == 27) and unforced_guide_mgr.GetCurStep() then
            local unforced_guide_event_define = require "unforced_guide_event_define"
            event.Trigger(unforced_guide_event_define.click_miniGame_level_view)
            self.curUnforcedGuideLevelId = nil
        end
        local itemData = scri.data
        -- dump(itemData)
        if itemData then
            if itemData.state == gw_independent_game_const.LevelType.Finish then
                return
            end
            local minilevelCfg = game_scheme:MiniGameLevelControl_0(itemData.levelId, gw_independent_game_const.UnlockType)
            if not minilevelCfg then
                return
            end
            if minilevelCfg.iPreLevel ~= 0 and (not gw_independent_game_mgr.GetIsFinishByLevelId(minilevelCfg.iPreLevel)
                    and not gw_independent_game_mgr.GetIsSkipByLevelId(minilevelCfg.iPreLevel)) then
                flow_text.Add(lang.Get(2718))
                return
            end
            if minilevelCfg.CreatRole and minilevelCfg.CreatRole ~= 0  then
                if player_mgr.GetRoleCreateDaysFromZeroTime() < minilevelCfg.CreatRole then
                    --解锁当天0点
                    local time1 = player_mgr.GetRoleCreateTime() + (minilevelCfg.CreatRole-1)*3600*24
                    local time2 = time_util.GetServerTime0(time1)
                    self:FlowLevelUnLockTip(time2-os.server_time())
                    return
                end
            end
            local hookCfg = game_scheme:HookLevel_0(minilevelCfg.iUnlockStage) or {}
            local mapLv = laymain_data.GetPassLevel()
            local playerLv = player_mgr.GetPlayerLV()
            local passMapCfg = game_scheme:HookLevel_0(mapLv)
            if hookCfg and (minilevelCfg.iUnlockStage ~= 0 and (passMapCfg and passMapCfg.checkPointID < minilevelCfg.iUnlockStage)) then
                self.unlockTip.gameObject:SetActive(true)
                --self.unlockDes.text = string.format(lang.Get(2729),hookCfg.ChapterID .. "-" .. hookCfg.LevelID)
                local gw_home_chapter_util = require "gw_home_chapter_util"
                local jumpParams = gw_home_chapter_util.MiniGameUnlockJump(hookCfg.checkPointID)
                if jumpParams.title then
                    self.unlockDes.text = jumpParams.title
                end
                if jumpParams.info then
                    self.unlockDesIntroduce.text = jumpParams.info
                end
                if jumpParams.jumpFunc then
                    self._jumpFunc = jumpParams.jumpFunc
                end
                if unforced_guide_mgr.GetCurGuide() == 27 and unforced_guide_mgr.GetCurStep() then
                    local unforced_guide_event_define = require "unforced_guide_event_define"
                    event.Trigger(unforced_guide_event_define.start_miniGame_go_view)
                    self.curUnforcedGuideLevelId = nil
                end
                return
            end
            if minilevelCfg.iPrebuilding ~= 0 and playerLv and playerLv < minilevelCfg.iPrebuilding then
                self.unlockTip.gameObject:SetActive(true)
                self.unlockDes.text = string.format2(lang.Get(607395), minilevelCfg.iPrebuilding)
                self.unlockDesIntroduce.text = lang.Get(607396)
                local function jumpPreBuilding()
                    local gw_home_camera_util = require "gw_home_camera_util"
                    local gw_const = require "gw_const"
                    local gw_home_building_data = require "gw_home_building_data"
                    local mainId = gw_home_building_data.GetBuildingIdByBuildingType(gw_const.enBuildingType.enBuildingType_Main)
                    ui_window_mgr:UnloadModule("ui_common_mini_game_level")
                    gw_home_camera_util.DoCameraToBuildMove(mainId, nil, gw_const.CameraMoveTime, true, false, nil, 2)
                end
                self._jumpFunc = jumpPreBuilding
                if unforced_guide_mgr.GetCurGuide() == 27 and unforced_guide_mgr.GetCurStep() then
                    local unforced_guide_event_define = require "unforced_guide_event_define"
                    event.Trigger(unforced_guide_event_define.start_miniGame_go_view)
                    self.curUnforcedGuideLevelId = nil
                end
                return
            end
            --可以打开游戏
            gw_independent_game_mgr.OpenMiniGame(lastGameType,minilevelCfg.LevelID,nil,self.curToggleIndex == 1 )
            gw_independent_game_mgr.SetLevleIsChallenge(itemData.levelId)
        end
    end

    local centerLevelIndex = self:SetRootContent(listDatas)
    lastNotFinishLevel = centerLevelIndex

    --目前这块逻辑没触发，先留着
    if not self:CompatibleMode() then
        local bottom, top = self:GetScreenRange()
        self.pageIndex = self:GetTopPageIndex()
        self.preTop = top
        self:InitPageBg(1, self.pageIndex+2)
        self:InitPageBg(2, self.pageIndex+1)
        self:InitPageBg(3, self.pageIndex)
    end

    self.PlayNodeAnim = function(i,node)
        local duration = 0.4
        local amplitude = 50  -- 上下移动幅度
        if util.IsObjNull(node) then
            return
        end
        -- 记录初始位置
        local startPos = node.transform.localPosition
        -- Tween上下浮动：用LeanTween的pingPong循环，但时长限制1秒
        self.nodeTween = self.nodeTween or {}
        self.nodeTween[i] = LeanTween.moveLocalY(node.gameObject, startPos.y + amplitude, duration/2)
                                  :setLoopPingPong(1)  -- 上下往返1次，总时长约1秒
                                  :setOnComplete(function()
            -- 完成后重置到初始位置
            node.transform.localPosition = startPos
        end)
    end
    self.OnLevelItemRender = function(i, ch)
        local data = listDatas[i]
        if not data then
            self.OnLevelItemDispose(i, ch)
            return
        end
        local levelCfg = game_scheme:MiniGameLevelControl_0(data.levelId,gw_independent_game_const.UnlockType)
        if not levelCfg then
            log.Error("MiniGameLevelControl cfg nil ",data.levelId)
            return
        end

        local rootRect = ch:GetComponent(typeof(RectTransform))
        local pos1 = data.pos
        if self:CompatibleMode() then
            pos1 = chRootTf:GetChild(i - 1).anchoredPosition3D
        end
        rootRect.anchoredPosition3D = pos1
        if self.currentLevel == data.levelId then
            if self.delayCall then
                util.RemoveDelayCall(self.delayCall)
                self.delayCall = nil
            end
            self.delayCall = util.DelayCallOnce(0.25,function()
                self.PlayNodeAnim(i,rootRect)
            end)
        end
        local levelItemArr = ch:GetComponent(typeof(ScrollRectItem))
        levelItemArr.InvokeFunc = self.OnLevelClick
        levelItemArr.data = data
        levelItemArr.gameObject:SetActive(true)

        local levelbg = levelItemArr:Get("levelbg")
        local red = levelItemArr:Get("red")
        local ad = levelItemArr:Get("ad")
        local effbigRoot = levelItemArr:Get("effBig")
        local name = levelItemArr:Get("name")
        local levelItem = levelItemArr:Get("levelItem")
        local levelbgSW = levelItemArr:Get("levelbgSW")
        local levelbgImgray = levelItemArr:Get("levelbgImgray")
        local BgLevel = levelItemArr:Get("BgLevel")
        local state = data.state
        levelItem.name = "levelItem"..i

        local isBoss = levelCfg.BossTag == 1
        local gameInfo = self.gameInfo
        if isBoss then
            if gameInfo and not util.IsObjNull(gameInfo.IconChapter) then
                levelbg.sprite = gameInfo.IconChapter
            else
                levelbgSW:Switch(1)
            end
        else
            if gameInfo and not util.IsObjNull(gameInfo.IconChapter2) then
                levelbg.sprite = gameInfo.IconChapter2
            else
                levelbgSW:Switch(0)
            end
        end
        levelbg:SetNativeSize()

        local isFinish = state == gw_independent_game_const.LevelType.Finish
        levelItemArr:Get("pass"):SetActive(isFinish)
        levelbg.color = isFinish and color_palette.HexToColor("#8A8A8A") or color_palette.HexToColor("#FFFFFF")
        window:BindUIRed(red,red_const.Enum.MiniGame_Level_Red,{lastGameType,data.levelId},{redPath = red_const.Type.Default})
        
        local levelNum = i+preLevelCount

        BgLevel.text=levelNum

        if state == gw_independent_game_const.LevelType.Unlock then
            if levelCfg.iPreLevel == 0 or gw_independent_game_mgr.GetIsFinishByLevelId(levelCfg.iPreLevel)
                    or gw_independent_game_mgr.GetIsSkipByLevelId(levelCfg.iPreLevel) then
                levelbgImgray:SetEnable(false)
                BgLevel.curOutlineColor=color_palette.HexToColor("#611900")
                BgLevel.color=color_palette.HexToColor("#FFFFE8")
            else
                levelbgImgray:SetEnable(true)
                BgLevel.curOutlineColor=color_palette.HexToColor("#343434")
                BgLevel.color=color_palette.HexToColor("#FFFFFF")
            end
        elseif state == gw_independent_game_const.LevelType.Skip then
            levelbgImgray:SetEnable(false)
            BgLevel.curOutlineColor=color_palette.HexToColor("#611900")
            BgLevel.color=color_palette.HexToColor("#FFFFE8")
        elseif state == gw_independent_game_const.LevelType.Lock then
            levelbgImgray:SetEnable(true)
            BgLevel.curOutlineColor=color_palette.HexToColor("#343434")
            BgLevel.color=color_palette.HexToColor("#FFFFFF")
        elseif isFinish then
            levelbgImgray:SetEnable(false)
            BgLevel.curOutlineColor=color_palette.HexToColor("#611900")
            BgLevel.color=color_palette.HexToColor("#FFFFE8")
        end


        --找到第一个没有通关的关卡进行指引
        if isFinish then
            return
        end
        if state == gw_independent_game_const.LevelType.Skip then
            return
        end
        if self.curUnforcedGuideLevelId then
            return
        end
        --指引进入关卡
        if unforced_guide_mgr.GetCurGuide() == 9 and unforced_guide_mgr.GetCurStep() then
            local unforced_guide_event_define = require "unforced_guide_event_define"
            event.Trigger(unforced_guide_event_define.start_miniGame_level_view, nil, nil, levelbg.transform)
            self.curUnforcedGuideLevelId = levelCfg.LevelID
            return
        end
        local isshow = (state == gw_independent_game_const.LevelType.Unlock) and (levelCfg.iPreLevel == 0 or gw_independent_game_mgr.GetIsFinishByLevelId(levelCfg.iPreLevel) or gw_independent_game_mgr.GetIsSkipByLevelId(levelCfg.iPreLevel))
        if not isshow then
            return
        end
        --判断关卡是否在未解锁状态
        local hookCfg = game_scheme:HookLevel_0(levelCfg.iUnlockStage) or {}
        local mapLv = laymain_data.GetPassLevel()
        local playerLv = player_mgr.GetPlayerLV()
        local passMapCfg = game_scheme:HookLevel_0(mapLv)
        if hookCfg and (levelCfg.iUnlockStage ~= 0 and (passMapCfg and passMapCfg.checkPointID <= levelCfg.iUnlockStage)) then
            --触发引导
            if unforced_guide_mgr.NewUnlock(27) then
                local unforced_guide_event_define = require "unforced_guide_event_define"
                event.Trigger(unforced_guide_event_define.start_miniGame_level_view, nil, nil, ch.transform)
                self.curUnforcedGuideLevelId = levelCfg.LevelID
                return
            end
        end
        if levelCfg.iPrebuilding ~= 0 and playerLv and playerLv < levelCfg.iPrebuilding then
            --触发引导
            if unforced_guide_mgr.NewUnlock(27) then
                local unforced_guide_event_define = require "unforced_guide_event_define"
                event.Trigger(unforced_guide_event_define.start_miniGame_level_view, nil, nil, ch.transform)
                self.curUnforcedGuideLevelId = levelCfg.LevelID
                return
            end
        end
    end

    self.OnLevelItemDispose = function(i, ch)
        local rootRect = ch:GetComponent(typeof(RectTransform))
        if self.nodeTween and self.nodeTween[i] then
            LeanTween.cancel(self.nodeTween[i].uniqueId)
        end
        rootRect.anchoredPosition3D = self.levelItem.anchoredPosition3D
    end

    self.bottomLevelIndex, self.topLevelIndex = self:GetLevelIndexRange(listDatas, centerLevelIndex)
    self.bottomIndex = 1
    self.topIndex = self.bottomIndex + (self.topLevelIndex-self.bottomLevelIndex)
    self.cachedItems = self.cachedItems or {{},{}}
    local cachedIndex = 2--lastGameType == gw_independent_game_const.SaveDogType and 1 or 2
    if not next(self.cachedItems[cachedIndex]) then
        --loadItem = function(i, ch)
        --    self.cachedItems[cachedIndex][i] = ch
        --end
        -- 关卡UI详情设置
        local levelItem = self.levelItem1--lastGameType == gw_independent_game_const.SaveDogType and self.levelItem or self.levelItem1
        --multply_item.Exe(MAX_CACHE_LEVEL_COUNT, loadItem, self.ContRoot, levelItem)
        for i = 1 ,MAX_CACHE_LEVEL_COUNT do
            local newItem = GameObject.Instantiate(levelItem)
            newItem.transform:SetParent(self.ContRoot.transform,false)
            self.cachedItems[cachedIndex][i] = newItem
        end

    end
    for i=1, MAX_CACHE_LEVEL_COUNT do
        local ch = self.cachedItems[cachedIndex][i]
        local index = self.bottomLevelIndex + i - 1
        if i>=self.bottomIndex and i<=self.topIndex then
            self.OnLevelItemRender(index, ch)
        else
            self.OnLevelItemDispose(index, ch)
        end
    end
    self.inited = true

end

--function M:UpdateSelectEnter()
--    local passLevel = laymain_data.GetPassLevel()
--    if minigame_select_manager.IsMinigameSelectMode() and passLevel >= 6 then
--        self.minigameSelectRectTransform:SetActive(minigame_select_manager.IsMinigameSelectMode())
--        self.minigameSelectRedDotCanvas:SetActive(minigame_select_manager.CanUnlockNext())
--        self.edit_hejigameTransform:SetActive(minigame_select_manager.CanSnakeEnterImage())
--    else
--        self.minigameSelectRectTransform:SetActive(false)
--        self.minigameSelectRedDotCanvas:SetActive(false)
--        self.edit_hejigameTransform:SetActive(false)
--    end
--end

function M:SetRootContent(listDatas)
    --记录最小一个未通关的关卡（需要scrollerto）
    local lastNotFinishLevel = #listDatas
    local lowestPos = CHAPTER_PAGE_HEIGHT

    local sizeDelta = {x=0,y=lowestPos}
    local focusPos = listDatas[1].pos
    for i = 1, #listDatas do
        local data = listDatas[i]
        local bOpen = ( data.state ~= gw_independent_game_const.LevelType.Lock )
        if data.state ~= gw_independent_game_const.LevelType.Finish then
            if i < lastNotFinishLevel then
                lastNotFinishLevel = i
            end
        end
        if bOpen then
            focusPos = data.pos
        end
    end

    if not self:CompatibleMode() then
        sizeDelta.y = -listDatas[1].pos.y + EXT_BOTTOM_HEIGHT
    end

    self.BgContent.sizeDelta = sizeDelta
    local pos = nil
    if lastNotFinishLevel and listDatas[lastNotFinishLevel] then
        pos = listDatas[lastNotFinishLevel].pos
    end
    if self:CompatibleMode() then
        local lastNotFinishLevelTransform = self.ScrollRect.content:GetChild(0):GetChild(lastNotFinishLevel - 1)
        if lastNotFinishLevelTransform then
            pos = lastNotFinishLevelTransform.anchoredPosition3D
        end
    end
    --if lastGameType == gw_independent_game_const.SaveDogType then
    if false then--
        if pos then
            local rect = self.Viewport.rect
            local y = math.min(-pos.y - rect.height / 2, sizeDelta.y-rect.height)
            self.BgContent.transform.anchoredPosition3D = { x = self.BgContent.transform.anchoredPosition3D.x, y = y}
        else
            local passCount = gw_independent_game_mgr.GetPassCountByChapterId(curChapterId)
            if passCount < self.levelCount/2 then
                local YOff = lowestPos/2 - focusPos.y --最高端

                self.BgContent.transform.localPosition = {x=0,y=YOff,z=0}
            else
                local YOff =   -focusPos.y - lowestPos/2 --最低端
                self.BgContent.transform.localPosition = {x=0,y=YOff,z=0}
            end
        end
    else
        if screen_util.GetScreenAspect() < 0.5 then
            self.BgContent.transform.anchoredPosition = {x=0,y=592}
        else
            self.BgContent.transform.anchoredPosition = {x=0,y=653}
        end

    end

    return lastNotFinishLevel
end



function M:DisposeLevelItems()
end

-- 构建章节关卡数据
function M:BuildLevelsData(cfg)

    local levelsData = {}
    local chapterCfg = cfg

    --挂点坐标
    local rawArrPos = string.split(chapterCfg.arrlevelPos, ";")
    local arrPos = {}
    for i, v in ipairs(rawArrPos) do
        local xyz = string.split(v, "#", tonumber)
        xyz = {x =xyz[1],y =xyz[2],z =xyz[3] }
        table.insert(arrPos,xyz)
    end

    local levelCount = #arrPos - TOP_LEVEL_COUNT
    local pageCount = math.ceil(levelCount/MAX_REPEAT_LEVEL_COUNT)
    local bottomCount = levelCount % MAX_REPEAT_LEVEL_COUNT
    for i = 0, chapterCfg.arrLevel.count - 1 do
        local levelId = chapterCfg.arrLevel.data[i]
        -- if levelId <= maxLevel then
        local tData = {}
        tData.chapterId = chapterCfg.ChaptersID
        tData.levelId = levelId
        -- tData.chapterCfg = chapterCfg
        tData.levelCfg = game_scheme:MiniGameLevelControl_0(levelId,gw_independent_game_const.UnlockType)
        tData.pos = arrPos[i + 1]
        if not self:CompatibleMode() then
            if i < #arrPos - TOP_LEVEL_COUNT then
                local id = bottomCount > 0 and i + (MAX_REPEAT_LEVEL_COUNT-bottomCount) or i
                tData.pos.y = tData.pos.y - (pageCount - math.floor(id / MAX_REPEAT_LEVEL_COUNT) - 1) * REPEAT_PAGE_HEIGHT - EXT_TOP_HEIGHT
            end
        end
        tData.state = gw_independent_game_mgr.GetStateByLevel(levelId)
        table.insert(levelsData, tData)
        -- end
    end
    -- dump(levelsData)
    return levelsData
end

function M:Init()
    orderStart, orderEnd = sort_order.ApplyBaseIndexs(self,nil, 5)	--重新获得层级
    self:DefaultGameTypeChapterId()
    self:CreateTab()
    self:UpdateTabList()
    self:SubscribeEvents()
    net_route.RegisterMsgHandlers(MessageTable)
    window.sceneReturnFlag = false
    self.inited = false
    self.cachedItems = nil

    if self.chRoot then
        self.chRoot.transform:SetParent(self.BgContent,false)
    end

    if M.updateTickId then
        util.RemoveDelayCall(M.updateTickId)
        M.updateTickId = nil
    end
    M.updateTickId = util.IntervalCall(0,self.Tick)
end

function M:GetScreenRange()
    local rect = self.Viewport.rect
    local pos = self.BgContent.transform.anchoredPosition3D
    return -rect.height-pos.y, -pos.y
end

function M:GetTopPageIndex()
    local bottom, top = self:GetScreenRange()
    for k, v in pairs(self.pageDatas) do
        if top >= v.bottom then
            return k
        end
    end
    return 0
end

function M:Tick()
    if window and window:IsValid() and window.inited then
        --目前这块逻辑没触发，先留着
        if not window:CompatibleMode() then
            if window.moveTo then
                window.moveTo = window:MoveToCurrent()
            end
            local bottom, top = window:GetScreenRange()
            local halfSize = window.levelItem.sizeDelta
            halfSize.x, halfSize.y = halfSize.x / 2, halfSize.y / 2
            while(window:ScrollViewRender(window.levelDatas, bottom, top, halfSize)) do end
            local pageIndex = window:GetTopPageIndex()
            if pageIndex ~= window.pageIndex then
                window:InitPageBg(1, pageIndex+2)
                window:InitPageBg(2, pageIndex+1)
                window:InitPageBg(3, pageIndex)
            end
            local pos = window.BgContent.transform.anchoredPosition3D
            local centerPos = -(pos.y + window.Viewport.rect.height/2)
            local centerLevelIndex = window.topLevelIndex
            for i=window.topLevelIndex-1, window.bottomLevelIndex, -1 do
                if math.abs(window.levelDatas[i].pos.y - centerPos) < math.abs(window.levelDatas[centerLevelIndex].pos.y - centerPos) then
                    centerLevelIndex = i
                end
            end
            window.gotoCurrentBtn2.gameObject:SetActive(centerLevelIndex - lastNotFinishLevel <= -16)
            window.gotoCurrentBtn1.gameObject:SetActive(centerLevelIndex - lastNotFinishLevel >= 16)
            window.pageIndex = pageIndex
            window.preTop = top
        end
    end
end

function M:ResetJiemiPoints(index, targetPageIndex, dataIndex, bg)
    for i=1, MAX_REPEAT_LEVEL_COUNT do
        local id = dataIndex+i-1
        local child = bg:Find("jiemiPoint"..i)
        if child then
            if targetPageIndex >= 0 and self.levelDatas[id] and id > 1 then
                child.gameObject:SetActive(true)
            else
                child.gameObject:SetActive(false)
            end
        end
    end
end

function M:CompatibleMode()
    return not (self.pages and self.pages[1] and self.pages[2] and self.pages[3])
end

function M:GetLevelIndexRange(levelDatas, centerIndex)
    if self:CompatibleMode() then
        return 1, MAX_CHAPTER_LEVEL_COUNT
    end
    local halfSize = self.levelItem.sizeDelta
    halfSize.x, halfSize.y = halfSize.x / 2, halfSize.y / 2
    local bottom, top = self:GetScreenRange()
    local _start = math.max(centerIndex - MAX_CHAPTER_LEVEL_COUNT, 1)
    local _end = math.min(centerIndex + MAX_CHAPTER_LEVEL_COUNT, self.levelCount)
    local topIndex = _start
    local bottomIndex = _end
    for i=_start, _end do
        local data = levelDatas[i]
        local y1 = data.pos.y + halfSize.y
        local y2 = data.pos.y - halfSize.y
        if y2 < top then
            topIndex = i
        end
        if y1 > bottom and i < bottomIndex then
            bottomIndex = i
        end
    end
    return bottomIndex, topIndex
end

--目前这块逻辑没触发，先留着
function M:ScrollViewRender(levelDatas, bottom, top, halfSize)
    local keep = false
    --向上滚动
    local newLevelIndex = math.min(self.topLevelIndex + 1, self.levelCount)
    local data = levelDatas[newLevelIndex]
    local y1 = data.pos.y + halfSize.y
    local y2 = data.pos.y - halfSize.y
    if y2 < top and newLevelIndex ~= self.topLevelIndex then
        self.topIndex = self.topIndex + 1
        if self.topIndex > MAX_CACHE_LEVEL_COUNT then
            self.topIndex = 1
        end
        self.topLevelIndex = self.topLevelIndex + 1
        local cachedIndex = 2--lastGameType == gw_independent_game_const.SaveDogType and 1 or 2
        self.OnLevelItemRender(newLevelIndex, self.cachedItems[cachedIndex][self.topIndex])
        keep = true
    end
    data = levelDatas[self.bottomLevelIndex]
    y1 = data.pos.y + halfSize.y
    y2 = data.pos.y - halfSize.y
    if y1 < bottom then
        newLevelIndex = self.bottomLevelIndex
        local bottomIndex = self.bottomIndex
        self.bottomLevelIndex = math.min(self.bottomLevelIndex + 1, self.levelCount)
        if newLevelIndex ~= self.bottomLevelIndex then
            self.bottomIndex = self.bottomIndex + 1
            if self.bottomIndex > MAX_CACHE_LEVEL_COUNT then
                self.bottomIndex = 1
            end
            local cachedIndex = 2--lastGameType == gw_independent_game_const.SaveDogType and 1 or 2
            self.OnLevelItemDispose(newLevelIndex, self.cachedItems[cachedIndex][bottomIndex])
            keep = true
        end
    end

    --向下滚动
    newLevelIndex = math.max(self.bottomLevelIndex - 1, 1)
    data = levelDatas[newLevelIndex]
    y1 = data.pos.y + halfSize.y
    y2 = data.pos.y - halfSize.y
    if y1 > bottom and newLevelIndex ~= self.bottomLevelIndex then
        self.bottomIndex = self.bottomIndex - 1
        if self.bottomIndex < 1 then
            self.bottomIndex = MAX_CACHE_LEVEL_COUNT
        end
        self.bottomLevelIndex = self.bottomLevelIndex - 1
        local cachedIndex = 2--lastGameType == gw_independent_game_const.SaveDogType and 1 or 2
        self.OnLevelItemRender(newLevelIndex, self.cachedItems[cachedIndex][self.bottomIndex])
        keep = true
    end
    data = levelDatas[self.topLevelIndex]
    y1 = data.pos.y + halfSize.y
    y2 = data.pos.y - halfSize.y
    if y2 > top then
        newLevelIndex = self.topLevelIndex
        local topIndex = self.topIndex
        self.topLevelIndex = math.max(self.topLevelIndex - 1, 1)
        if newLevelIndex ~= self.topLevelIndex then
            self.topIndex = self.topIndex - 1
            if self.topIndex < 1 then
                self.topIndex = MAX_CACHE_LEVEL_COUNT
            end
            local cachedIndex = 2--lastGameType == gw_independent_game_const.SaveDogType and 1 or 2
            self.OnLevelItemDispose(newLevelIndex, self.cachedItems[cachedIndex][topIndex])
            keep = true
        end
    end
    return keep
end

--目前这块逻辑没触发，先留着
function M:InitPageBg(index, pageIndex)
    local pagedata
    local lastPageIndex = #self.pageDatas
    if pageIndex <= lastPageIndex then
        pagedata = self.pageDatas[pageIndex]
    else
        pagedata = {dataIndex=-100, top=self.pageDatas[lastPageIndex].top-REPEAT_PAGE_HEIGHT*(pageIndex-lastPageIndex), bg=1}
    end
    self.pages[index].anchoredPosition3D = {x=0, y=pagedata.top, z=0}
    local scrollRectItem = self.pages[index]:GetComponent(typeof(ScrollRectItem))
    if pagedata.bg == 1 then
        scrollRectItem:Get("bg1").gameObject:SetActive(true)
        scrollRectItem:Get("bg2").gameObject:SetActive(false)
        self:ResetJiemiPoints(index, pageIndex, pagedata.dataIndex, scrollRectItem:Get("bg1"))
    else
        scrollRectItem:Get("bg1").gameObject:SetActive(false)
        scrollRectItem:Get("bg2").gameObject:SetActive(true)
        self:ResetJiemiPoints(index, pageIndex, pagedata.dataIndex, scrollRectItem:Get("bg2"))
    end
end

function M:BuildPageDatas()
    local PageDatas = {}
    table.insert(PageDatas, {bg=2, top=0, bottom=-EXT_TOP_HEIGHT, dataIndex=self.levelCount-TOP_LEVEL_COUNT+1, height=EXT_TOP_HEIGHT})
    local levelCount = self.levelCount - TOP_LEVEL_COUNT
    local pageCount = math.ceil(levelCount/MAX_REPEAT_LEVEL_COUNT)
    for i=1, pageCount do
        table.insert(PageDatas, {bg=1, top=-EXT_TOP_HEIGHT-REPEAT_PAGE_HEIGHT*(i-1), bottom=-EXT_TOP_HEIGHT-REPEAT_PAGE_HEIGHT*i, dataIndex=self.levelCount-TOP_LEVEL_COUNT-MAX_REPEAT_LEVEL_COUNT*i+1, height=REPEAT_PAGE_HEIGHT})
    end
    return PageDatas
end

function M:UpdateTabList(  )
    self.tabList.data = self:BuildTabListData()
    self.tabList:Refresh(0, -1)
end

function M:Close()
    if (unforced_guide_mgr.GetCurGuide() == 9) and unforced_guide_mgr.GetCurStep() then
        local unforced_guide_event_define = require "unforced_guide_event_define"
        event.Trigger(unforced_guide_event_define.click_miniGame_level_view)
        self.curUnforcedGuideLevelId = nil
    end
    if unforced_guide_mgr.GetCurGuide() == 27 and unforced_guide_mgr.GetCurStep() then
        unforced_guide_mgr.CloseGuide()
    end
    net_route.UnregisterMsgHandlers(MessageTable)
    if self:IsValid() then
        self:UnsubscribeEvents()
        util.SetCameraHuiguang(false)
    end

    if self.Scrs then
        for index,scroll_rect_item in pairs(self.Scrs) do
            OnItemDispose(scroll_rect_item,index)
        end
        self.Scrs = nil
    end

    if self.rewardScrollTable then
        self.rewardScrollTable:ItemsDispose()
    end

    if self.chRoot then
        self.chRoot:Dispose()
        self.chRoot = nil
    end
    if self.moveAniTick then
        util.RemoveDelayCall(self.moveAniTick)
        self.moveAniTick = nil
    end

    if ui_window_mgr:IsModuleShown("ui_pointing_target") then
        ui_pointing_target.CloseWithParam()
    end
    if self.ContRoot and not self.ContRoot:IsNull() then
        for i=1,self.ContRoot.childCount do
            local chNode = self.ContRoot:GetChild(i-1)
            local levelItemArr = chNode:GetComponent(typeof(ScrollRectItem))
            levelItemArr.InvokeFunc = nil
        end
    end
    for i,v in ipairs(chapterLevelReward) do
        v:Dispose()
        v = nil
    end
    
    chapterLevelReward = {}
    lastNotFinishLevel = 0
    effChapterId = 0
    curChapterId = 0
    lastGameType = 0
    effChapterObj = nil
    self:DisposeLevelItems()
    self:DisposeTab()
    event.Trigger(event.ENABLE_ROW_BUTTON,false)
    if check then
        util.RemoveDelayCall(check)
    end
    check = nil
    self.__base:Close()
    if window then
        window.sceneReturnFlag = false
    end
    window = nil
end

function M:SetActive(obj, active)
    local go = obj and obj.gameObject
    if go then
        if go.activeSelf ~= active then
            go:SetActive(active)
        end
    end
end

function M:MoveToCurrent()
    local pos = window.BgContent.transform.anchoredPosition3D
    local centerPos = -(pos.y + window.Viewport.rect.height/2)
    local centerLevelIndex = window.topLevelIndex
    for i=window.topLevelIndex-1, window.bottomLevelIndex, -1 do
        if math.abs(window.levelDatas[i].pos.y - centerPos) < math.abs(window.levelDatas[centerLevelIndex].pos.y - centerPos) then
            centerLevelIndex = i
        end
    end
    local down = centerLevelIndex > lastNotFinishLevel
    local offset
    if math.abs(window.levelDatas[centerLevelIndex].pos.y - window.levelDatas[lastNotFinishLevel].pos.y) > 1000 then
        offset = down and 1000 or -1000
    else
        offset = window.levelDatas[centerLevelIndex].pos.y - window.levelDatas[lastNotFinishLevel].pos.y
    end
    local sizeDelta = window.BgContent.sizeDelta
    if down then
        pos.y = math.min(pos.y+offset, sizeDelta.y-window.Viewport.rect.height)
    else
        pos.y = math.max(pos.y+offset, 0)
    end
    window.BgContent.transform.anchoredPosition3D = pos
    local moveTo = false
    if centerLevelIndex ~= lastNotFinishLevel and math.floor(pos.y)~=math.floor(sizeDelta.y-window.Viewport.rect.height) and math.floor(pos.y)~=0 then
        moveTo = true
        window.ScrollRect.inertia = false
    else
        window.ScrollRect.inertia = true
    end
    return moveTo
end

function M:SubscribeEvents()
    self.OnCloseEvent = function ()
        local mainSlgMgr = require "main_slg_mgr"
        mainSlgMgr.EnterBattleState(false)
        ui_window_mgr:UnloadModule("ui_common_mini_game_level")
        if closeCallback then
            local cb = closeCallback
            closeCallback = nil
            cb()
        end
        -- local button_tips_trigger = require "button_tips_trigger"
        -- button_tips_trigger.CheckTask()
    end

    self.GoHuntEvent = function ()
        self.OnCloseEvent()


        if self._jumpFunc then
            self._jumpFunc()
            self._jumpFunc = nil
        end
    end
    self.OnCloseUnlockTipEvent = function()
        if unforced_guide_mgr.GetCurGuide() == 27 and unforced_guide_mgr.GetCurStep() then
            unforced_guide_mgr.CloseGuide()
        end
        self.unlockTip.gameObject:SetActive(false)

    end

    self.GotoCurrentBtnEvent = function()
        self.moveTo = true
    end

    self.nextChapterBtnBtnEvent = function()
        local currentChapterIndex = self.currentChapterIndex
        if currentChapterIndex < #self.currentChapterArr then
            local chapterCfg = self.currentChapterArr[currentChapterIndex+1]
            if chapterCfg and chapterCfg.ChaptersID then
                self.currentChapterIndex = currentChapterIndex+1
                self:ChangePage(chapterCfg.ChaptersID)
            end
        else
            flow_text.Add(lang.Get(902))--lang.Get(902)
        end
    end

    self.soldierBtnEvent = function()
        local gw_common_util = require "gw_common_util"
        local func = function()
            local typeId = GWG.GWConst.enBuildingType.enBuildingType_Garden
            local buildId = GWG.GWHomeMgr.cfg.GetBuildId(typeId)
            local buildData = GWG.GWHomeMgr.buildingData.GetMinLevelBuildingDataByBuildingID(buildId)

            if not buildData then
                self.OnCloseEvent()
                local gw_const = require "gw_const"
                local gw_jump_util = require "gw_jump_util"
                local buildTypeId = gw_const.enBuildingType.enBuildingType_Garden
                gw_jump_util.JumpToBuildBarUI(tostring(buildTypeId))
                flow_text.Add(lang.Get(607406))
            else
                local pos = {
                    x = buildData.x,
                    y = -1,
                    z = buildData.y+1
                }
                self.OnCloseEvent()
                GWAdmin.HomeCameraUtil.GWCameraDoMoveToGridPos(buildData.x, buildData.y, 1, true, function()
                    local gw_home_node = require "gw_home_node"
                    local common_tip_util = require "common_tip_util"
                    local parent = GWAdmin.SandSceneUtil.GetTransformByName(GWG.GWConst.ESandOrderKey.Move1)
                    log.Error(buildData.x,buildData.y,buildData.z)
                    common_tip_util.AddArrowTip(pos, 3, parent, nil, true)
                end)

            end
        end
        gw_common_util.SwitchToHome(func)
        
    end

    self.preChapterBtnEvent = function()
        local currentChapterIndex = self.currentChapterIndex
        if currentChapterIndex > 1 then
            local chapterCfg = self.currentChapterArr[currentChapterIndex-1]
            if chapterCfg and chapterCfg.ChaptersID then
                self.currentChapterIndex = currentChapterIndex-1
                self:ChangePage(chapterCfg.ChaptersID)
            end
        else

        end
    end

    --屏幕尺寸变化
    self.OnScreenSizeChanged = function(eventName, newSize, oldSize)
        util.DelayCallOnce(0.4, function()
            if self and self:IsValid() and self.ScrollRect and not util.IsObjNull(self.ScrollRect) then
                self.ScrollRect.normalizedPosition = {x=0,y=0}
            end
        end)
    end
    event.Register(event.SCREEN_SIZE_CHANGED, self.OnScreenSizeChanged)
end

function M:UnsubscribeEvents()

    if self.LoadRes then
        for k,res in pairs(self.LoadRes) do
            --  --print("<color=#FF8C00>卸载texture >>>>></color> ",k,res)
            IOSystem.UnloadAssetBundle(res, "ui_common_mini_game_level")
        end
        self.LoadRes = nil
    end

    event.Unregister(event.SCREEN_SIZE_CHANGED, self.OnScreenSizeChanged)
end

function M:UpdateUI()
    self.uiCanvas.sortingOrder = self.curOrder + 2
    self.SortingGroup.sortingOrder = self.curOrder + 3
    --self.minigameSelectRedDotCanvas.sortingOrder = self.curOrder + 4
end


-- 根据id加载texture
function M:LoadImage(res,imageObj,comCallback)
    self.MarkDispose = self.MarkDispose or {}
    if self.chRoot and self.chRoot._assetBundleName ~=res then
        -- table.insert(self.MarkDispose,self.chRoot)
        self.MarkDispose[self.chRoot] = 1
        self.chRoot = nil
    end
    if not self.chRoot then
        self.chRoot = BaseGameObject()
        local now = self.chRoot
        self.chRoot:LoadResource(res,nil, function(gameObject)
            if self.MarkDispose[now] then return end
            --  --print("Loaded",res)

            for v,s in pairs(self.MarkDispose) do
                v:Dispose()
            end
            self.MarkDispose = {}
            if gameObject and not gameObject:IsNull() then
                for i=1,gameObject.transform.childCount do
                    local chNode = gameObject.transform:GetChild(i-1)
                    if chNode.name == "jiemiPoint" or chNode.name=="bg" or chNode.name == "page1" or chNode.name == "page2" or chNode.name == "page3" then
                        chNode:SetActive(true)
                    else
                        chNode:SetActive(false)
                    end
                end
            end
            comCallback()
        end,true,self.BgContent)
    else
        comCallback()
    end
end

-- 根据id加载texture
function M:LoadImageLevel(level,res,imageObj)
    self.LoadRes = self.LoadRes or {}
    --  --print("<color=#00FF00>根据id加载texture >>>>></color> ",level,res)
    IOSystem.LoadAssetAsync(res, nil, function (ModelPrefab)
        if not self.UIRoot or self.UIRoot:IsNull() then
            IOSystem.UnloadAssetBundle(res, "ui_common_mini_game_level")
            return
        end

        self.LoadRes[level] = res
        imageObj.texture = ModelPrefab
        imageObj.enabled = true
        imageObj:SetNativeSize()
    end, "ui_common_mini_game_level")
end

--章节阶段奖励特效
function LoadCanSigninEffect(scroll_rect_item, parent, scale)
    if scroll_rect_item.data and scroll_rect_item.data.effect then
        scroll_rect_item.data.effect:Dispose()
        scroll_rect_item.data.effect = nil
    end
    --加载特效并播放
    local modelPath = "art/effects/effects/effect_ui_qiandaojiangli/prefabs/effect_ui_qiandaojiangli.prefab"
    if scroll_rect_item.data and scroll_rect_item.data.effect == nil then
        scroll_rect_item.data.effect = CModelViewer()
        scroll_rect_item.data.effect:Init(parent, function()
            scroll_rect_item.data.effect:ShowGameObject(modelPath, function(goEffect)
                goEffect:GetComponent(typeof(SortingGroup)).sortingOrder = window.uiCanvas.sortingOrder+1
                local childs = goEffect:GetComponentsInChildren(typeof(ParticleSystemRenderer))
                for i = 0, childs.Length - 1 do
                    -- childs[i].gameObject.transform.localScale = {x = scaleFactor, y = scaleFactor, z = scaleFactor} 
                    -- 特效与UI一致，不估分辨率适配
                    childs[i].maskInteraction = 1
                    util.SetTransformScale(childs[i].gameObject.transform, scale)
                end
            end)
        end)
    end
end

--卸载章节阶段奖励特效
function UnLoadEffect(scroll_rect_item)
    if scroll_rect_item.data and scroll_rect_item.data.effect then
        scroll_rect_item.data.effect:Dispose()
        scroll_rect_item.data.effect = nil
    end
end

--@region WindowBtnFunctions
--@endregion 

--@region ScrollItem
--@endregion 

--@region WindowInherited
local CUIPuzzleGameLevel = class(ui_base, nil, M)
--@endregion 

--@region ModuleFunction
function Show()
    if window == nil then
        window = CUIPuzzleGameLevel()
        window._NAME = _NAME
        window.delayOpenMain = 0
        window.delayCloseMain = 0
        window:LoadUIResource("ui/prefabs/gw_commonminigame/uicommonminigamelevel.prefab", nil, nil, nil,nil,true,nil,nil,true)
    else
        window:Show()
    end
    return window
end

function IsFullUI()
    return true
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end

function Close()
    -- log.Error("puzzle game close")

    if window ~= nil then
        Hide()
        window:Close()
        -- if window and not window.recycle_ui then
        --     window = nil
        -- end
        window = nil
    end
end

function OnSceneDestroy()
    curChapterId = 0
    lastGameType = 0
end
event.Register(event.SCENE_DESTROY, OnSceneDestroy)
event.Register(event.MINIGAME_SELECT_SWITCH, OnSceneDestroy)
event.Register(event.ACCOUNT_CHANGE_WORLD_RSP,OnSceneDestroy)

function OpenOnSceneReturn(onShow,onClose)
    local window = ui_window_mgr:ShowModule("ui_common_mini_game_level",onShow,onClose)
    window.sceneReturnFlag = true
end


function ClickPassLevel(  )
    if window and window:IsValid() then
        if curChapterId > 0 then
            window:UpdateTabList()
            window:ChangePage(curChapterId)
        end
    end
end
event.Register(event.CLICK_PASS_LEVEL, ClickPassLevel)

function SetScrollRect( )
    if window and window:IsValid() then
        window.ScrollRect.vertical = true
    end
end

--更新章节阶段奖励
function UpdateChapterPassReward(  )
    if window and window:IsValid() then
        if curChapterId > 0 then
            window:UpdatePassReward()
        end
    end
end
event.Register(event.CHAPTER_STAGE_REWARD_UPDATE,UpdateChapterPassReward)

--@endregion 
--是否启用独立小游戏资源
function IsUsingTinyStandAloneRes()
    StandAloneTinyMgr = StandAloneTinyMgr or require("tiny_stand_alone_mgr")
    return StandAloneTinyMgr.IsUsingStandAloneRes()
end

--以章节id为索引，获取章节数据
function M:GetTinyGameChapter(chapterid)
    return TinyStaticDataCenter.GetTinyGameChapter(chapterid)
end

--根据行数为索引，获取章节数据
function GetTinyGameRawChapter(lineIndex)
    if IsUsingTinyStandAloneRes() then
        return StandAloneTinyMgr.GetTinyGameRawChapter(lineIndex)
    else
        return game_scheme:MiniGameControl(lineIndex)
    end
end

--以关卡id为索引，获取关卡数据
function GetTinyGameLevelInfo(levelid)
    return puzzlegame_mgr.GetTinyGameLevelInfo(levelid)
end

--这里只需要考虑独立热更的
function M:GetTinyGameName()
    if IsUsingTinyStandAloneRes() then
        local tmpGameType = puzzlegame_mgr.getMiniGameType()
        if tmpGameType == puzzlegame_mgr.MiniGameType.None then
            return "益智游戏"
        else
            return StandAloneTinyMgr.GetEntryGameName(tmpGameType)
        end
    else
        return "益智游戏"
    end
end

function M:DefaultGameTypeChapterId()
    if lastGameType == 0 then
        local _gameList = gw_independent_game_mgr.GetSortGameList()
        for i, v in ipairs(_gameList) do
            local gameInfo = gw_independent_game_mgr.GetGameDataByType(v)
            if gameInfo and gameInfo.isOpen then
                lastGameType = gameInfo.TypeId
                break
            end
        end
    end
    if curChapterId == 0 then
        curChapterId = gw_independent_game_mgr.GetLatestChapterDataByType(lastGameType)
    end
end

function M:PlayAnimation()
    if self.tweenList then
        LeanTween.cancel(self.tweenList.uniqueId)
    end
    if self.tweenTimer then
        util.RemoveDelayCall(self.tweenTimer)
    end
    self.canvasGroup.alpha = 0
    self.tweenList = LeanTween.alphaCanvas(self.canvasGroup, 1, 0.5)

    --有点玄学，有时候会出现alpha一直为0，重新切页也不正常
    self.tweenTimer = util.DelayCallOnce(0.5,function ()
        if self and self:IsValid() and self.canvasGroup and not util.IsObjNull(self.canvasGroup) then
            self.canvasGroup.alpha = 1
        end

    end)
end

function SetLastChapter(chapter)
    curChapterId = chapter
end

function SetLastGameType(gameType)
    lastGameType = gameType
end
--@region RegisterMsg
MessageTable =
{ --///<<< tableStart
} --///<<< tableEnd
--@endregion 
