---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/Emmy<PERSON>ua)
--- Created by du<PERSON><PERSON>.
--- DateTime: 2025/4/1 16:58
---
local require = require
local string = string
local table = table
local ipairs = ipairs
local PlayerPrefs = CS.UnityEngine.PlayerPrefs
local os = os

local time_util = require "time_util"
local war_zone_duel_helper = require "war_zone_duel_helper"
local gw_event_activity_define = require "gw_event_activity_define"
local game_scheme = require "game_scheme"
local event_taskpart_define = require "event_taskpart_define"
local gw_task_const = require "gw_task_const"
local event_task_define = require "event_task_define"
local red_const = require "red_const"
local red_system = require "red_system"
local war_zone_duel_define = require "war_zone_duel_define"
local gw_sand_event_define = require "gw_sand_event_define"
local ui_window_mgr = require "ui_window_mgr"

local event = require "event"
local war_zone_duel_data = require "war_zone_duel_data"
local net_war_zone_duel_module = require "net_war_zone_duel_module"
local gw_common_util = require "gw_common_util"
local net_module_open = require "net_module_open"
local moduleOpenPro_pb = require "moduleOpenPro_pb"
local util = require "util"

---@class war_zone_duel_mgr
local M = {}
M.logger = war_zone_duel_data.logger

---@field 是否显示弹窗 boolean
M.IsShowPopup = true
---@field 第一次初始化数据 table
M.firstInitData = {
    infoReq = false,
    vsItemReq = false
}

--初始化
function M.Init()
    event.Register(gw_sand_event_define.GW_SAND_INIT_SELF_DATA_FINISH, M.AllDataReq)

    event.Register(war_zone_duel_define.Evt_MSG_ZONEBATTLEDUEL_GETINFO_RSP_Handler, M.AllInfo_RSP)
    event.Register(war_zone_duel_define.Evt_MSG_ZONEBATTLEDUEL_GET_VSINFO_RSP_Handler, M.VSItemInfo_RSP)

    event.Register(event_task_define.REFRESH_TASK, M.TaskPointUpdate) --任务刷新,红点注册
    event.Register(event_taskpart_define.TMSG_ACT_TASK_GETREWARD_RSP, M.WarZoneReceiveReward)
    event.Register(event_taskpart_define.TMSG_ACT_TASK_GETREWARD_RSP, M.WarZoneTaskRefresh)

    event.Register(event.USER_DATA_RESET, M.ClearData)
    event.Register(event.SERVER_CROSS_DAY, M.SERVER_CROSS_DAY_Handler) --跨天事件（如果是活动未开启状态，则再次尝试主动请求数据）
    event.Register(event.UPDATE_MODULE_OPEN, M.UPDATE_MODULE_OPEN_Handler)

    --region 红点注册
    red_system.RegisterRedFunc(red_const.Enum.WarZoneMain, M.GetWarZoneMainRed)
    red_system.RegisterRedFunc(red_const.Enum.WarZoneCapitol, M.GetWarZoneCapitolRed)
    red_system.RegisterRedFunc(red_const.Enum.WarZoneCapitolClickBtn, M.GetWarZoneAttackBtnRed)
    red_system.RegisterRedFunc(red_const.Enum.WarZoneRewardBtn, M.GetWarZoneRewardBtnRed)
    --endregion
end

---moduleOpen
function M.IsModuleOpen_ZoneDuel()
    local isOpen = net_module_open.CheckModuleOpen(moduleOpenPro_pb.emModuleID_ZoneBattleDuel) -- 战区对决模块ID
    --local isOpen = true -- 战区对决模块ID
    return isOpen
end

---functionOpen
function M.IsFunctionOpen_ZoneDuel()
    local function_open = require "function_open"
    local function_open_mgr = require "function_open_mgr"
    local isOpen = function_open.CheckFunctionIsOpen(function_open_mgr.OpenIdEnum.WarZoneDuel)
    return isOpen
end

-- 登录时请求所有数据，同时请求1v1数据（刷新活动阶段）
-- 每次打开活动入口请求1v1数据
-- 当活动阶段切换时，服务器推送NTF通知（刷新活动阶段）

--请求战区对决所有数据
function M.AllDataReq()
    if not M.IsFunctionOpen_ZoneDuel() or not M.IsModuleOpen_ZoneDuel() then
        return
    end
    M.GET_VSINFO_REQ()
    M.GETINFO_REQ()
end

function M.GETINFO_REQ()
    net_war_zone_duel_module.MSG_ZONEBATTLEDUEL_GETINFO_REQ()
end

function M.GET_VSINFO_REQ()
    net_war_zone_duel_module.MSG_ZONEBATTLEDUEL_GET_VSINFO_REQ()
end

---请求查看临时占领列表回复
function M.GET_DEFTEAMLIST_REQ(entitySid)
    local gw_sand_data = require "gw_sand_data"
    local sandBoxID = gw_sand_data.selfData.GetSandBoxSid()
    net_war_zone_duel_module.MSG_ZONEBATTLEDUEL_GET_DEFTEAMLIST_REQ(sandBoxID, entitySid)
end

---处理请求获取指定战区对决国会坐标点请求
function M.GET_CONGRESS_POS_REQ()
    local sandboxid = gw_common_util.GetSandZoneSandBoxSid()
    net_war_zone_duel_module.MSG_ZONEBATTLEDUEL_GET_CONGRESS_POS_REQ(sandboxid)
end

---请求相关worldid的总统信息
function M.GET_ZONEBATTLEDUEL_PRESIDENT_GET_INFO_REQ(data)
    net_war_zone_duel_module.MSG_ZONEBATTLEDUEL_PRESIDENT_GET_INFO_REQ(data)
end

--region 弹窗数据处理
function M.AllInfo_RSP()
    M.firstInitData.infoReq = true
    if M.IsShowPopup and M.firstInitData.infoReq and M.firstInitData.vsItemReq then
        M.WarZoneLoginPopup()
    end
    --region 这里请求一次总统
    local allWorlds = war_zone_duel_data.groups and war_zone_duel_data.groups.worlds or nil

    if allWorlds then
        local worldIDArr = {}
        for i, v in ipairs(allWorlds) do
            table.insert(worldIDArr, v.worldID)
        end
        M.GET_ZONEBATTLEDUEL_PRESIDENT_GET_INFO_REQ(worldIDArr)
    end
    --endregion
end

function M.VSItemInfo_RSP()
    M.firstInitData.vsItemReq = true
    if M.IsShowPopup and M.firstInitData.infoReq and M.firstInitData.vsItemReq then
        M.WarZoneLoginPopup()
    end
end

---@public 根据阶段不同弹出不同的拍脸图
function M.WarZoneLoginPopup()
    local activityID = war_zone_duel_data.GetActivityID()
    local festival_activity_mgr = require "festival_activity_mgr"
    local isActivityOpen = festival_activity_mgr.IsOpenActivityBase(activityID)
    if not isActivityOpen then
        return
    end

    local main_slg_tips_mgr = require "main_slg_tips_mgr"
    main_slg_tips_mgr.ShowTip(main_slg_tips_mgr.EnumTipType.WarZoneTip)

    local gw_popups_data = require "gw_popups_data"
    local windowName = ""
    M.stageWindowMap = {
        [war_zone_duel_define.enZoneBattleDuelStage.PreNotice] = "ui_war_zone_preview",
        [war_zone_duel_define.enZoneBattleDuelStage.AtyStart] = "ui_war_zone_race",
        [war_zone_duel_define.enZoneBattleDuelStage.CongressBattleStart] = "ui_war_zone_invade",
    }
    local isPreview = war_zone_duel_data.IsInStage(war_zone_duel_define.enZoneBattleDuelStage.PreNotice)
    if isPreview then
        windowName = M.stageWindowMap[war_zone_duel_define.enZoneBattleDuelStage.PreNotice]
    end
    local isAryStart = war_zone_duel_data.IsInStage(war_zone_duel_define.enZoneBattleDuelStage.AtyStart)
    if isAryStart then
        windowName = M.stageWindowMap[war_zone_duel_define.enZoneBattleDuelStage.AtyStart]
    end
    local isCongStart = war_zone_duel_data.IsInStage(war_zone_duel_define.enZoneBattleDuelStage.CongressBattleStart)
    if isCongStart then
        windowName = M.stageWindowMap[war_zone_duel_define.enZoneBattleDuelStage.CongressBattleStart]
    end

    --windowName = "ui_war_zone_preview"  --强制测试弹窗
    --log.Error("windowName：",windowName)

    M.IsShowPopup = false
    if string.IsNullOrEmpty(windowName) then

        return
    end

    gw_popups_data.AddMessage(windowName, function()
        local ui_window_mgr = require "ui_window_mgr"
        ui_window_mgr:ShowModule(windowName, nil, function()
            gw_popups_data.ShowNextMessage(0)
        end)
    end, gw_popups_data.PopupsType.Popups)


end
--endregion

--region 红点逻辑处理
function M.TaskPointUpdate(eventName, taskData, moduleId, moduleList)
    if moduleList[gw_task_const.TaskModuleType.WarZoneDuel] then
        red_system.TriggerRed(red_const.Enum.WarZoneCapitol)

        local festival_activity_mgr = require "festival_activity_mgr"
        local festival_activity_cfg = require "festival_activity_cfg"
        local gw_event_activity_define = require "gw_event_activity_define"
        local activityID = festival_activity_mgr.GetActivityIdByCodeType(festival_activity_cfg.ActivityCodeType.WarZoneDuel)
        event.Trigger(gw_event_activity_define.GW_ACTIVITY_RED_NEED_UPDATE, activityID)
    end
end

---@public 获取战区对决主界面红点
function M.GetWarZoneMainRed()
    local red = 0
    local capitolRed = M.GetWarZoneCapitolRed()
    red = red + capitolRed
    return red
end

---@public 获取战区对决国会页签红点
function M.GetWarZoneCapitolRed()
    local red = 0

    --领取按钮红点
    local rewardBtnRed = red_system.GetBuildFunRed(red_const.Enum.WarZoneRewardBtn)
    red = red + rewardBtnRed[1]

    --判断是否有能领取的宝箱
    local taskIDArr = war_zone_duel_data.WarZoneTaskIDArr
    local gw_task_data = require "gw_task_data"
    if taskIDArr then
        for _, taskCfg in ipairs(taskIDArr) do
            local taskData = gw_task_data.GetTaskData(taskCfg.taskID)
            if taskData and taskData.rate >= taskCfg.points and not taskData.status then
                red = red + 1
            end
        end
    end

    -- 判断是否国会争夺阶段,并且没有打开过活动
    local mainSlgRed = M.GetWarZoneAttackMainSlgBtnRed()
    if mainSlgRed and mainSlgRed > 0 then
        red = red + mainSlgRed
    end

    return red
end

--region 国会争夺按钮红点
local zoneDuel_day_congressMainSLGAtk = "zoneDuel_day_congressMainSLGAtk"
local zoneDuel_day_congressAtk = "zoneDuel_day_congressAtk"

local function getPlayerKey(key)
    local player_mgr = require "player_mgr"
    local roleId = player_mgr.GetPlayerRoleID()
    return string.format("%s_%d", key, roleId)
end

local function isCurDayTime(moduleKey)
    local key = getPlayerKey(moduleKey)
    if not PlayerPrefs.HasKey(key) then
        return false
    end
    local ts = PlayerPrefs.GetInt(key)
    if not ts or ts == 0 then
        return false
    end
    return time_util.GetIsCurDayTime(ts, os.server_zone())
end

-- 通用：记录当前服务器时间到 key（仅在需要的时候调用）
local function setDailyClicked(moduleKey)
    local key = getPlayerKey(moduleKey)
    PlayerPrefs.SetInt(key, os.server_time())
    PlayerPrefs.Save()
end

function M.GetWarZoneAttackMainSlgBtnRed()
    if not war_zone_duel_helper.IsCongressAtk() then
        return 0
    end

    return isCurDayTime(zoneDuel_day_congressMainSLGAtk) and 0 or 1
end

function M.SetWarZoneAttackMainSlgBtnRed()
    if war_zone_duel_helper.IsCongressAtk() then
        setDailyClicked(zoneDuel_day_congressMainSLGAtk)
    end
end

function M.GetWarZoneAttackBtnRed()
    if not war_zone_duel_helper.IsCongressAtk() then
        return 0
    end
    return isCurDayTime(zoneDuel_day_congressAtk) and 0 or 1
end

function M.SetWarZoneAttackBtnRed()
    if war_zone_duel_helper.IsCongressAtk() then
        setDailyClicked(zoneDuel_day_congressAtk)
        red_system.TriggerRed(red_const.Enum.WarZoneCapitolClickBtn)
    end
end
--endregion

---@public 获取战区对决领取连胜奖励红点
function M.GetWarZoneRewardBtnRed()
    local red = 0
    if war_zone_duel_data.bSkipReward == 0 then
        return red
    end
    local isCanReceive = war_zone_duel_data.IsInStage(war_zone_duel_define.enZoneBattleDuelStage.CongressBattleSettlement)
    local isRewardResult = war_zone_duel_data.IsInStage(war_zone_duel_define.enZoneBattleDuelStage.CongressBattleEnd)

    local showDownCfg = game_scheme:SrverShowdown_1(war_zone_duel_data.GetActivityID())
    local limitPoints = showDownCfg.CompetitivePointsConditions
    local pointsNum = war_zone_duel_data.GetSelfCapitalPoints()
    local isPointsReached = pointsNum >= limitPoints

    if isPointsReached and (isCanReceive or isRewardResult) then
        red = 1
    end
    return red
end

---@public 跨天事件（如果是活动未开启状态，则再次尝试主动请求数据）
function M.SERVER_CROSS_DAY_Handler()
    util.DelayCallOnce(60, function()
        M.firstInitData.infoReq = false
        M.firstInitData.vsItemReq = false
        --如果是活动未开启状态，则再次尝试主动请求数据
        if war_zone_duel_data.IsInStage(war_zone_duel_define.enZoneBattleDuelStage.None)
                or war_zone_duel_data.IsInStage(war_zone_duel_define.enZoneBattleDuelStage.AtyEnd)
                or war_zone_duel_data.IsInStage(war_zone_duel_define.enZoneBattleDuelStage.PreNotice) then
            M.AllDataReq()
        end
    end)
end

---实时监听moduleOpen关闭函数处理 
function M.UPDATE_MODULE_OPEN_Handler(eventName)
    --判断如果战区对决模块关闭，且缓存有战区对决数据，则重置阶段数据，触发入口刷新
    if not M.IsModuleOpen_ZoneDuel() and war_zone_duel_data.IsOverStage(war_zone_duel_define.enZoneBattleDuelStage.AtyStart) then
        war_zone_duel_data.ClearStageData()
        event.Trigger(gw_event_activity_define.GW_ACTIVITY_ENTRANCE_UPDATE)
    end
end

--endregion


function M.WarZoneReceiveReward(_, msg)
    if msg.actId == war_zone_duel_data.GetActivityID() then
        if msg.rewardIds then

            local rewardStr = table.concat(msg.rewardIds, "#")
            local reportMsg = {
                Position_Reward_Group = rewardStr
            }
            M.WarZoneEventReport("ServerShowdown_personal_progress_rewards", reportMsg)
        end
    end
end

--- 任务进度变化，获得积分时弹窗
function M.WarZoneTaskRefresh(_, msg)
    if msg.moduleId ~= gw_task_const.TaskModuleType.WarZoneDuel then
        return
    end
    local progress = 0
    -- 获取数据
    local showDownCfg = game_scheme:SrverShowdown_1(war_zone_duel_data.GetActivityID())
    local boxCount = showDownCfg.ProgressRewards.count
    local boxDataArr = showDownCfg.ProgressRewards.data

    local viewData = {}
    local gw_task_data = require "gw_task_data"
    -- 默认第一个任务节点
    local currProgressPoint = gw_task_data.GetTaskData(boxDataArr[0])
    for i = 0, boxCount - 1, 2 do
        local taskID = boxDataArr[i]
        local taskCfg = game_scheme:TaskMain_0(taskID)
        local points = taskCfg.ConditionValue1
        local rewardID = taskCfg.TaskReward
        local diamond = boxDataArr[i + 1]

        local tempData = {
            taskID = taskID,
            points = points,
            rewardID = rewardID,
            diamond = diamond
        }
        table.insert(viewData, tempData)
        -- 获取总进度
        local taskData = gw_task_data.GetTaskData(viewData[1].taskID)
        if taskData.rate ~= 0 then
            -- 更新进度
            progress = taskData.rate
        end
    end
    -- 根据viewData，获取当前的三个进度节点
    local currViewData = { viewData[1], viewData[2], viewData[3] }
    for i = 1, #viewData, 3 do
        local group = { viewData[i], viewData[i + 1], viewData[i + 2] }
        local minPoints = group[1].points
        local maxPoints = group[3].points
        if progress >= minPoints and progress <= maxPoints then
            currViewData = group
            break
        end
    end
    -- 判断当前任务节点
    for i = 0, boxCount - 1, 2 do
        local lastTaskData = gw_task_data.GetTaskData(boxDataArr[i - 2])
        local taskData = gw_task_data.GetTaskData(boxDataArr[i])
        if lastTaskData ~= nil and taskData ~= nil then
            -- 更新进度
            if lastTaskData.completeValue <= progress and taskData.completeValue >= progress then
                currProgressPoint = lastTaskData
            end
            break
        end
    end
    -- 打开弹窗
    local pointerSliderMsg = {
        startPos = currProgressPoint.completeValue,
        endPos = progress,
        sliderType = 1,
        pointType = 2,
        notShowStar = true,
        pos_1 = currViewData[1].points,
        pos_2 = currViewData[2].points,
        pos_3 = currViewData[3].points,
    }
    local temp = ui_window_mgr:GetWindowObj("ui_point_slider")
    if not temp then
        ui_window_mgr:ShowModule("ui_point_slider", nil, nil, pointerSliderMsg)
    else
        event.Trigger(event.SHOW_POINT_SLIDER, pointerSliderMsg)
    end
end

---@public 战区对决事件上报
function M.WarZoneEventReport(reportName, reportMsg)
    reportMsg = reportMsg or {}
    local alliance_mgr = require "alliance_mgr"
    reportMsg.LeagueAID = alliance_mgr.GetUserAllianceId()
    reportMsg.Act_ID = war_zone_duel_data.GetActivityID()
    reportMsg.NumberOfGroups = war_zone_duel_data.GetServerCross_ServerCount()
    event.EventReport(reportName, reportMsg)
end

---清除数据
function M.ClearData()
    if war_zone_duel_data then
        war_zone_duel_data.ClearData()
    end
    M.IsShowPopup = true
    M.firstInitData = {
        infoReq = false,
        vsItemReq = false
    }
end

return M