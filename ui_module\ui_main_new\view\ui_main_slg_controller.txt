--- ui_main_slg_controller.txt
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by .
--- DateTime: 
--- desc:    
---
local require = require
local pairs = pairs
local ipairs = ipairs
local table = table
local newclass = newclass

local war_zone_duel_define = require "war_zone_duel_define"
local util = require "util"
local gw_sand_event_define = require "gw_sand_event_define"
local gw_task_const = require "gw_task_const"
local cfg_util = require "cfg_util"
local os = require "os"
local event_task_define = require "event_task_define"
local gw_task_util = require "gw_task_util"
local gw_task_mgr = require "gw_task_mgr"
local sand_gather_data = require "sand_gather_data"
local data_personalInfo = require "data_personalInfo"
local custom_avatar_data = require "custom_avatar_data"
local gw_ed = require "gw_ed"
local event_personalInfo = require "event_personalInfo"
local sand_reinforce_data = require "sand_reinforce_data"
local gw_event_activity_define = require "gw_event_activity_define"
local festival_activity_mgr = require "festival_activity_mgr"
local event = require "event"
local controller_base = require "controller_base"
local player_mgr = require "player_mgr"
local game_scheme = require "game_scheme"
local windowMgr = require "ui_window_mgr"
local main_slg_data = require "main_slg_data"
local sand_ui_event_define = require "sand_ui_event_define"
local sand_ui_data = require "sand_ui_data"
local main_slg_common_define = require "main_slg_common_define"
local main_slg_const = require "main_slg_const"
local table_util = require "table_util"
local function_open_mgr = require "function_open_mgr"
local GWMgr = require "gw_mgr"
local GWConst = require "gw_const"
local ReviewingUtil = require "ReviewingUtil"
local screen_util = require "screen_util"
local type = type
local monsters_approaching_define = require "monsters_approaching_define"
local monsters_approaching_group_define = require "monsters_approaching_group_define"
local sceneType = GWConst.ESceneType

module("ui_main_slg_controller")
local controller = nil
local UIController = newclass("ui_main_slg_controller", controller_base)

--[[窗口初始化]]
function UIController:Init(view_name, controller_name, data)
    self.__base.Init(self, view_name, controller_name)

    --刷新配置的按钮
    local refreshGroups = main_slg_common_define.loginNeedUpdateGroupType
    for i, v in ipairs(refreshGroups) do
        self:FreshButtonList(v)
        --获取开放列表
        local OpenMap = main_slg_common_define.groupButtonTypeOpenMap[v]
        if OpenMap then
            local groupType = v
            local freshFunc = function()
                self:FreshButtonList(groupType)
            end
            for buttonId, openId in pairs(OpenMap or {}) do
                self:AddOpenFunction(openId, freshFunc)
            end
        end
    end

    self.cacheEntrances = table_util.Copy({}, main_slg_common_define.groupButtonType[main_slg_const.MainButtonGroupType.RightTop])
    self.cacheEntrances2 = table_util.Copy({}, main_slg_common_define.groupButtonType[main_slg_const.MainButtonGroupType.RightTop2])
    self.cacheEntrances3 = table_util.Copy({}, main_slg_common_define.groupButtonType[main_slg_const.MainButtonGroupType.LeftBottom2])
    self.cacheEntrances4 = table_util.Copy({}, main_slg_common_define.groupButtonType[main_slg_const.MainButtonGroupType.LeftBottom4])
    self.cacheEntrances5 = table_util.Copy({}, main_slg_common_define.groupButtonType[main_slg_const.MainButtonGroupType.RightTop3])

    self.OnUpdateActivityEntrances()
    --终止引导
    self:StopTaskWeakGuide()
    --刷新vip
    self:InitVIPShow()
    --刷新体力
    self:SetStaminaData()
    --刷新头像
    self:SetPlayerFaceData()
    --刷新增援按钮显示
    self:SetReinforceBtn()
    --刷新集结按钮显示
    self:SetUnionWarBtn()
    --刷新任务显示
    self:SetTaskData()
    --刷新任务弱强制引导
    self:RefreshTaskWeakGuide()
    --刷新攻城号角按钮显示
    self:SetWarHornData()
    --刷新简化模式显示
    self:SetSimpleModelState()
    --绑定数据判定下发
    self:OnReqBindRewardInMain()
    --刷新战区对决按钮显示
    self:SetZoneDuelData()

    --刷新右上第2组按钮
    --self:FreshButtonList(main_slg_const.MainButtonGroupType.RightTop2)
    --刷新下载按钮显示
    self:OnRefreshDownload()
end

function UIController:OnReqBindRewardInMain()
    local q1sdk = require "q1sdk"
    local isBind = q1sdk.GetIsExitBindState()
    if isBind then
        local activity_mgr = require "activity_mgr"
        local rewardStatus = activity_mgr.OnGetTopicBindRewardStatus()
        if not rewardStatus then
          --已绑定未获奖
          local net_activity_module = require "net_activity_module"
          net_activity_module.SetBindStateReward(false)
          net_activity_module.MSG_GETBINDEMAIL_REWARD_REQ()
        end
    end
    
end

--[[界面被显示的时候调用]]
function UIController:OnShow()
    --警告 OnShow添加业务需求；因为主界面会频繁的显示隐藏，从而触发onshow，造成卡顿掉帧 1，比如频繁触发全屏  2，沙盘，城建等会频繁的触发掉帧
    self.__base.OnShow(self)
    local main_slg_mgr = require "main_slg_mgr"
    if main_slg_mgr.GetUIMainSlgShowType() ~= 0 then
        return
    end
    --刷新集结按钮显示
    self:SetUnionWarBtn()
    --刷新增援按钮显示
    self:SetReinforceBtn()
    --刷新攻城号角按钮显示
    self:SetWarHornData()
    --刷新简化模式显示
    self:SetSimpleModelState()
    --刷新战区对决按钮显示
    self:SetZoneDuelData()

    event.Trigger(event.OPEN_MAIN_SLG_VIEW)
    -- 在onshow中禁止频繁刷新，  1，这种业务刷新本来就不应该依赖ui的生命周期 ，如果要刷新，需自己的模块事件来管理
    --self:FreshButtonList(main_slg_const.MainButtonGroupType.RightTop)
    --self:FreshButtonList(main_slg_const.MainButtonGroupType.RightTop2)
    --self:FreshButtonList(main_slg_const.MainButtonGroupType.RightTop3)
    --self:FreshButtonList(main_slg_const.MainButtonGroupType.LeftBottom2)
    --self:FreshButtonList(main_slg_const.MainButtonGroupType.LeftBottom4)
    --self:FreshButtonList(main_slg_const.MainButtonGroupType.LeftBottom)
    self:InitBomberMan()
    self:UpdateGatherPop()
    self:InitMonsterAttack()
    self:OnRefreshMonsterUI()
    self:OnRefreshMainButtons()
    self:OnRefreshECS()--初始隐藏vip客服按钮
end

function UIController:OnRefreshMainButtons()
    self:FreshButtonList(main_slg_const.MainButtonGroupType.LeftBottom)
end


function UIController:onGrayLoadClickProxy()
    --self:TriggerUIEvent("OnGrayLoadBtnClick")
end

function UIController:OnBtn_PowerShowClickedProxy()
    windowMgr:ShowModule("ui_power_recovery_show")
end
function UIController:OnBtn_VipShowBtnClickedProxy()
    windowMgr:ShowModule("ui_vip_main_new")
    event.EventReport("Vip_Enter", {})
end
function UIController:OnBtn_VipCustomerBtnClickedProxy()
    windowMgr:ShowModule("ui_exclusive_customer_service")
end

-- function UIController:OnBtn_MonsterApproClickedProxy()
--     local monsters_approaching_mgr = require "monsters_approaching_mgr"
--     monsters_approaching_mgr.OpenMonstersUI()
-- end

function UIController:OnBtn_GatherBtnClickedProxy()
    local AllianceMgr = require "alliance_mgr"
    AllianceMgr.OpenAllianceWarUI()
end

function UIController:OnBtn_InviteBtnClickedProxy()
    self:MassShare()
end

function UIController:MassShare()
    local share_util = require "share_util"
    local lastOneGatherData = sand_gather_data.GetLastOneGatherData()
    if lastOneGatherData then
        local alliance_mgr = require "alliance_mgr"
        local LeaderData = alliance_mgr.GetAllianceRoleInfo(lastOneGatherData.massLeaderId)
        local shareData = {
            massTeamId = lastOneGatherData.massTeamId,
            massFinishTime = lastOneGatherData.massFinishTime,
            targetId = lastOneGatherData.targetId,
            pos = lastOneGatherData.targetPos,
            strName = LeaderData.strName,
            targetSId = lastOneGatherData.targetSId
        }
        share_util.OpenMassShare(shareData)
    end
    self:TriggerUIEvent("UpdateGatherPop", false)
end

--刷新集结气泡
function UIController:UpdateGatherPop()
    local lastOneGatherData = sand_gather_data.GetLastOneGatherData()
    self:TriggerUIEvent("UpdateGatherPop", lastOneGatherData ~= nil)
    -- 创建定时器
    if self.gatherPopTimer then
        util.RemoveDelayCall(self.gatherPopTimer)
        self.gatherPopTimer = nil
    end
    if not self.gatherPopDelayCloseTime then
        local cfg = game_scheme:GWMapConstant_0(134)
        self.gatherPopDelayCloseTime = cfg and cfg.szParam.data[0] or 10
    end
    self.gatherPopTimer = util.DelayCallOnce(self.gatherPopDelayCloseTime, function()
        self:TriggerUIEvent("UpdateGatherPop", false)
        sand_gather_data.ClearLastOneGatherData()
    end)
end

--任务按钮
function UIController:OnBtnTaskClicked()
    local force_guide_system = require "force_guide_system"
    local stepId = force_guide_system.GetCurStep()
    if stepId and stepId == 889 then
        return
    end
    windowMgr:ShowModule("ui_gw_general_task")
end

function UIController:OnBtnTaskContentClicked()
    if self.taskData then
        if gw_task_mgr.GetTaskIsReceive(self.taskData.taskId) then
            gw_task_mgr.SetIsClickTaskTracking(true)
            gw_task_mgr.ReceiveTaskReward(self.taskData.taskId, 0, false)
            gw_task_mgr.ReceiveTaskReport(self.taskData.taskId)
        else
            gw_task_util.TaskJumpFunc(self.taskData.taskId)
            --点击任务追踪（不包含领奖） 打点
            local reportMsg = {
                MissionID = self.taskData.taskId
            }
            event.EventReport("Mission_Click", reportMsg)
        end
    end
    local force_guide_system = require "force_guide_system"
    local stepId = force_guide_system.GetCurStep()
    if stepId and stepId == 889 then
        local force_guide_event = require "force_guide_event"
        force_guide_system.TriComEvent(force_guide_event.cEventMainTask)

        local gw_ab_test_mgr = require "gw_ab_test_mgr"
        local abTestId = gw_ab_test_mgr.GetPackageABTestId()
        if abTestId == gw_ab_test_mgr.ABTestDefaultId then
            return
        end
        local unforced_guide_mgr = require "unforced_guide_mgr"
        if unforced_guide_mgr.NewUnlock(9) then
            local unforced_guide_event_define = require "unforced_guide_event_define"
            event.Trigger(unforced_guide_event_define.start_miniGame_main_view)
        end
    end
end

function UIController:OnBtn_ReinforceBtnClickedProxy()
    local sand_warning_mgr = require "sand_warning_mgr"
    local isShowWarning, warningInfo = sand_warning_mgr.GetShortTime()
    if isShowWarning ~= 0 then
        windowMgr:ShowModule("ui_sand_warning_tip")
    else
        windowMgr:ShowModule("ui_sand_reinforce")
    end

end

function UIController:OnAuto_ChessBtnClickedProxy()
    windowMgr:ShowModule("ui_desert_storm_reward_panel")
    local gw_storm_mgr = require "gw_storm_mgr"
    local sendData =
    {
        point = gw_storm_mgr.GetSelfScore()
    }
    gw_storm_mgr.ReportEvent("DesertStorm_ViewRewarDesertStorm",sendData)
end

-- 攻城号角事件
function UIController:OnBtn_WarHornsBtnClickedProxy()
    if self.warHornsData then
        local function jump_to_city()
            local regionCfg = game_scheme:SandMapRegion_0(self.warHornsData.data.nRegionID)
            if regionCfg then
                local cityPos = cfg_util.StringToNumberArray(regionCfg.CityPos)
                local gw_common_util = require "gw_common_util"
                local gw_sand_data = require "gw_sand_data"
                gw_common_util.SwitchToSand(nil, { x = cityPos[1], y = cityPos[2] }, gw_sand_data.selfData.GetZoneSandBoxSid())
                --点击攻城号角 打点
                event.EventReport("SandMapCity_Horn", {})
            end
        end
        if os.server_time() < self.warHornsData.data.nStartTime then
            local city_siege_activity_data = require "city_siege_activity_data"
            local temp = city_siege_activity_data.GetCityOpenTime(self.warHornsData.data.nRegionID)
            if temp > 0 then
                windowMgr:ShowModule("ui_city_siege_popup",nil,nil,{
                    openTime = temp
                })
            else
                jump_to_city()
            end
        else
            jump_to_city()
        end
    else

    end
end

-- 战区对决按钮事件
function UIController:OnBtn_DuelZoneBtnClickedProxy()
    local gw_common_util = require "gw_common_util"
    local war_zone_duel_data = require "war_zone_duel_data"
    local gw_sand_external_mgr = require "gw_sand_external_mgr"
    
    local defWorldID = war_zone_duel_data.GetSelfZoneDuelDefWorldID()
    if defWorldID == gw_common_util.GetSandRoleSandBoxSid() and
            gw_sand_external_mgr.IsInPolluteZoneBuff1(gw_common_util.GetSandBasePosition()) then
        local gw_sand_cfg = require "gw_sand_cfg"
        local congressCfg = gw_sand_cfg.GetSandCongressRegionCfg()
        if congressCfg then
            local cityPos = cfg_util.StringToNumberArray(congressCfg.CityPos)
            gw_common_util.SwitchToSand(nil, { x = cityPos[1], y = cityPos[2] }, defWorldID)
        end
    else
        event.RegisterOnce(war_zone_duel_define.Evt_MSG_ZONEBATTLEDUEL_GET_CONGRESS_POS_RSP_Handler, self.DuelZoneCongressPosCallback)
        
        local war_zone_duel_mgr = require "war_zone_duel_mgr"
        war_zone_duel_mgr.GET_CONGRESS_POS_REQ()
    end
end

function UIController:Close(data)
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
        --注意；当前改变了原定义的集合；因此如果重登需要重置回去
        main_slg_common_define.groupButtonType[main_slg_const.MainButtonGroupType.RightTop] = table_util.Copy({}, self.cacheEntrances)
        main_slg_common_define.groupButtonType[main_slg_const.MainButtonGroupType.RightTop2] = table_util.Copy({}, self.cacheEntrances2)
        main_slg_common_define.groupButtonType[main_slg_const.MainButtonGroupType.LeftBottom2] = table_util.Copy({}, self.cacheEntrances3)
        main_slg_common_define.groupButtonType[main_slg_const.MainButtonGroupType.LeftBottom4] = table_util.Copy({}, self.cacheEntrances4)
        main_slg_common_define.groupButtonType[main_slg_const.MainButtonGroupType.RightTop3] = table_util.Copy({}, self.cacheEntrances5)
        self.cacheEntrances = nil
        self.cacheEntrances2 = nil
        self.cacheEntrances3 = nil
        self.cacheEntrances4 = nil
        self.cacheEntrances5 = nil
        if self.ShowDialogueTime then
            util.RemoveDelayCall(self.ShowDialogueTime)
            self.ShowDialogueTime = nil
        end

        if self.gatherPopTimer then
            util.RemoveDelayCall(self.gatherPopTimer)
            self.gatherPopTimer = nil
        end

        self:RemoveHornTimer()
        self:RemoveDuelZoneTimer()

        local net_activity_module = require "net_activity_module"
        net_activity_module.SetBindStateReward(false)
    end
end

--会基类自动调用
function UIController:AutoSubscribeEvents()
    -- 注意 事件建议使用self:RegisterEvent(event_name,func)来注册，不需要手动维护注销了 
    local RefreshButtonList = function(_, groupType)
        self:FreshButtonList(groupType)
    end
    self:RegisterEvent(sand_ui_event_define.GW_MAIN_BUTTONS_REFRESH, RefreshButtonList)

    local RefreshPlayerIcon = function()
        self:SetPlayerFaceData()
    end

    self:RegisterEvent(event.FIRST_LOGIN_CREATE_DATA_FINISH, RefreshPlayerIcon)
    -- 大本被修复统一由个人信息处理
    self:RegisterEvent(event_personalInfo.UPDATE_BASE_INFO, RefreshPlayerIcon)
    self:RegisterEvent(event_personalInfo.UPDATE_PERSONALISED_INFO, RefreshPlayerIcon)
    self:RegisterEvent(event_personalInfo.CUSTOM_AVATAR_USE, RefreshPlayerIcon)
    local OnSceneChange = function()
        self:InitVIPShow()
        self:StopTaskWeakGuide()
        self:RefreshTaskWeakGuide()
        self:OnRefreshMainButtons()
    end
    self:RegisterEvent(sand_ui_event_define.GW_SAND_SCENE_CHANGE, OnSceneChange)
    --刷新增援按钮显示
    local RefreshReinforceBtn = function()
        self:SetReinforceBtn()
    end
    self:RegisterEvent(sand_ui_event_define.GW_SAND_REINFORCE_DATA_CHANGE, RefreshReinforceBtn)
    self:RegisterEvent(sand_ui_event_define.GW_SAND_UPDATE_WARNING_DATA, RefreshReinforceBtn)

    --刷新体力显示
    local RefreshStaminaData = function()
        self:SetStaminaData()
    end
    self:RegisterEvent(sand_ui_event_define.GW_STAMINA_CHANGE, RefreshStaminaData)
    self:RegisterEvent(sand_ui_event_define.GW_STAMINA_BASE_RECOVER_CHANGE, RefreshStaminaData)

    local DoDonateAnim = function(eventName, data)
        self:TriggerUIEvent("DoDonateAnim", data)
    end
    self:RegisterEvent(event.TASK_TRACKING_ITEM_ANIM, DoDonateAnim)

    --刷新主界面集结按钮
    self.updateMainMassBtnState = function()
        self:SetUnionWarBtn()
    end
    self:RegisterEvent(sand_ui_event_define.GW_SAND_MAIN_BUTTON_DATA_CHANGE, self.updateMainMassBtnState)

    --[[    self.SandAlertNtf = self.SandAlertNtf or function(_, bool)
            --GWG.GWAdmin.SwitchUtility.SandWarn("SandAlertNtf", bool)
            self:TriggerUIEvent("OnSandAlertNtf", bool)
        end
        self:RegisterEvent(gw_ed.GW_SAND_ALERT_NTF, self.SandAlertNtf, bool)]]

    self.RefreshMainTask = self.RefreshMainTask or function(_, __,moduleId, moduleList)

        if not moduleList or type(var) ~= "table" or moduleList[gw_task_const.MainUITaskModuleType.Mainline] or moduleList[gw_task_const.MainUITaskModuleType.SideQuest] or
                moduleList[gw_task_const.MainUITaskModuleType.Chapter] or moduleList[gw_task_const.MainUITaskModuleType.Daily] then
            local red_system = require "red_system"
            local red_const = require "red_const"
            red_system.TriggerRed(red_const.Enum.TaskMain)
            self:SetTaskData()
        end
    end
    self:RegisterEvent(event_task_define.REFRESH_TASK, self.RefreshMainTask)

    self.OnUpdateActivityEntrances = self.OnUpdateActivityEntrances or function()
        if not self.cacheEntrances then
            return
        end
        local entrances, entrances2 = festival_activity_mgr.GetCurFestivalActivityEntrances(true)
        entrances.type = main_slg_const.MainButtonGroupType.RightTop
        entrances.cacheEntrances = self.cacheEntrances
        entrances2.type = main_slg_const.MainButtonGroupType.RightTop2
        entrances2.cacheEntrances = self.cacheEntrances2
        local entrance3 = {}
        entrance3.type = main_slg_const.MainButtonGroupType.LeftBottom2
        entrance3.cacheEntrances = self.cacheEntrances3
        local count3 = 0
        local festival_activity_cfg = require "festival_activity_cfg"
        for i, v in pairs(festival_activity_cfg.ActivityBlackList) do
            count3 = count3 + 1
            table.insert(entrance3, v)
        end

        local entrance4 = {}
        entrance4.type = main_slg_const.MainButtonGroupType.LeftBottom4
        entrance4.cacheEntrances = self.cacheEntrances4
		-- 右侧第三列，问卷、下载进度、换包好礼，接入functionBar进行排序
        local entrance5 = {{entranceID = festival_activity_cfg.ActivityEntranceType.ResourceDownload}, {entranceID = festival_activity_cfg.ActivityEntranceType.Questionnaire}, {entranceID = festival_activity_cfg.ActivityEntranceType.ChangePackage}}
        entrance5.type = main_slg_const.MainButtonGroupType.RightTop3
        entrance5.cacheEntrances = self.cacheEntrances5
--[[        local count4 = 0
        for i, v in pairs(festival_activity_cfg.ActivityBlackList) do
            count4 = count4 + 1
            table.insert(entrance4, v)
        end]]
        

        local entranceTab = { entrances, entrances2, entrance3, entrance4, entrance5 }
        for key, _value in ipairs(entranceTab) do
            --先进行排序
            if key < 3 then
                table.sort(_value, function(a, b)
                    if not a.seqencing or not b.seqencing then
                        return false
                    end
                    if a.seqencing == b.seqencing then
                        return a.entranceID > b.entranceID
                    end
                    return a.seqencing > b.seqencing
                end
                )
            end
            if key == 5 then
                for index, value in ipairs(_value) do
                    local cfg = game_scheme:FunctionBar_0(value.entranceID)
                    if cfg then
                        value.seqencing = cfg.Seqencing
                    end
                end
                table.sort(_value, function(a, b)
                    if not a.seqencing or not b.seqencing then
                        return false
                    end
                    if a.seqencing == b.seqencing then
                        return a.entranceID > b.entranceID
                    end
                    return a.seqencing > b.seqencing
                end
                )
            end

            local newEntrances = {}
            for i, v in ipairs(_value.cacheEntrances) do
                table.insert(newEntrances, v)
            end
            --备注，新增的活动相关的GroupButtonTypeData 的结构 {entranceID,activityID,isQuickEntrance}
            for i, v in ipairs(_value) do
                if type(v) ~= 'number' then
                    local buttonType = v.isQuickEntrance and v.entranceID + 1000000 or v.entranceID
                    table.insert(newEntrances, buttonType)
                else
                    table.insert(newEntrances, v)
                end
            end
            local type = _value.type
            main_slg_data.SetGroupButtonTypeData(type, newEntrances)
            self:FreshButtonList(type)
        end
    end
    self:RegisterEvent(gw_event_activity_define.GW_ACTIVITY_ENTRANCE_UPDATE, self.OnUpdateActivityEntrances)
    self:RegisterEvent(event.GW_SCENE_CHANGE_SUCCESS,self.OnUpdateActivityEntrances)
    self:RegisterEvent(event.FIRST_LOGIN_CREATE_DATA_FINISH, self.OnUpdateActivityEntrances)
    self:RegisterEvent(gw_event_activity_define.GW_LOGIN_GIFT_LOCK_STATE_UPDATE, self.OnUpdateActivityEntrances)
    --首充
    self:RegisterEvent(gw_event_activity_define.GW_FIRST_CHARGE_DATA_UPDATE, self.OnUpdateActivityEntrances)
    --限时礼包
    self:RegisterEvent(gw_event_activity_define.GW_LIMIT_GIFT_ICON_UPDATE, self.OnUpdateActivityEntrances)

    --怪物攻城事件 start
    -- self.RefreshMonsterAttackTalk = function(eventName, stage, desId)
    --     self:SetMonsterAttackTalk(stage, desId)
    -- end
    -- --刷新怪物攻城对话
    -- self:RegisterEvent(monsters_approaching_define.MONSTER_ATTACK_POP_TALK, self.RefreshMonsterAttackTalk)

    self.UnlockMonsterAttack = function()
        self:InitMonsterAttack()
    end
    --解锁怪物攻城时刷新入口
    self:RegisterEvent(monsters_approaching_define.UNLOCK_MONSTER_ATTACK, self.UnlockMonsterAttack)
    --解锁怪物攻城时刷新入口
    self:RegisterEvent(monsters_approaching_group_define.UNLOCK_MONSTER_ATTACK_GROUP, self.UnlockMonsterAttack)

    -- self.MonsterAttackSlider = function()
    --     self:UpdateMonsterAttackSlider()
    -- end
    -- --领取奖励时刷新进度条
    -- self:RegisterEvent(monsters_approaching_define.MONSTER_ATTACK_REWARD, self.MonsterAttackSlider)
    --怪物攻城事件 end

    self.BomberManFresh = function()
        self:UpdateBomberMan()
    end
    --服务器数据变化
    self:RegisterEvent(event.BOMBERMAN_ENTER_FRESH, self.BomberManFresh)
    --切换场景变化
    self:RegisterEvent(event.GW_SCENE_CHANGE_SUCCESS, self.BomberManFresh)

    self.refreshVIP = function()
        self:InitVIPShow()
    end
    self:RegisterEvent(event.GW_VIP_LEVEL_UP, self.refreshVIP)
    self:RegisterEvent(event.GW_VIP_TIME_CHANGE, self.refreshVIP)

    --开放条件
    --vip
    self:AddOpenFunction(function_open_mgr.OpenIdEnum.VIP, self.refreshVIP)
    --任务
    self:AddOpenFunction(function_open_mgr.OpenIdEnum.ChapterTasks, self.RefreshMainTask)

    self.UpdatePlayerInfo = function()
        self:SetPlayerInfoOpen()
    end
    self:AddOpenFunction(function_open_mgr.OpenIdEnum.PersonalInformation, self.UpdatePlayerInfo, true)
    self.OnInputEvent = function(_, action, p1, p2, p3, p4)
        if action == gw_ed.Input.EventBegin then
            self:RefreshTaskWeakGuide()
        end
    end
    gw_ed.mgr:Register(gw_ed.GW_INPUT_EVENT, self.OnInputEvent)
    self.onHomeMainUpgrade = function()
        self:RefreshTaskWeakGuide()
    end
    gw_ed.mgr:Register(gw_ed.GW_HOME_MAIN_UPGRADE, self.onHomeMainUpgrade)
    self:RegisterEvent(event.FORCE_GUIDE_EXIT, self.onHomeMainUpgrade)
    self:RegisterEvent(event.TASK_REFRESH_UNFORCED_GUIDE, self.onHomeMainUpgrade)
    self.OnStopTaskWeakGuide = function()
        self:StopTaskWeakGuide()
    end
    self:RegisterEvent(event.TASK_UNFORCED_GUIDE, self.OnStopTaskWeakGuide)

    -- 攻城号角
    self.warHornsDataChange = function(eventName, data)
        self:SetWarHornData()
    end
    self:RegisterEvent(sand_ui_event_define.GW_SAND_CITY_WAR_HORNS_POP_CHANGE, self.warHornsDataChange)
    self:RegisterEvent(sand_ui_event_define.GW_SAND_CITY_ALL_POPS_CLEAR, self.warHornsDataChange)

    -- 简化模式
    self.sandSimpleLevelChange = function()
        self:SetSimpleModelState()
    end
    self:RegisterEvent(gw_sand_event_define.GW_SAND_SIMPLE_LEVEL_CHANGED, self.sandSimpleLevelChange)

    self.languageChange = function()
        self:SetTaskData()
        -- self:UpdateMonsterAttack()
    end
    self:RegisterEvent(event.LANGUAGE_SETTING_CHANGED, self.languageChange)

    self.updateTeamOperate = function()
        self:UpdateGatherPop()
    end
    self:RegisterEvent(sand_ui_event_define.GW_GATHER_TEAM_OPERATE_RSP, self.updateTeamOperate)
    
    self.UpdateCarriageBubble = function()
        self:TriggerUIEvent("CheckShowTrucksQiPao")
    end
    local event_carriage_define = require("event_carriage_define")
    self:RegisterEvent(event_carriage_define.TMSG_CARRIAGE_CNETER_DATA_NTF, self.UpdateCarriageBubble)

    self.ShowOrHideDesertStormUI = function(_,value)
        if value ~= sceneType.Storm then
            self:TriggerUIEvent("SetDesertStormChess",false)
        else
            --打开沙漠风暴主界面
            local rate = self:GetDesertStormChessRateData()
            self:TriggerUIEvent("SetDesertStormChess",true,rate)
        end
    end
    self:RegisterEvent(event.GW_SCENE_CHANGE_SUCCESS, self.ShowOrHideDesertStormUI)
    self.UpdateDesertStormChessRate = function()
        local rate = self:GetDesertStormChessRateData()
        self:TriggerUIEvent("SetDesertStormChessRate",rate)
    end
    local event_DesertStrom_define = require "event_DesertStrom_define"
    self:RegisterEvent(event_DesertStrom_define.EVENT_STORM_SELF_SCORE_UPDATE, self.UpdateDesertStormChessRate)
    gw_ed.mgr:Register(gw_ed.GW_HOME_EVENT_UPDATE, self.BomberManFresh)
    
    -- 屏幕尺寸变化监听
    self.OnUPDATE_SCREEN_SIZE = function(event, new, old)
        self:OnRefreshMonsterUI()
	end
    self:RegisterEvent(event.SCREEN_SIZE_CHANGED, self.OnUPDATE_SCREEN_SIZE)

    --专属客服按钮
    self.RefreshECS = function ()
        self:OnRefreshECS()
    end
    self:RegisterEvent(event.EVENT_ECS_REFRESHECS, self.RefreshECS)
    --专属客服vip弹窗
    self.ShowVIPCustomer = function ()
        local ecs = require "ui_exclusive_customer_service_data"
        ecs.ShowExclusiveCustomerService()
    end
    self:RegisterEvent(event.LOGIN_UI_POPUP_END, self.ShowVIPCustomer)

    --战区对决按钮刷新
    self.RefreshDuelZoneBtnState = function()
        self:SetZoneDuelData()
    end
    self:RegisterEvent(war_zone_duel_define.Evt_MSG_ZONEBATTLEDUEL_GET_VSINFO_RSP_Handler, self.RefreshDuelZoneBtnState)
    self:RegisterEvent(war_zone_duel_define.Evt_ALLIANCE_BATTLE_DUEL_CHANGE_NTF_Handler, self.RefreshDuelZoneBtnState)
    
    self.RefreshDuelZoneData = function(_, msg)
        self:OnZoneDuelData(msg)
    end
    self:RegisterEvent(war_zone_duel_define.Evt_MSG_ZONEBATTLEDUEL_GET_COUNTRY_SCORE_RSP_Handler, self.RefreshDuelZoneData)

    self.DuelZoneCongressPosCallback = function (_,msg)
        if msg.errCode ~= 0 then
            return
        end

        local gw_common_util = require "gw_common_util"
        local war_zone_duel_data = require "war_zone_duel_data"
        local atkWorldID = war_zone_duel_data.GetSelfZoneDuelAtkWorldID()
        local selfWorldID = gw_common_util.GetSandZoneSandBoxSid()
        local pos = { x = msg.x, y = msg.y }
        local sandbox_pb = require "sandbox_pb"
        gw_common_util.SwitchToSand(function()
            if atkWorldID == selfWorldID  then
                local gw_admin = require "gw_admin"
                gw_admin.SandRelocationEntityUtil.SetRelocationEntitySid(0, { x = pos.x, y = 0, z = pos.y }, function(state, gridPos)
                    if not state then
                        gw_common_util.SwitchToSand()
                    end
                end, sandbox_pb.enSandboxCrossMoveType_ZoneDuel_Battle)
            else
                local sand_operate_command = require "sand_operate_command"
                sand_operate_command.ClickNoneEvent(pos)
            end
        end, pos, msg.sandboxSid, true)
    end
end

-- 屏幕尺寸变化时根据分辨率改变尺寸大小
function  UIController:OnRefreshMonsterUI()
    local new = screen_util.GetScreenAspect()
    local isSmall = new >= 720/1280
    self:TriggerUIEvent("OnRefreshMonsterUI", isSmall)
end
function UIController:GetDesertStormChessRateData()
    local gw_storm_mgr = require "gw_storm_mgr"
    local selfScore = gw_storm_mgr.GetSelfScore()
    local targetScore = gw_storm_mgr.OnGetSelfMaxScoreGroup()
    if targetScore == 0 then
        targetScore = -1
    end
    local rate = 0
    if targetScore ~= 0 then
        if targetScore > 0 then
            rate = selfScore / targetScore
        else
            rate = 1
        end
    else
        --目标积分为0，一般是出错了
        local log = require "log"
        log.Warning("当前阶段的目标积分为0，需要检查逻辑。")
    end
    return rate < 1 and rate or 1
end

--会基类自动调用
function UIController:AutoUnsubscribeEvents()
    gw_ed.mgr:Unregister(gw_ed.GW_HOME_MAIN_REPAIR, self.OnHomeMainRepair)
    gw_ed.mgr:Unregister(gw_ed.GW_HOME_MAIN_UPGRADE, self.onHomeMainUpgrade)
    gw_ed.mgr:Unregister(gw_ed.GW_INPUT_EVENT, self.OnInputEvent)
    gw_ed.mgr:Unregister(gw_ed.GW_HOME_EVENT_UPDATE, self.BomberManOpen)
    self.OnHomeMainRepair = nil
end
---********************功能函数区**********---
function UIController:FreshButtonList(type)
    local dataList = main_slg_data.GetButtonDataListByGroupType(type)
    if dataList then
        self:TriggerUIEvent("SetBtnListByData", type, dataList)
        for _, buttonType in ipairs(dataList) do
            local configData = main_slg_data.GetButtonConfigData(buttonType.buttonType)--main_slg_common_define.buttonConfig[buttonType.buttonType]
            if configData and configData.atyBubbleData and #configData.atyBubbleData > 0 then
                local main_slg_tips_mgr = require "main_slg_tips_mgr"
                main_slg_tips_mgr.CheckActivityBubble()
            end
        end
    end
end

--设置个人信息开放
function UIController:SetPlayerInfoOpen()
    local isOpen = function_open_mgr.CheckFunctionIsOpen(function_open_mgr.OpenIdEnum.PersonalInformation)
    self:TriggerUIEvent("SetPlayerInfoOpen", isOpen)
end

--[初始化vip显示]
function UIController:InitVIPShow()
    self.vipData = self.vipData or {}
    if function_open_mgr.CheckFunctionIsOpen(function_open_mgr.OpenIdEnum.VIP) then
        self.vipData.vipLevel = player_mgr.GetPlayerVipLevel()
        self.vipData.isGray = player_mgr.GetPlayerVipEndTime() - os.server_time() <= 0
        self.vipData.isShow = not main_slg_data.GetIsSandScene()
    else
        self.vipData.isShow = false
    end

    --审核服屏蔽
    if ReviewingUtil.IsReviewing() then
        self.vipData.isShow = false
    end
    self:TriggerUIEvent("SetVipData", self.vipData)
end



--设置头像
function UIController:SetPlayerFaceData()
    self.playerData = self.playerData or {}
    self.playerData.faceID = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.FaceID)
    self.playerData.playerLevel = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleLevel)
    self.playerData.frameID = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.FrameID)
    self.playerData.customAvatarData = custom_avatar_data.GetMyAvatar()
    self:TriggerUIEvent("SetPlayerFaceData", self.playerData)
end

--设置体力
function UIController:SetStaminaData()
    self.staminaData = self.staminaData or {}
    self.staminaData.realPower = sand_ui_data.GetStamina()
    self.staminaData.maxPower = sand_ui_data.GetStaminaLimit()
    self:TriggerUIEvent("SetStaminaData", self.staminaData)
end

function UIController:SetUnionWarBtn()
    local data = sand_gather_data.GetMainMassListInfo()
    self:TriggerUIEvent("SetUnionWarBtn", data)
end

function UIController:SetTaskData()
    local taskData = gw_task_mgr.GetMainShowTaskData()
    if not taskData then
        self:TriggerUIEvent("SetTaskData", nil)
        return
    end
    self.taskData = self.taskData or {}
    local cfg = game_scheme:TaskMain_0(taskData.taskId)
    if not cfg then
        return
    end
    self.taskData.taskId = taskData.taskId
    self.taskData.content = gw_task_util.GetTaskLang(taskData)
    self.taskData.status = taskData.rate / cfg.ConditionValue1
    self.taskData.redFunc = self.taskData.redFunc or function()
        return 0
    end
    self:TriggerUIEvent("SetTaskData", self.taskData)
end

function UIController:RefreshTaskWeakGuide()
    if GWMgr.curScene ~= GWConst.ESceneType.Home then
        return
    end
    local force_guide_system = require "force_guide_system"
    if force_guide_system.GetState() == 2 then
        return
    end
    local unforced_guide_mgr = require "unforced_guide_mgr"
    if unforced_guide_mgr.GetCurGuide() ~= nil then
        return
    end
    if self:RefreshRescuePeopleWeakGuide() then
        --触发救援弱引导，不触发任务弱引导
        return
    end
    local weak_guide_follow_mgr = require "weak_guide_follow_mgr"
    if weak_guide_follow_mgr.GetFollowEntityNum() > 0 then
        return
    end
    local isKeysOpen = windowMgr:IsModuleShown("ui_bomberman_keys")
    if isKeysOpen then
        return
    end
    self:TriggerUIEvent("RefreshTaskWeakGuide")
end

---@public 刷新救援弱引导
function UIController:RefreshRescuePeopleWeakGuide()
    local gw_bomberman_mgr = require "gw_bomberman_mgr"
    local state = gw_bomberman_mgr.GetBombermanWeakState()
    if state then
        self:TriggerUIEvent("RefreshBomberWeakGuide")
        return true
    end
    return false
end

function UIController:StopTaskWeakGuide()
    self:TriggerUIEvent("StopAllWeakGuide")
end

function UIController:SetReinforceBtn()
    local sand_warning_mgr = require "sand_warning_mgr"
    local data = {}
    local isShowWarning, warningInfo = sand_warning_mgr.GetShortTime()
    data.isActive = isShowWarning ~= 0
    data.iconIndex = isShowWarning ~= 0 and warningInfo.warningType or 0
    data.endTime = (isShowWarning ~= 0 and warningInfo.info) and warningInfo.info.timeEnd or 0
    if not data.isActive then
        local listCount = sand_reinforce_data.GetListCount()
        if listCount > 0 then
            data.isActive = true
        end
    end
    self:TriggerUIEvent("SetReinforceBtn", data)
end

function UIController:InitBomberMan()
    --这边只想初始化一次，但是onshow会执行好多次
    self:UpdateBomberMan()
end

function UIController:UpdateBomberMan()
    local isOpen = false
    if GWMgr.curScene == GWConst.ESceneType.Home then
        local gw_bomberman_mgr = require "gw_bomberman_mgr"
        local open = gw_bomberman_mgr.CheckOpen()
        local isEventOpen = gw_bomberman_mgr.IsBomberManPassEvent()
        isOpen = open and isEventOpen
    end
    self:TriggerUIEvent("UpdateBomberMan", isOpen)
end


-- 怪物攻城 start
function UIController:InitMonsterAttack()
    --处理 占位显示和消失
    local monsters_approaching_data = require "monsters_approaching_data"--旧版怪物来袭
    local isOpen = monsters_approaching_data.GetIsOpenActivity()
    local monsters_approaching_group_data = require "monsters_approaching_group_data"--新版怪物来袭
    isOpen = isOpen or monsters_approaching_group_data.GetIsOpenActivity()
    self:TriggerUIEvent("UpdateMonsterAttack", isOpen)
end

-- 设置攻城号角事件
function UIController:SetWarHornData()
    local mgr_nc_pops_data = require "mgr_nc_pops_data"
    self.warHornsData = mgr_nc_pops_data.GetWarHornData()
    if self.warHornsData then
        if os.server_time() >= self.warHornsData.data.nStartTime then
            self.warHornsTimestamp = self.warHornsData.siegeTime
            self.warHornsIsReady = false
            self:TriggerUIEvent("SetWarHornState", true, false)
        else
            self.warHornsTimestamp = self.warHornsData.data.nStartTime
            self.warHornsIsReady = true
            self:TriggerUIEvent("SetWarHornState", true, true)
        end
        self:CreateHornTimer()
    else
        self:TriggerUIEvent("SetWarHornState", false)
        self:RemoveHornTimer()
    end
end

-- 设置攻城号角事件
function UIController:SetSimpleModelState()
    local gw_sand_data = require "gw_sand_data"
    local level = gw_sand_data.GetSandSimpleLevel()
    self:TriggerUIEvent("SetSimpleModelState", level and level > 0 or false)
end

function UIController:CreateHornTimer()
    if not self.warHornsTicker then
        local sand_camp_mgr = require "sand_camp_mgr"
        self.warHornsTicker = self:CreateTimer(1, function()
            if self.warHornsTimestamp then
                local timestamp = self.warHornsTimestamp - os.server_time()
                if timestamp > 0 then
                    self:TriggerUIEvent("SetWarHornsText", timestamp)
                    if timestamp <= 10 and self.warHornsIsReady then
                        --攻城倒计时特效
                        self.warHornsIsReady = false
                        sand_camp_mgr.SetIsShowEffect(true)
                    end
                else
                    sand_camp_mgr.SetIsShowEffect(false)
                    self:SetWarHornData()
                end
            end
        end)
    end
end

function UIController:RemoveHornTimer()
    if self.warHornsTicker then
        self:RemoveTimer(self.warHornsTicker)
        self.warHornsTicker = nil
    end
end

function UIController:OnBtn_RightExpandClickedProxy()
    self:TriggerUIEvent("OnBtn_RightExpandClicked")
end

--专属客服
function UIController:OnRefreshECS()
    local ecs = require "ui_exclusive_customer_service_data"
    local edata = ecs.GetvipConfig()
    local open = edata and edata.reached
    -- log.Warning("[customer] 专属客服注册", open)
    if open then
        self:TriggerUIEvent("SetVipCustomer", true)
    else
        self:TriggerUIEvent("SetVipCustomer", false)
    end
end

function UIController:OnRefreshDownload()
    --又问题 = 定时刷新  
    self:CreateDelayTimer(3, function()
        event.Trigger(event.RefreshMain_Download)
    end)
end

--region 战区对决按钮
function UIController:SetZoneDuelData()
    local war_zone_duel_helper = require "war_zone_duel_helper"
    if war_zone_duel_helper.IsCongressAtk() then
        self:TriggerUIEvent("SetZoneDuelState", true)
        self:OnZoneDuelData({ nDefScore = 0, nAttScore = 0 }, true)
        self:CreateDuelZoneTimer()
        self:RequireDuelZoneCongressData()
    else
        self:TriggerUIEvent("SetZoneDuelState", false)
        self:RemoveDuelZoneTimer()
    end
end

function UIController:OnZoneDuelData(msgData, needWorldId)
    -- 统一引入所需模块，避免多次 require
    local war_zone_duel_helper = require "war_zone_duel_helper"
    if not war_zone_duel_helper.IsCongressAtk() then
        return
    end
    
    local gw_sand_cfg = require "gw_sand_cfg"
    local congressCfg = gw_sand_cfg.GetSandCongressCityCfg()
    if not congressCfg or not congressCfg.maxintegral then
        return
    end
    local maxScore = congressCfg.maxintegral > 0 and congressCfg.maxintegral or 1
    
    local war_zone_duel_data = require "war_zone_duel_data"
    local gw_common_util = require "gw_common_util"
    local atkWorldID = war_zone_duel_data.GetSelfZoneDuelAtkWorldID()
    local defWorldID = war_zone_duel_data.GetSelfZoneDuelDefWorldID()
    local selfZoneSid = gw_common_util.GetSandZoneSandBoxSid()
    
    local function buildData(worldID, progress)
        return {
            worldID = needWorldId and worldID,
            progress = progress
        }
    end

    local selfData, enemyData
    if atkWorldID == selfZoneSid then
        selfData  = buildData(atkWorldID, msgData.nAttScore / maxScore)
        enemyData = buildData(defWorldID, msgData.nDefScore / maxScore)
    else
        selfData  = buildData(defWorldID, msgData.nDefScore / maxScore)
        enemyData = buildData(atkWorldID, msgData.nAttScore / maxScore)
    end
    self:TriggerUIEvent("SetZoneDuelCongressData", selfData, enemyData)
end

function UIController:CreateDuelZoneTimer()
    if not self.duelZoneTicker then
        self:TriggerUIEvent("SetZoneDuelText", 0)

        local war_zone_duel_data = require "war_zone_duel_data"
        self.duelZoneRequiredCD = 10
        self.duelZoneTicker = self:CreateTimer(1, function()
            local duelZoneEndTime = war_zone_duel_data.GetTimeTypePoint(war_zone_duel_define.enZoneBattleDuelTimeType.CongressAtkEnd)
            local timeStamp = duelZoneEndTime - os.server_time()
            self:TriggerUIEvent("SetZoneDuelText", timeStamp)

            self.duelZoneRequiredCD = self.duelZoneRequiredCD - 1
            if self.duelZoneRequiredCD <= 0 then
                self.duelZoneRequiredCD = 10
                self:RequireDuelZoneCongressData()
            end
        end)
    end
end

function UIController:RemoveDuelZoneTimer()
    if self.duelZoneTicker then
        self:RemoveTimer(self.duelZoneTicker)
        self.duelZoneTicker = nil
    end
end

function UIController:RequireDuelZoneCongressData()
    local war_zone_duel_data = require "war_zone_duel_data"
    local net_war_zone_duel_module = require "net_war_zone_duel_module"

    local worldID = war_zone_duel_data.GetSelfZoneDuelDefWorldID()
    if worldID and worldID > 0 then
        net_war_zone_duel_module.MSG_ZONEBATTLEDUEL_GET_COUNTRY_SCORE_REQ(worldID)
    end
end
--endregion

---********************end功能函数区**********---
--region ModuleFunction 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
