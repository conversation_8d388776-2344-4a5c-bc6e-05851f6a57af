------------------------------------
---------------服务器切页-------------
------------------------------------
local print = print
local require = require
local ipairs = ipairs
local table = table
local typeof = typeof
local type = type
local string = string
local math = math
local dump=dump
local tonumber = tonumber
local tostring=tostring
local util = require "util"
local lang = require "lang"
local lang_key = require "lang_res_key"
local ui_base = require "ui_base"
local class = require "class"
local event = require "event"
local player_mgr = require "player_mgr"
local face_item = require "face_item_new"
local message_box = require "message_box"
local account_pb = require "account_pb"
local msg_pb = require "msg_pb"
local net = require "net"
local face_data=require"actor_face_data"
local ui_setting_util 	= require "ui_setting_util"
local log = require "log"
local game_config = require "game_config"
local ui_login_main_mgr = require "ui_login_main_mgr"
local const = require "const"
local setting_server_data = require "setting_server_data"
local ui_util = require "ui_util"
local IsInEditor = CS.War.Script.Utility.IsInEditor
local PlayerPrefs = CS.UnityEngine.PlayerPrefs
local Outline       = CS.UnityEngine.UI.Outline
local Text = CS.UnityEngine.UI.Text
local Button = CS.UnityEngine.UI.Button
local Toggle = CS.UnityEngine.UI.Toggle
local RectTransform = CS.UnityEngine.RectTransform
local Debug = CS.UnityEngine.Debug
local Canvas = CS.UnityEngine.Canvas
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable
local TextMeshProUGUI       = CS.TMPro.TextMeshProUGUI

module("ui_setting_server_new")

local window = nil
local isRecommend
local SettingServer = {}
local oParent = nil
local CSettingServer = nil
local serverListData = {}
local selectWorldID = nil
local ServerListShrinkDelta = 158.81
local ItemTextHighlightColor = {r=254/255,g=252/255,b=169/255,a=1} 
local ItemTextDefaultColor = {r=105/255,g=161/255,b=224/255,a=1} 
local ServerListWidth
local normalStartIdx = 0
local normalEndIdx = 0
local xHeroStartIdx = 0
local xHeroEndIdx = 0
local avengerStartIdx = 0
local avengerEndIdx = 0
local eNormal = 1
local eXHero = 2
local eAvenger = 3
local itemNum = 0
local allServerData = {}
local selectedIndex = nil --当前选中的服务器索引
local lastSelected = nil
local lastScrollItem = nil
local region = 
{
	NARegion = 1,  --欧美/韩国
	SeaRegion = 2, --东南亚
}
local curSelectRegion = nil  -- 当前选中的大区，1代表欧美 2代表东南亚
local indexCount = 50
local SWITCH_SHOW_NUM = false

SettingServer.widget_table =
{
	serverListSkin = {path = "serverList", type = RectTransform},
	serverList = {path = "serverList/Viewport/Content", type = ScrollRectTable},
	serverListItem = {path = "serverList/Viewport/Content/ListItem", type = RectTransform},
	rect_ServerListContent = {path = "serverList/Viewport/Content", type = RectTransform},
	serverSectionList = {path = "serverGroup/serverSectionList/Viewport/Content", type = ScrollRectTable},
	serverSection = {path = "serverGroup/serverSectionList/Viewport/Content/serverSection", type = RectTransform},
	serverSectionContent = {path = "serverGroup/serverSectionList/Viewport/Content", type = RectTransform},
	RecommendServer = {path = "serverGroup/RecommendServer", type = RectTransform},
	RecommendServerText = {path = "serverGroup/RecommendServer/Text", type = Text},

	closeBtn = {path = "closeBtn", type = Button, backEvent = true},
	toggleRec = {path = "serverGroup/RecommendServer", type = Toggle},
	toggleRec_Text = {path = "serverGroup/RecommendServer/Text", type = Text},
	toggleAll = {path = "serverGroup/allServer", type = Toggle},
	toggleAll_Text = {path = "serverGroup/allServer/Text", type = Text},

	serverGroup = {path = "serverGroup", type = "ToggleGroup"},

	--韩国包新增切页（韩国和亚太切页）
	toggleNA = {path = "regionToggleGroup/ToggleNA", type = Toggle},
	toggleNA_Text = {path = "regionToggleGroup/ToggleNA/Background/Text", type = TextMeshProUGUI},
	toggleNA_SelText = {path = "regionToggleGroup/ToggleNA/Background/Checkmark/Text", type = TextMeshProUGUI},
	toggleSEA = {path = "regionToggleGroup/ToggleSEA", type = Toggle},
	toggleSEA_Text = {path = "regionToggleGroup/ToggleSEA/Background/Text", type = TextMeshProUGUI},
	toggleSEA_SelText = {path = "regionToggleGroup/ToggleSEA/Background/Checkmark/Text", type = TextMeshProUGUI},
	regionToggleGroup = {path = "regionToggleGroup", type = RectTransform},

}
local serverStateColor = {
	[1] = {r = 255/255 ,g = 77/255 ,b = 77/255 ,a = 1}, --拥挤
	[2] = {r = 28/255 ,g = 167/255 ,b = 31/255 ,a = 1} -- 畅通
}
function onItemRenderBottom(scroll_rect_item, index, dataItem)
	scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem
	
    local btn = scroll_rect_item:Get("btn")
    local selectImage = scroll_rect_item:Get("selectedBg")
    local serverName = scroll_rect_item:Get("serverName")
    local roleInfo = scroll_rect_item:Get("roleInfo")
    local actorName = scroll_rect_item:Get("name")
	local level = scroll_rect_item:Get("level")
	local face = scroll_rect_item:Get("face")
	local newIcon = scroll_rect_item:Get("new")
	local stateImage = scroll_rect_item:Get("stateImage")
	local stateImageRect = scroll_rect_item:Get("stateImageRect")
	local serverNameRect = scroll_rect_item:Get("serverNameRect")
	local hotGo = scroll_rect_item:Get("hot")
	local normalGo = scroll_rect_item:Get("normal")

	local iconUI = scroll_rect_item.data["itemUI"] or face_item.CFaceItem():Init(face, nil, 1.3)
    scroll_rect_item.data["itemUI"] = iconUI
	iconUI:SetNewBg(true)
	local hot = dataItem.roleid and dataItem.roleid > 0 or dataItem.isnew
	stateImage.color = hot and serverStateColor[1] or serverStateColor[2]
	hotGo.gameObject:SetActive(hot)
	normalGo.gameObject:SetActive(not hot)
	
	if SWITCH_SHOW_NUM then 
		serverName.text =  string.format("#%s",ui_util.GetWorldIDToShowWorldID(dataItem.worldid,nil,ui_util.WorldIDRangeType.Normal)) -- dataItem.worldid) 
	else
		serverName.text = dataItem.worldName
	end
	-- if isRecommend then
	-- 	actorName.gameObject:SetActive(true)
	-- else
	-- 	actorName.gameObject:SetActive(false)
	-- end
	actorName.text = dataItem.name
	if dataItem.level then
		level.text = "Lv. "..dataItem.level
	end
	newIcon.gameObject:SetActive(dataItem.isnew)			--最新服为true
	selectImage.gameObject:SetActive(dataItem.isCurr and dataItem.isCurr == 1)		--当前所在的服务器为true
	roleInfo.gameObject:SetActive(dataItem.roleid and dataItem.roleid > 0)
	if dataItem.roleid and dataItem.roleid > 0 then
		iconUI:SetFaceInfo(dataItem.faceid, nil)
		iconUI:SetActorLvText(true, dataItem.level)
		if dataItem.roleid == player_mgr.GetPlayerRoleID() then
			local frameID = face_data.GetRoleFrameID()
			iconUI:SetFrameID(frameID,true)
			iconUI:FrameEffectEnable(true, window.curOrder+1,1)
		else
			iconUI:SetFrameID(0,true)
			iconUI:FrameEffectEnable(false, window.curOrder+1,1)
		end
		stateImageRect.anchoredPosition = {x = -76.88, y = 24.7, z = 1}
		serverNameRect.sizeDelta = {x=224.6968,y=43.42}
	else
		stateImageRect.anchoredPosition = {x = -192, y = 0, z = 1}
		serverNameRect.sizeDelta = {x=379,y=43.42}
	end
	local btnEvent = function()
		if dataItem.isCurr and dataItem.isCurr == 0 then
			--切换资源版本检测
			local split_server_res_ver_mgr = require"split_server_res_ver_mgr"
			local isNeedDownload, version = split_server_res_ver_mgr.CheckSwitchServerIsNeedDownload(dataItem.worldid)
			if isNeedDownload then
				local msgContent = lang.Get(lang_key.KEY_SETTING_TIPS2)
				msgContent = "目标服务器资源版本过高，更新后才能切服，是否更新";  --先暂时写死
				message_box.Open(msgContent, message_box.STYLE_YESNO,function(_, nRet)
					if message_box.RESULT_YES == nRet then
						split_server_res_ver_mgr.DownloadSwitchServerRes(dataItem.worldid, version)
					end
				end, nil, 
						lang.KEY_OK,
						lang.KEY_CANCEL)
				return
			end
			
			local content = lang.Get(lang_key.KEY_SETTING_TIPS2)
			message_box.Open(
				content, message_box.STYLE_YESNO,
				function(data, nRet)
					if message_box.RESULT_YES == nRet then
						--返回登录
						local ui_login_main = require "ui_login_main"
						ui_login_main.SetNewWorldID(dataItem.worldid)	

						local net_login_module = require "net_login_module"
						net_login_module.SetSwitchServer(true)
						net_login_module.AutoReturnLogin()
						
						local q1sdk = require "q1sdk"
						q1sdk.SwitchWorld()
						
						log.Warning("---------切换到区id----------", dataItem.worldid)
						selectWorldID = dataItem.worldid
						ConnectServer()
					end
				end,
				0,
				lang.KEY_OK,
				lang.KEY_CANCEL
			)
		end
	end
	
	local func = {}
	func["btnEvent"] = btnEvent

	scroll_rect_item.InvokeFunc = function(funcname)
		func[funcname]()
    end
end
function ConnectServer()
	local setting_server_data = require "setting_server_data"
	local net_login_module = require "net_login_module"
	setting_server_data.SetLoginWorldID(selectWorldID)
	net_login_module.SetLoginWorldID(selectWorldID)
	local ui_login_main = require "ui_login_main"
	ui_login_main.SaveWorldId(selectWorldID)
	--S2CActorInfo流程会根据verifying来判断是否执行，换区需要执行这个流程
	net_login_module.SetVerifying(true)
	setting_server_data.ReportSelectServerReport("select_server")	
	event.Trigger(event.SCENE_DESTROY)
	event.Trigger(event.ACCOUNT_CHANGE_WORLD_RSP)
end

local selectedColor = {r = 51/255 , g = 87/255, b = 154/255, a = 1}
local unSelectedColor = {r = 77/255 , g = 113/255, b = 138/255, a = 1}
local selectIndex = 0
function onServerSectionListItemRenderBottom(scroll_rect_item, index, dataItem)
	scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem

	local btn = scroll_rect_item:Get("btn")
    local range = scroll_rect_item:Get("Text")
	local TextOutline = scroll_rect_item:Get("TextOutline")
	
	range.text = dataItem.name
	local func = {}
	--toggle事件
	scroll_rect_item.data["toggleEvent"] = function(isOn)
		if isOn then
			selectedIndex = index
			isRecommend = false
			if window then
				window:SetServerContent()
			end
			TextOutline.effectColor = selectedColor
			local serverDatas = window and window.serverDatas
			local openKoreaServer = serverDatas and #serverDatas>0--韩国包配置韩服显示韩国专服
			if not curSelectRegion and openKoreaServer and const.IsKoreaChannel() then
				curSelectRegion = region.NARegion
				if window then
					window.toggleNA.isOn = true
					window:RegionToggleEvent(curSelectRegion)
				end
			end
			--切换到所有server时不能排序，要按照顺序显示
			if window and window.serverList then
				if not IsSinglePage() then
					local data = window.ServerListDataNoSort
					if selectedIndex and allServerData[curSelectRegion][selectedIndex] then
						data = allServerData[curSelectRegion][selectedIndex]
					end
					window.serverList.data = data
				else
					--TODO韩国包亚太服无角色且有配置韩国服务器时，列表中筛选只保留韩国专服
					if openKoreaServer and const.IsKoreaChannel()  then
						curSelectRegion = region.NARegion
						local data = window.ServerListDataNoSort
						if selectedIndex and allServerData[curSelectRegion] and  allServerData[curSelectRegion][selectedIndex] then
							data = allServerData[curSelectRegion][selectedIndex]
						end
						window.serverList.data = data
					else
						window.serverList.data = GetSectionServerDataByIndex(index)
					end
				end
				window.serverList:Refresh(-1, -1)
				selectIndex = index
			end
		else
			TextOutline.effectColor = unSelectedColor
		end
	end
	btn.onValueChanged:AddListener(scroll_rect_item.data["toggleEvent"])
    -- func["toggleEvent"] = toggleEvent
	-- scroll_rect_item.InvokeFunc = function(funcname)
	-- 	func[funcname]()
    -- end
end

function GetSectionServerDataByIndex(_index)
	if window and window.ServerListDataNoSort then
		local range2 = #window.ServerListDataNoSort - (window.groupCount - _index) * 50
		local range1 = (range2 - 50) < 0 and 0 or (range2 - 50) + 1 
		local tempData = {}
		for i = range1 , range2 do 
			table.insert(tempData,window.ServerListDataNoSort[i])
		end
		return tempData
	else
		log.Error("无服务器列表数据，区间段：",range1,"-",range2)
		return nil
	end
	
end

function SettingServer:ctor(selfType)
	self.__base:ctor(selfType)
end

--服务器记录玩家不同区服的自定义头像比较复杂，现通过客户端缓存的方式记录自定义头像信息
function SettingServer:CacheFaceInfo(faceStr,roleID,worldID) 
	local key = string.format("CacheFaceInfo#%d#%d",roleID,worldID) 
	local value = PlayerPrefs.GetString(key)
	local curWorldID = setting_server_data.GetLoginWorldID()
	if string.IsNullOrEmpty(value) then 
		if not tonumber(faceStr) then 
			PlayerPrefs.SetString(key,faceStr)
		end
		return faceStr
	else
		if curWorldID == worldID then 
			if not tonumber(faceStr) then  
				PlayerPrefs.SetString(key,faceStr)
			end
			return faceStr
		else
			if not tonumber(faceStr) then 
				PlayerPrefs.SetString(key,faceStr)
				return faceStr
			else
				return value
			end
		end
	end
end

--[[
	2021年11月18日所有服务器修改规则如下：
	1、每50个服务器为一组
	2、只显示最新的2个组和有角色的组的服务器
]]
function SettingServer:UpdateList()
	local accountDatas = setting_server_data.GetAllAccountListData() or {}			--服务器发下来的数据
	local serverDatas = setting_server_data.GetServerListData()
	local worldid = setting_server_data.GetLoginWorldID()										--当前的世界id
	local isConnectWs = setting_server_data.IsConnectWebSocket()

----	 --print("SettingServer:UpdateList>>>>>>>>>>>")
----	 --print("GetLoginWorldID>>>",worldid)
	--dump(serverDatas)
	--dump(accountDatas)
	--for j = 1, #accountDatas do
		------print(accountDatas[j].faceid)
	--end

	local listDatas = {}
	local serverList = ui_login_main_mgr.GetLoginServerList()
	if accountDatas and serverDatas and serverList then
		for i = 1, #serverDatas do
			listDatas[i] = {}
			listDatas[i].worldid = serverDatas[i].id--ui_util.GetWorldIDToShowWorldID(serverDatas[i].id,nil,ui_util.WorldIDRangeType.Normal) 
			listDatas[i].loginTime = serverList[listDatas[i].worldid] or 0
			--后续确认如果需要openServerId，则加上
			--if serverDatas[i].openServerId then
			--	listDatas[i].openServerId = serverDatas[i].openServerId
			--end
			listDatas[i].worldName = serverDatas[i].name
			listDatas[i].isnew = serverDatas[i].isnew
			listDatas[i].isws = serverDatas[i].isws
			listDatas[i].isCurr = 0
			listDatas[i].roleid = 0
			listDatas[i].name = ""
			listDatas[i].level = ""
			listDatas[i].faceid = ""
			listDatas[i].regionId = serverDatas[i].regionID
			if worldid == listDatas[i].worldid and isConnectWs == listDatas[i].isws then --用世界id好一些
				listDatas[i].isCurr = 1
				listDatas[i].roleid =player_mgr.GetPlayerRoleID()
				listDatas[i].name = player_mgr.GetRoleName() or ""
				listDatas[i].level = player_mgr.GetPlayerLV() or ""
				--适配faceID 2025.4.2 将faceID 转换为 faceStr
				local custom_avatar_data = require "custom_avatar_data"
				local customHeadData = custom_avatar_data.GetMyAvatar()
				local faceStr = face_data.GetRoleFaceID()
				if customHeadData then 
					faceStr = customHeadData.remoteUrl
				end
				listDatas[i].faceid = self:CacheFaceInfo(faceStr or "",listDatas[i].roleid,listDatas[i].worldid) 
			end
			if listDatas[i].isCurr ~= 1 then --确保数据及时，当前区的不从这里取
				local accountInfo = accountDatas[serverDatas[i].id]
				if accountInfo then
					listDatas[i].roleid = accountInfo.roleid
					listDatas[i].name = accountInfo.name
					listDatas[i].level = accountInfo.level
					listDatas[i].faceid = self:CacheFaceInfo(accountInfo.faceid,listDatas[i].roleid,listDatas[i].worldid) 
				end
			end
		end

		-- 只添加最新的两组以及有角色的组 50个服为一组
		self.ServerListDataNoSort = {}
		-- local showAllServer = PlayerPrefs.GetInt("isAllServerInfo", 0)
		local showAllServer = 1
		if showAllServer == 0 then
			table.sort(listDatas, function(a, b)
				if b==nil then return false end
					return a.worldid<b.worldid
			end)
			local count = 50
			local groupCount = math.ceil(#listDatas/count) 
			local index = 0
			local grounData = {}
			
			if groupCount <= 2 then
				self.ServerListDataNoSort = ui_setting_util.DeepCopy(listDatas)
			else
				for i = 1, groupCount do
					local tempData = {}
					for j = 1, count do
						index = (i - 1) * count + j
						if listDatas[index] then
							table.insert(tempData, listDatas[index])
						end
					end
					table.insert(grounData, tempData)
				end
		
				for key, tempData in ipairs(grounData) do
					if key > groupCount - 2 then--取最新的两组服务器
						for i = 1, #tempData do
							table.insert(self.ServerListDataNoSort, tempData[i])
						end
					else
						-- 该组有角色，显示整组
						local hasRole = false
						for i = 1, #tempData do
							if tempData[i].roleid > 0 then
								hasRole = true
								break
							end
						end
						if hasRole == true then
							for i = 1, #tempData do
								table.insert(self.ServerListDataNoSort, tempData[i])
							end
						end
					end
				end
			end
	
			--所有服务器只按开服id排序
			table.sort(self.ServerListDataNoSort, function(a, b)
				if b==nil then return false end
					return a.worldid>b.worldid
			end)
		else
			table.sort(listDatas, function(a, b)
				if b==nil then return false end
					return a.worldid>b.worldid
			end)
			self.ServerListDataNoSort = ui_setting_util.DeepCopy(listDatas)
		end
		
		------  --print("all server list:")
		--dump(self.ServerListDataNoSort)
		

	
		for i=#listDatas,1,-1 do
			if not listDatas[i].isnew and 
			listDatas[i].roleid == 0 then
				table.remove(listDatas,i)--推荐服务器剔除没有角色和非new的服务器
			end
		end
		if const.IsKoreaChannel() and not setting_server_data.HasSEARole() then
			serverDatas = window and window.serverDatas
			local openKoreaServer = serverDatas and #serverDatas>0--韩国包配置韩服显示韩国专服
			if openKoreaServer then
				--韩国包没有亚太服角色且有开放韩服时剔除亚太服
				for i=#listDatas,1,-1 do
					if listDatas[i].regionId and listDatas[i].regionId == 3 then
						table.remove(listDatas,i)--推荐服务器剔除的服务器
					end
				end
			end
		end

		table.sort(listDatas, function(a, b)
			if b==nil then return false end
			if a.isCurr ~= b.isCurr then return a.isCurr == 1 end
			--推荐服务器，目前主要按等级排序
			if a.isnew ~= b.isnew then return a.isnew end
			if a.isnew then
				-- a 和 b 都是新服，按 worldid 降序
				return a.worldid > b.worldid
			end
			if a.loginTime == 0 and b.loginTime == 0 then
				if (a.level and a.level~="") and (b.level and b.level~="") then
					if tonumber(a.level) > tonumber(b.level) then 
						--Debug.LogError("a level:"..a.level.." b level:"..b.level.." return true")
						return true 
					else
						--Debug.LogError("a level:"..a.level.." b level:"..b.level.." return false")
						return false
					end
				end
			end
			return a.loginTime > b.loginTime and true or false
			-- if a.roleid > 0 and b.roleid == 0 then return true end
			-- if b.roleid > 0 and a.roleid == 0 then return false end
			--if a.isnew ~= b.isnew then return a.isnew end
			--return a.worldid > b.worldid
		end)
		-- Debug.Log("after sort,server list:")
		-- --dump(listDatas)
		-- for i,v in ipairs(listDatas) do
		-- 	Debug.Log(v.level.." isnew:"..tostring(v.isnew).." iscur:"..tostring(v.isCurr))
		-- end
		--dump(listDatas)
		
		self.ServerListData = listDatas

		-- 刷新所有服务器的分段列表		
		serverDatas = window and window.serverDatas
		local openKoreaServer = serverDatas and #serverDatas>0--韩国包配置韩服显示韩国专服
		if not IsSinglePage() or (openKoreaServer and const.IsKoreaChannel()) then
			self:InitServerListItem(self.ServerListDataNoSort)
			if not curSelectRegion then
				curSelectRegion = region.NARegion
			end
			self.serverSectionList.data = curSelectRegion == region.NARegion and self.naServerIndexToggleData or self.seaServerIndexToggleData
			self.serverSectionList:Refresh(-1, -1)
		else
			self.serverSectionList.data = self:BuildServerSectionListData(self.ServerListDataNoSort)
			self.serverSectionList:Refresh(-1, -1)		
		end
		-- 刷新推荐服务器
		self.serverList.data = self.ServerListData
		self.serverList:Refresh(-1, -1)
	end
end

--所有服务器分组数据
function SettingServer:BuildServerSectionListData(datas)
	local count = 50
	self.groupCount = math.ceil(#datas/count) 
	local grounData = {}
	local text = nil
	local range1 = 0
	local range2 = 0
	for i = 1, self.groupCount do
		range1 = (self.groupCount-i)*indexCount+1
		range2 = (self.groupCount-i + 1)*indexCount
		text = string.format(lang.Get(852),range1,range2)
		table.insert(grounData,{index = i, name = text,range1 = range1,range2 = range2})
	end

	return grounData
end
function SettingServer:SubscribeEvents()
	self.OnAccountListRsp = function()
		self:UpdateList()
	end
	event.Register(event.SETTING_ACCOUNT_LIST_RSP, self.OnAccountListRsp)
	
	self.OnUpdateServerListData = function()
		self:UpdateList()
	end
	event.Register(event.SETTING_UPDATE_SERVER_LIST_DATA, self.OnUpdateServerListData)

		
	self.OnToggleSEAChange = function (isOn)
		if isOn and not IsSinglePage() then
			self:RegionToggleEvent(region.SeaRegion)
		end
	end
	self.toggleSEA.onValueChanged:AddListener(self.OnToggleSEAChange)

	self.OnToggleNAChange = function (isOn)
		if isOn and not IsSinglePage() then
			self:RegionToggleEvent(region.NARegion)
		end
	end
	self.toggleNA.onValueChanged:AddListener(self.OnToggleNAChange)
end

function SettingServer:UnsubscribeEvents()
	event.Unregister(event.SETTING_ACCOUNT_LIST_RSP, self.OnAccountListRsp)
	event.Unregister(event.SETTING_UPDATE_SERVER_LIST_DATA, self.OnUpdateServerListData)
	self.toggleSEA.onValueChanged:RemoveListener(self.OnToggleSEAChange)
	self.toggleNA.onValueChanged:RemoveListener(self.OnToggleNAChange)
end

function SettingServer:RegionToggleEvent(selectRegion)
	if not game_config.Q1SDK_DOMESTIC then
		curSelectRegion = selectRegion
		self.toggleNA_Text.gameObject:SetActive(curSelectRegion == region.SeaRegion)
		self.toggleNA_SelText.gameObject:SetActive(curSelectRegion == region.NARegion)
		self.toggleSEA_Text.gameObject:SetActive(curSelectRegion == region.NARegion)
		self.toggleSEA_SelText.gameObject:SetActive(curSelectRegion == region.SeaRegion)
		
		-- 刷新所有服务器的分段列表
		self.serverSectionList.data = curSelectRegion == region.NARegion and self.naServerIndexToggleData or self.seaServerIndexToggleData

		local data = self.ServerListDataNoSort
		if not selectedIndex then
			selectedIndex = 1
		end
		if selectedIndex and allServerData[curSelectRegion][selectedIndex] then
			data = allServerData[curSelectRegion][selectedIndex]
		else
			data = {}
		end
		self.serverList.data = data
	else
		-- 刷新所有服务器的分段列表
		self.serverSectionList.data = self:BuildServerSectionListData(self.ServerListDataNoSort)
		self.serverList.data = self.ServerListDataNoSort
	end
	self.serverSectionList:Refresh(-1, -1)
	self.serverList:Refresh(-1, -1)
end

function SettingServer:SetServerContent()
	if isRecommend then
		self.regionToggleGroup.gameObject:SetActive(false)	
	else
		self.regionToggleGroup.gameObject:SetActive(not IsSinglePage())
		self.toggleNA_Text.text = lang.Get(58401)
		self.toggleNA_SelText.text = lang.Get(58401)
		self.toggleSEA_Text.text = lang.Get(58402)
		self.toggleSEA_SelText.text = lang.Get(58402)
	end
end

function SettingServer:InitServerVisibleItem(index, nRegion)
	local serverData = {}
	
	local range2 = #self.naList - (#self.naServerIndexToggleData - index) * 50
	local range1 = (range2 - 50) < 0 and 0 or (range2 - 50) + 1 

	if nRegion == region.NARegion then
		for i = range1, range2 do
			if self.naList[i] then
				table.insert(serverData, self.naList[i])
			end
		end
	end
	if nRegion == region.SeaRegion then
		range2 = #self.seaList - (#self.seaServerIndexToggleData - index) * 50
		range1 = (range2 - 50) < 0 and 0 or (range2 - 50) + 1 
		for i = range1, range2 do
			if self.seaList[i] then
				table.insert(serverData,  self.seaList[i])
			end
		end
	end
	return serverData
end

function SettingServer:InitServerListItem(datas)
	self.naList = {}
	self.seaList = {}
	self.naServerIndexToggleData = {}
	self.seaServerIndexToggleData = {}
	for index, value in ipairs(datas) do
		-- 欧美或其他配置、东南亚
		if value.regionId == 3 then
			table.insert(self.seaList, value)
		else
			table.insert(self.naList, value)
		end
	end
	-- 欧美/韩国
	local naCount = table.getn(self.naList)
	itemNum = math.ceil(naCount/indexCount)
	local text = nil
	local range1 = 0
	local range2 = 0
	for idx = 1, itemNum do
		range1 = (itemNum-idx)*indexCount+1
		range2 = (itemNum-idx + 1)*indexCount
		text = string.format(lang.Get(852),range1,range2)
		table.insert(self.naServerIndexToggleData,{index = idx, name = text,range1 = range1,range2 = range2})
	end

	

	-- 东南亚
	local seaCount = table.getn(self.seaList)
	itemNum = math.ceil(seaCount/indexCount)
	for idx = 1, itemNum do
		range1 = (itemNum-idx)*indexCount+1
		range2 = (itemNum-idx + 1)*indexCount
		text = string.format(lang.Get(852),range1,range2)
		table.insert(self.seaServerIndexToggleData,{index = idx, name = text,range1 = range1,range2 = range2})
	end

	allServerData = {[region.NARegion] = {}, [region.SeaRegion] = {}}
	for i,v in ipairs(self.naServerIndexToggleData) do
		allServerData[region.NARegion][v.index] = self:InitServerVisibleItem(v.index, region.NARegion)
	end

	for i,v in ipairs(self.seaServerIndexToggleData) do
		allServerData[region.SeaRegion][v.index] = self:InitServerVisibleItem(v.index, region.SeaRegion)
	end
end

function SettingServer:Init()
	if const.IsKoreaChannel() then
		self.serverDatas = setting_server_data.GetTcpServerListData(2)
	end
	ServerListWidth = self.serverListSkin.sizeDelta.x
	self:SubscribeEvents()
	self.toggleData =
	{
		{isRec = true,obj = self.toggleRec ,text = self.toggleRec_Text},
		-- {isRec = false,obj = self.toggleAll ,text = self.toggleAll_Text},
	}

	isRecommend = true
	self.serverList.onItemRender = onItemRenderBottom
	self.serverList.onItemDispose = function(scroll_rect_item, index)
		if scroll_rect_item and scroll_rect_item.data and scroll_rect_item.data.itemUI ~= nil then
			scroll_rect_item.data.itemUI:Dispose()
            scroll_rect_item.data.itemUI = nil
		end
	end
	self:SetServerContent()
	
	self.serverSectionList.onItemRender = onServerSectionListItemRenderBottom
	self.serverSectionList.onItemDispose = function(scroll_rect_item, index)
		if scroll_rect_item and scroll_rect_item.data and scroll_rect_item.data["toggleEvent"] then
			local btn = scroll_rect_item:Get("btn")
			if not util.IsObjNull(btn) then
				btn.onValueChanged:RemoveListener(scroll_rect_item.data["toggleEvent"])
			end
		end
	end
	self:UpdateList()
	
	self.onClickCloseBtn = function()
		local ui_window_mgr = require "ui_window_mgr"
		ui_window_mgr:UnloadModule("ui_setting_server_new")
	end
	self.closeBtn.onClick:AddListener(self.onClickCloseBtn)

	self.OnToggleChange = function(isOn)
		for i,v in ipairs(self.toggleData) do
			if v.obj.isOn then
				self.serverGroup.enabled = true
				isRecommend = v.isRec
				self:SetServerContent()
				if not isRecommend then
					--切换到所有server时不能排序，要按照顺序显示
					self.serverList.data = self.ServerListDataNoSort
					self.serverList:Refresh(-1, -1)
					v.text.gameObject:SetActive(v.obj.isOn)
				else
					self.serverList.data = self.ServerListData
					self.serverList:Refresh(-1, -1)
				end
				v.text.gameObject:GetComponent(typeof(Outline)).effectColor = selectedColor
			else
				v.text.gameObject:GetComponent(typeof(Outline)).effectColor = unSelectedColor
			end				
		end
	end
	--self.toggleData[1].obj.onValueChanged:AddListener(self.OnToggleChange)
	for i,v in ipairs(self.toggleData) do
		v.obj.onValueChanged:AddListener(self.OnToggleChange)
	end

	--self.UIRoot:GetComponent(typeof(Canvas)).sortingLayerName = "Popups"
end

--@region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function SettingServer:OnShow()
	local net_setting_module = require "net_setting_module"
	net_setting_module.SendAccountListReq()
	self.__base:OnShow()
end

function SettingServer:Close()
	if self.toggleData then
		for i,v in ipairs(self.toggleData) do
			v.obj.onValueChanged:RemoveListener(self.OnToggleChange)
		end
	end

	if self.UIRoot and self:IsValid() then
		self:UnsubscribeEvents()
	end

	if self.serverList then
        self.serverList:ItemsDispose()
    end
	
	if self.serverSectionList then
        self.serverSectionList:ItemsDispose()
    end
	
	if self.onClickCloseBtn then
		self.closeBtn.onClick:RemoveListener(self.onClickCloseBtn)
		self.onClickCloseBtn = nil
	end
	
	self.__base:Close()
	window = nil
	normalStartIdx = 0
	normalEndIdx = 0
	xHeroStartIdx = 0
	xHeroEndIdx = 0
	avengerStartIdx = 0
	avengerEndIdx = 0
	itemNum = 0
	allServerData = {}
	selectedIndex = nil
	lastScrollItem = nil
	curSelectRegion = nil
end

--TODO：韩国包亚太服有角色时显示亚太服和韩国专服；亚太服无角色时只显示韩国专服
function IsSinglePage()
	if const.IsKoreaChannel() then--韩国包未配置韩服显示单页
		local serverDatas = window and window.serverDatas
		local openKoreaServer = serverDatas and #serverDatas>0--韩国包配置韩服显示韩国专服
		if not openKoreaServer then--韩国包未配置韩服显示单页
			return true--未开放韩服时，所有服务器都是单页显示
		end
	end
	return not const.IsKoreaChannel() or (const.IsKoreaChannel() and not setting_server_data.HasSEARole())
end

CSettingServer = class(ui_base, nil, SettingServer)
function Show()
	if window == nil then
		window = CSettingServer()
		window._NAME = _NAME;window:LoadUIResource("ui/prefabs/uisettingservernew.prefab", nil, nil, nil, true)
	else
		window:Show()
	end
	return window
end

function Hide()
	if window ~= nil then
		window:Hide()
	end
end

function Close()
	if window ~= nil then
		window:Close()
		window = nil
	end
end

function ChangeWorldRsp(token, userid)
	if selectWorldID then
----		 --print("ChangeWorldRsp>>>>>>>",token,userid)
		local net_gateway_module = require "net_gateway_module"
		local net_login_module = require "net_login_module"
		local msg = account_pb.TMSG_ACCOUNT_CHANGE_WORLD_LOGIN_REQ()
		msg.userid = userid
		msg.token = token


		--net_login_module.SetLoginContext(tostring(msg.userid), "g", net_login_module.GetPartnerID(), msg.token )
		net_gateway_module.ConnectServer(function()
----			 --print("ChangeWorldRsp---连接网关成功回调------------")
			net.SendMessage(net.Endpoint_Client, net.Endpoint_AccountLogin, 0, msg_pb.MSG_ACCOUNT_CHANGE_WORLD_LOGIN_REQ, msg)
			--util.DelayCall(0.1, function ()
			--end) 
			setting_server_data.SetLoginWorldID(selectWorldID)
			net_login_module.SetLoginWorldID(selectWorldID)
			
			local ui_login_main = require "ui_login_main"
			ui_login_main.SaveWorldId(selectWorldID)

			--S2CActorInfo流程会根据verifying来判断是否执行，换区需要执行这个流程
			net_login_module.SetVerifying(true)
		end, selectWorldID,net_gateway_module.ENUM_CON_REASON.changeWorld)

		setting_server_data.ReportSelectServerReport("select_server")	
	end
end

function OnSceneDestroy()
	Close()
end

event.Register(event.SCENE_DESTROY, OnSceneDestroy)