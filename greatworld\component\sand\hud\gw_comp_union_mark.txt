---
--- Created by connan.
--- DateTime: 2025/2/20
--- Desc: 个人标记

local require = require
local newclass = newclass
local typeof = typeof
local UIUtil = UIUtil
local Vector3 = CS.UnityEngine.Vector3
local MeshRenderer = CS.UnityEngine.MeshRenderer
local Utility = CS.War.Script.Utility
local SpriteRenderer = CS.UnityEngine.SpriteRenderer
local SpriteButton = CS.TextMeshUtil.SpriteButton
local SpriteFitSize = CS.TextMeshUtil.SpriteFitSize
local GameObject = CS.UnityEngine.GameObject
local BoxCollider = CS.UnityEngine.BoxCollider

local gw_ed = require "gw_ed"
local effect_item = require "effect_item"
local sand_hud_base = require "sand_hud_base"
local AllianceMgr = require "alliance_mgr"
local windowMgr = require "ui_window_mgr"
local util = require "util"
local event = require "event"

module("gw_comp_union_mark")

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -
---@class GWCompUnionMark : GWDisposableObject
local GWCompUnionMark = newclass("gw_comp_union_mark", sand_hud_base)
local ShowEffect = "art/effects/effects/effect_scene_jijiedi/prefabs/effect_scene_jijiedi.prefab"

GWCompUnionMark.widget_table = {
    obj_root = { path = "root", type = GameObject },
    obj_effect = { path = "root/obj_effect", type = GameObject },
    sp_mark = { path = "root/sp_mark", type = SpriteRenderer },
    obj_mark = { path = "root/sp_mark", type = GameObject },
    txt_mark = { path = "root/btn_bg/txt_mark", type = "TextMesh" },

    btn_bg_size = { path = "root/btn_bg", type = SpriteFitSize },

    btn_bg = { path = "root/btn_bg", type = SpriteButton, event_name = "OnBgClicked" },
    sp_bg = { path = "root/btn_bg", type = SpriteRenderer },
    boxC_bg = { path = "root/btn_bg", type = BoxCollider },

    btn_translate = { path = "root/btn_bg/btn_translate", type = SpriteButton, event_name = "OnTranslateClicked" },
}

function GWCompUnionMark:InitHudComp(hudData, data, tab)
    self.markHudData = data
    sand_hud_base.InitHudData(self, hudData)
    self:InstantiateModelAsync(data.resPath, data.parent)
end

---实例化成功 （现必须基成设置名字和组件Id）
---@see override
function GWCompUnionMark:InstantiateSuccess(_obj)
    sand_hud_base.InstantiateSuccess(self, _obj)

    self.markText = self.markHudData.langDes
    self:RefreshMarkData(self.markHudData)

    self.effectObj = self.effectObj or effect_item.CEffectItem():Init(ShowEffect, self.obj_effect.transform, nil, function(gameObject)
        self.transEffect = UIUtil.GetTrans(gameObject.transform, "Effect")
        self.markEffectImg = UIUtil.GetTrans(gameObject.transform, "Effect/f_mask_tky_003_gcl")
        self:FindAllWithMeshRenderer(self.transEffect)
        self:SandViewLevelChange()
    end, 1, false, false, false)
end

function GWCompUnionMark:RegisterListener()
    sand_hud_base.RegisterListener(self)

    self.updateTranslate = function(_, sStr, tStr)
        if self and self:IsValid() then
            if self.markHudData and sStr == self.markHudData.langDes then
                self.markHudData.tLangDes = tStr
                self.markText = self.markHudData.tLangDes
                self:SetMarkText(self.markText)
            end
        end
    end
    self:RegisterEvent(event.TRANSLATE_RSP, self.updateTranslate)

    self.onSandViewLevelChange = function(_, newLod, oldLod)
        if self:IsValid() then
            self:SandViewLevelChange(newLod)
        end
    end
    gw_ed.mgr:Register(gw_ed.GW_SAND_VIEW_LEVEL_CHANGE, self.onSandViewLevelChange)
end

function GWCompUnionMark:UnregisterListener()
    sand_hud_base.UnregisterListener(self)
    gw_ed.mgr:Unregister(gw_ed.GW_SAND_VIEW_LEVEL_CHANGE, self.onSandViewLevelChange)
end

function GWCompUnionMark:OnBgClicked()
    if self.markHudData.markData.nIndex ~= 1 and AllianceMgr.CanSetGatherPoint() then
        --不是同盟集结点并且有联盟权限
        local openMarkData = {}
        openMarkData.type = 1
        openMarkData.markData = self.markHudData.markData
        windowMgr:ShowModule("ui_sand_mark_tip", nil, nil, openMarkData)
    end
    local sandbox_ui_mgr = require "sandbox_ui_mgr"
    sandbox_ui_mgr.OnGW_CLICK_EMPTY()
end

function GWCompUnionMark:OnTranslateClicked()
    if not self.markHudData then
        return
    end

    if self.markHudData.tLangDes then
        if self.txt_mark.text == self.markHudData.tLangDes then
            self.markText = self.markHudData.langDes
        else
            self.markText = self.markHudData.tLangDes
        end
        self:SetMarkText(self.markText)
    else
        local net_chat_module_new = require "net_chat_module_new"
        net_chat_module_new.Req_TRANSLATE(self.markHudData.langDes)
    end
end

local gw_sand_camera_mgr
function GWCompUnionMark:SandViewLevelChange()
    if util.IsObjNull(self.markEffectImg) then
        return
    end

    gw_sand_camera_mgr = gw_sand_camera_mgr or require "gw_sand_camera_mgr"
    local viewLevel = gw_sand_camera_mgr.GetViewLevel()

    local hudData = self.hudData
    -- 特效显示控制
    UIUtil.SetActive(self.markEffectImg, viewLevel > 5)

    -- 视图层级处理
    local rootTransform = self.obj_root.transform
    if viewLevel == 1 then
        local sf = 1 / gw_sand_camera_mgr.GetCameraScaleFactor()
        rootTransform.localScale = Vector3.one * (rootTransform.localScale.x / sf)
        self:ApplyHudSettings(hudData, false)
    elseif viewLevel <= 2 then
        if not self.recordScale then
            local sf = 1 / gw_sand_camera_mgr.GetCameraScaleFactor()
            self.recordScale = Vector3.one * sf
        end
        rootTransform.localScale = self.recordScale
        self:ApplyHudSettings(hudData, true)
    elseif self.recordScale then
        self.recordScale = nil
        rootTransform.localScale = Vector3.one
        self:ApplyHudSettings(hudData, false)
    end
end

function GWCompUnionMark:ApplyHudSettings(hudData, needScale)
    self:OnSetHudOffsetAndScale(
            hudData.screen_offset_level,
            hudData.offset_x,
            self:OnGetOffsetY(hudData.offset_y),
            hudData.target_offset_x,
            hudData.target_offset_y,
            needScale or hudData.needScale,
            hudData.needOffsetScale
    )
end

function GWCompUnionMark:RefreshMarkData(data, forced)
    self.markHudData = data
    if self:IsValid() then
        self:FindAllWithMeshRenderer(self.transEffect)
        if forced then
            self.markText = self.markHudData.langDes
        end
        self:SetMarkText(self.markText)
        -- 刷新图标显示
        local gw_asset_mgr = require "gw_asset_mgr"
        gw_asset_mgr:LoadSandMarkIcon(self.sp_mark, self.markHudData.markIconId or "1105")
    end
end

function GWCompUnionMark:FindAllWithMeshRenderer(node)
    if util.IsObjNull(node) then
        return
    end
    -- 递归地检查所有的子节点
    for i = 0, node.childCount - 1 do
        local child = node:GetChild(i)
        local meshRenderer = child:GetComponent(typeof(MeshRenderer))
        if not util.IsObjNull(meshRenderer) and self.markHudData.color then
            Utility.SetMaterialBlockProperty(3, meshRenderer, "_Color", self.markHudData.color)
        end
    end
end

function GWCompUnionMark:SetMarkText(content)
    self.txt_mark.text = content
    self.btn_bg_size:UpdateBackground()
    self.boxC_bg.size = Vector3(self.sp_bg.size.x - 0.6, 0.5, 0.5);
end

function GWCompUnionMark:Dispose(unloadShowOnly)
    if self.effectObj then
        self.effectObj:Dispose()
        self.effectObj = nil
    end
    sand_hud_base.Dispose(self, unloadShowOnly)
end

return GWCompUnionMark
