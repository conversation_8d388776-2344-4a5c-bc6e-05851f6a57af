--- Created by fgy.
--- DateTime: 2024/5/30 12:13
--- Des:大世界家园配置 只处理配置

local require = require
local pairs = pairs
local string = string
local tonumber = tonumber
local table = table

local game_scheme = require "game_scheme"
local log = require "gw_sand_log"

---@class GWSandCfg
local GWSandCfg = {
    viewLevelShowTypeRange = nil,
    regionLine = nil,
    regionZoneBuff1 = nil
}

-- 预编译常用字符串
local VIEW_LEVEL_PREFIX = "viewLevel"
local GRID_SPLITTER = "#"
local SCOPE_SPLITTER = ":"
local BUFF_SPLITTER = ";"

--- Grid字符串转Grid坐标
---@param str string
---@return table {x=number, y=number}
local function _getGridPos(str)
    local x, y = string.match(str, "(%d+)"..GRID_SPLITTER.."(%d+)")
    return { x = tonumber(x), y = tonumber(y) }
end

local function _initSandMapViewLevelCfg()
    if GWSandCfg.viewLevelShowTypeRange then return end

    GWSandCfg.viewLevelShowTypeRange = {}
    local count = game_scheme:SandMapViewLevel_nums()

    for i = 0, count - 1 do
        local csvData = game_scheme:SandMapViewLevel(i)
        local resourceID = csvData.resourceID
        local tabRange = {}

        -- 从高到低遍历level
        for level = 6, 1, -1 do
            local showList = csvData[VIEW_LEVEL_PREFIX..level].data
            if showList and showList[0] then
                for _, v in pairs(showList) do
                    if not tabRange[v] then
                        tabRange[v] = {max = level, min = level}
                    else
                        tabRange[v].min = level
                    end
                end
            end
        end

        GWSandCfg.viewLevelShowTypeRange[resourceID] = {
            tabRange = tabRange,
            order = csvData.order
        }
    end
end

local function _initRegionLine()
    if GWSandCfg.regionLine then return end

    GWSandCfg.regionLine = {}
    local count = game_scheme:SandMapRegion_nums()

    for i = 0, count - 1 do
        local csvData = game_scheme:SandMapRegion(i)
        local regionID = tonumber(csvData.regionID)
        local posList = {}

        for _, v in ipairs(string.split(csvData.Scope, SCOPE_SPLITTER)) do
            local gridPos = _getGridPos(v)
            table.insert(posList, {x = gridPos.x, y = 0, z = gridPos.y})
        end

        GWSandCfg.regionLine[regionID] = {
            color = csvData.color,
            pos = posList
        }
    end
end

---@public 获取沙盘HUD层级配置信息
function GWSandCfg.GetLevelShowTypeRange()
    _initSandMapViewLevelCfg()
    return GWSandCfg.viewLevelShowTypeRange
end

---@public 获取所有区域的线列表
function GWSandCfg.GetLineList()
    _initRegionLine()
    return GWSandCfg.regionLine
end

local GW_SAND_CONGRESS_REGION = 124
function GWSandCfg.GetSandCongressRegionCfg()
    local constCfg = game_scheme:GWMapConstant_0(GW_SAND_CONGRESS_REGION)
    if constCfg then
        local congressRegionId = constCfg.szParam.data[0]
        local cfg = game_scheme:SandMapRegion_0(congressRegionId)
        if cfg then
            return cfg
        else
            log.Error("沙盘国会区域配置ID不存在，请检查配置，SandMapRegion.csv , regionID:", congressRegionId)
        end
    else
        log.Error("沙盘国会区域常量配置ID丢失！！！，GWMapConstant.csv , ID:", GW_SAND_CONGRESS_REGION)
    end
end

function GWSandCfg.GetSandCongressCityCfg()
    local regionCfg = GWSandCfg.GetSandCongressRegionCfg()
    if regionCfg then
        local cityId = regionCfg.cityID
        local cfg = game_scheme:SandMapCity_0(cityId)
        if cfg then
            return cfg
        else
            log.Error("沙盘国会城市配置ID不存在，请检查配置，SandMapCity.csv , cityID:", cityId)
        end
    end
end

return GWSandCfg

