local require = require
local typeof = typeof

local RectTransform = CS.UnityEngine.RectTransform
local SpriteSwitcher = CS.War.UI.SpriteSwitcher
local Image = CS.UnityEngine.UI.Image
local Text = CS.UnityEngine.UI.Text
local Button = CS.UnityEngine.UI.Button
local RawImage = CS.UnityEngine.UI.RawImage
local Slider = CS.UnityEngine.UI.Slider
local Animator = CS.UnityEngine.Animator
local GameObject = CS.UnityEngine.GameObject


module("ui_land_revival_main_binding")

UIPath = "ui/prefabs/gw/gw_landrevival/uilandrevivalmain.prefab"

WidgetTable ={
	rtf_landTask = { path = "rtf_landTask", type = RectTransform, },
	ss_bg = { path = "rtf_landTask/ss_bg", type = SpriteSwitcher, },
	img_themeIcon = { path = "rtf_landTask/img_themeIcon", type = Image, },
	txt_day = { path = "rtf_landTask/txt_day", type = Text, },
	rtf_blackMask = { path = "rtf_landTask/rtf_blackMask", type = RectTransform, },
	rtf_hammerIcon = { path = "rtf_landTask/rtf_hammerIcon", type = RectTransform, },
	btn_taskBtn = { path = "rtf_landTask/btn_taskBtn", type = Button, event_name = "OnBtnTaskBtnClickedProxy"},
	rImg_effectRawImage = { path = "topBg/rImg_effectRawImage", type = RawImage, },
	btn_help = { path = "topArea/btn_help", type = Button, event_name = "OnBtnHelpClickedProxy"},
	txt_time = { path = "topArea/timeBg/txt_time", type = Text, },
	sld_leftSlider = { path = "bottomArea/activityProgess/sld_leftSlider", type = Slider, value_changed_event = "OnSliderLeftSliderValueChange"},
	txt_sldNum = { path = "bottomArea/activityProgess/sld_leftSlider/txt_sldNum", type = Text, },
	ss_BoxIcon = { path = "bottomArea/activityProgess/ss&btn&ator_BoxIcon", type = SpriteSwitcher, },
	btn_BoxIcon = { path = "bottomArea/activityProgess/ss&btn&ator_BoxIcon", type = Button, event_name = "OnBtnBoxIconClickedProxy"},
	ator_BoxIcon = { path = "bottomArea/activityProgess/ss&btn&ator_BoxIcon", type = Animator, },
	item_boxBubble = { path = "bottomArea/activityProgess/ss&btn&ator_BoxIcon/item_boxBubble", type = GameObject, },
	rtf_topDayParent = { path = "bottomArea/rtf_topDayParent", type = RectTransform, },
	rtf_bottomDayParent = { path = "bottomArea/rtf_bottomDayParent", type = RectTransform, },

}
