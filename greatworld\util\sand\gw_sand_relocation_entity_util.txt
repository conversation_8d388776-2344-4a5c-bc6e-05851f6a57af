--- Created by bxz.
--- Changed by connan.
--- DateTime: 2025/5/28
--- Des:沙盘实体位置迁移util

local require = require
local xpcall = xpcall

local sandbox_pb = require "sandbox_pb"
local gw_admin = require "gw_admin"
local gw_ed = require "gw_ed"
local gw_comp_name = require "gw_comp_name"
local gw_const = require "gw_const"

-- 保留原有错误处理
local gw_switch_utility = require "gw_switch_utility"
local sandLuaErr = gw_switch_utility.OnSandLuaErr

---@class GWSandRelocationEntityUtil
local M = {}
local _runtime = {}

--region 模块懒加载
-- 模块缓存
local _moduleCache = {}

local function _requireModule(name)
    if not _moduleCache[name] then
        _moduleCache[name] = require(name)
    end
    return _moduleCache[name]
end

local function _getSandData()
    return _requireModule("gw_sand_data")
end
--endregion

--region 迁城特殊规则（完整保留）
local ESEntityResViewType = gw_const.ESEntityResViewType
local relocationFunctions = {
    [ESEntityResViewType.AllianceBoss] = function(entity, state)
        if entity then
            for _, hudComp in pairs(_runtime.entity.addCompList) do
                local hudData = hudComp.hudData
                local mindLod = state and 1 or hudData.minLod
                if hudComp.compName ~= gw_comp_name.comp_icon_hud then
                    hudComp:OnSetLodRange(mindLod, hudData.maxLod, hudData.minInnerLod, hudData.maxInnerLod)
                else
                    -- 超过最大值不显示
                    mindLod = state and 7 or hudData.minLod
                    hudComp:OnSetLodRange(mindLod, hudData.maxLod, hudData.minInnerLod, hudData.maxInnerLod)
                end
            end
            -- 传递过滤器给gw_map_util
            local mapUtil = _requireModule("gw_map_util")
            if state then
                mapUtil.SetMapLoadFilter(function(serData)
                    if serData and serData.sid == _runtime.entitySid then
                        return false
                    end
                    return true
                end)
            else
                mapUtil.SetMapLoadFilter(nil)
            end
        end
    end,
}
--endregion

function M.LateUpdate()
    -- 完整保留原有逻辑...
    if not _runtime.entitySid then
        return
    end

    _runtime.startMove = true
    -- 0 用来特殊处理跨服迁城的玩家ID
    if not _runtime.entity then
        if _runtime.entitySid > 0 then
            _runtime.entity = _requireModule("gw_map_util").GetCompBySid(_runtime.entitySid)
        else
            _runtime.entity = _getSandData().selfData.GetSelfBaseEntity()
        end

        if _runtime.entity and _runtime.entity.serData then
            local baseCompUnit = _runtime.entity:GetComponent(gw_comp_name.comp_unit_base)
            _runtime.entityUnitId = baseCompUnit and baseCompUnit.compId
            _runtime.entityColliderRange = _runtime.entity.serData.cfg and _runtime.entity.serData.cfg.range
            if _runtime.entitySid > 0 then
                xpcall(M.SetRelocationEntityHudState, sandLuaErr, true)
            end
            -- 如果有实体了，则取消默认显示
            if M.moveChoseHud then
                M.moveChoseHud:UpdateBaseRelocationState(false)
            end
        end
    end

    if _runtime.worldPos then
        xpcall(M.RelocationEntityPosition, sandLuaErr)
        -- 更新UI的位置
        local screenPos = _requireModule("gw_sand_mgr").GetScreenPosWorldPos(_runtime.worldPos)
        if screenPos then
            gw_ed.mgr:Trigger(gw_ed.GW_SAND_MOVE_BUILDING_CENTER_CHANGE, screenPos)
        end
    end
end

function M.SetRelocationEntityHudState(enterRelocation)
    if _runtime.entity and _runtime.entity.serData then
        local map = _requireModule("gw_map_util").GetMap()
        if enterRelocation then
            _runtime.entityViewRange = map:GetRadiusBySid(_runtime.entitySid)
            map:SetRangeBySid(_runtime.entitySid, 9999)
        else
            map:SetRangeBySid(_runtime.entitySid, _runtime.entityViewRange)
        end

        local finalKey = _runtime.entity.serData:GetFinalKey2()
        if not relocationFunctions[finalKey] then
            return
        end
        relocationFunctions[finalKey](_runtime.entity, enterRelocation)
    end
end

function M.GetRelocationEntityState()
    return _runtime.entitySid ~= nil and _runtime.startMove
end

function M.GetReadyRelocationEntityState()
    return _runtime.entitySid ~= nil
end

function M.GetRelocationMoveType()
    if _runtime.moveType then
        return _runtime.moveType
    else
        local sandData = _getSandData()
        if sandData.selfData.IsBaseCrossServiceState() and not sandData.selfData.IsVisualCrossServiceState() then
            -- 返回永远是2
            return sandbox_pb.enSandboxCrossMoveType_TestPosMove
        end
        return nil
    end
end

function M.SetRelocationEntitySid(entitySid, position, relocationCallback, moveType)
    if not _runtime.entitySid then
        _runtime.entitySid = entitySid
        _runtime.moveType = moveType
        _runtime.relocationCallback = relocationCallback
        M.SetMoveChoseHudState(position, { x = position.x, y = position.z })
    end
end

function M.CancelRelocationEntitySid(reset)
    if _runtime.entitySid then
        if reset then
            M.ResetEntityPosition()
        end
        -- 还原视野剔除范围
        if _runtime.entitySid > 0 then
            xpcall(M.SetRelocationEntityHudState, sandLuaErr, false)
        end

        if M.moveChoseHud then
            M.moveChoseHud:OnShow(false)
        end
        _runtime = {}
    end
end

function M.RelocationEntityCompleted(state, gridPos)
    if _runtime.relocationCallback then
        _runtime.relocationCallback(state, gridPos)
        _runtime.relocationCallback = nil
    end
end

function M.RelocationEntityPosition()
    if _runtime.entity then
        if _runtime.entity.compId and _runtime.entity.addCompList then
            for _, hudComp in pairs(_runtime.entity.addCompList) do
                if hudComp.compName == gw_comp_name.comp_unit_base then
                    hudComp["UpdatePos"](hudComp, _runtime.worldPos)
                else
                    if hudComp["UpdateChangeTargetPos"] then
                        hudComp["UpdateChangeTargetPos"](hudComp, _runtime.worldPos)
                    end
                end
            end
        else
            -- 如果迁城过程中,组件被回收,则取消迁城
            M.CancelRelocationEntitySid()
        end
    else
        _runtime.entity = nil
    end
end

function M.ResetEntityPosition()
    if _runtime.entity and _runtime.entity.addCompList then
        for _, hudComp in pairs(_runtime.entity.addCompList) do
            if hudComp.compName == gw_comp_name.comp_unit_base then
                hudComp["ResetPos"](hudComp)
            else
                if hudComp["ResetChangeTargetPos"] then
                    hudComp["ResetChangeTargetPos"](hudComp)
                end
            end
        end
    end
end

--region 沙盘迁城提示 comp_move_base_hud
function M.SetMoveBaseHudState(worldPos, gridPos)
    if not _getSandData().selfData then
        return
    end

    local shouldShowHud = _getSandData().selfData.IsVisualAndBaseCoServiceState()

    local commonUtil = _requireModule("gw_common_util")
    if not commonUtil.GetSandVisualCrossServiceState() then
        shouldShowHud = true
    elseif commonUtil.CheckInSand_Sand() and not shouldShowHud then
        local allianceDuelVisual, _ = commonUtil.CheckInAllianceDuelSandbox()
        local zoneDuelVisual, _ = commonUtil.CheckInZoneDuelSandbox()
        shouldShowHud = allianceDuelVisual or zoneDuelVisual
    elseif commonUtil.CheckInSand_Storm() then
        --hlgtodo沙漠风暴 沙漠风暴观战也要return
        shouldShowHud = true
        --if false then
        --    shouldShowHud = false
        --    return
        --end
    end

    if not shouldShowHud then
        return
    end

    if not M.moveBaseHud then
        local hudData = {
            needScale = true,
            minLod = 6,
            targetPos = worldPos,
            loadImmediate = true
        }
        local _, curComp = _requireModule("gw_hud_util").InitHudComponent(gw_comp_name.comp_move_base_hud, hudData)
        M.moveBaseHud = curComp
    end

    M.moveBaseHud:OnShow(worldPos ~= nil)
    if worldPos then
        M.moveBaseHud:UpdateChangeTargetPos(worldPos)
    end
end

function M.HideMoveBaseHud()
    if M.moveBaseHud then
        M.moveBaseHud:OnShow(false)
    end
end
--endregion


function M.SetMoveChoseHudState(worldPos, gridPos)
    -- 隐藏迁城提示界面
    M.HideMoveBaseHud()

    if not M.moveChoseHud then
        local hudData = { needScale = true, minLod = 1, targetPos = worldPos, loadImmediate = true }
        local _, curComp = _requireModule("gw_hud_util").InitHudComponent(gw_comp_name.comp_move_chose_hud, hudData)
        M.moveChoseHud = curComp
    end

    _runtime.worldPos = worldPos or _runtime.worldPos
    _runtime.gridPos = gridPos or _runtime.gridPos or _getSandData().selfData.GetSelfVisualSandBoxPosition()
    _runtime.canMove = M.OnCheckAvail(_runtime.gridPos)
    if _runtime.worldPos then
        M.moveChoseHud:SwitchSpriteRenderer(_runtime.canMove)
        M.moveChoseHud:UpdateChangeTargetPos(_runtime.worldPos)
        M.moveChoseHud:UpdateBaseRelocationState(_runtime.entitySid == 0 and (_getSandData().selfData.GetSelfBaseEntity() == nil))
        M.moveChoseHud:OnShow(true)
    else
        M.moveChoseHud:OnShow(false)
    end

    gw_ed.mgr:Trigger(gw_ed.GW_SAND_CLICK_SCENE_EVENT, "ShowBaseOperate",
            _runtime.canMove, _runtime.gridPos, _runtime.entitySid)
end

function M.OnCheckAvail(pos)
    local collisionRange = _runtime.entityColliderRange or 3 -- 默认碰撞检测是3
    local sandWidth, sandHeight = _getSandData().GetSandGrid()
    local moveRadius = math.floor(collisionRange * 0.5)
    if (pos.x - moveRadius) < 0 or (pos.x + moveRadius) >= sandWidth or (pos.y - moveRadius) < 0 or (pos.y + moveRadius) >= sandHeight then
        return false
    end
    local canMove = not _requireModule("gw_map_util").CheckPointCollision(pos.x, pos.y, collisionRange * 0.5)
    if not canMove then
        return canMove
    end

    --不可迁城区域
    if not _requireModule("gw_sand_special_areas").CheckPosCanMove(pos, collisionRange) then
        return false
    end
    return true
end

function M.CheckEnterDrag(compId)
    if not compId then
        return false
    end

    return compId == 0 or compId == _runtime.entityUnitId
end

function M.OnDrag(x, y)
    local worldPos = _requireModule("gw_sand_camera_mgr").ScreenToWorldPoint(x, y)
    local gridPos = _requireModule("gw_sand_external_mgr").GetGridPos(worldPos.x, worldPos.z)
    M.SetMoveChoseHudState({ x = gridPos.x, y = 0, z = gridPos.y }, gridPos)
end

function M.Dispose()
    if M.moveBaseHud then
        gw_admin.PushComponent(M.moveBaseHud)
        M.moveBaseHud = nil
    end

    if M.moveChoseHud then
        gw_admin.PushComponent(M.moveChoseHud)
        M.moveChoseHud = nil
    end
    _runtime = { }
end

return M
