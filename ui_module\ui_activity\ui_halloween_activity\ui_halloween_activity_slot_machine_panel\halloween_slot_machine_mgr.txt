local require = require

local table = table
local pairs = pairs
local ipairs = ipairs
local type = type
local string = string
local math = math

local festival_activity_mgr = require "festival_activity_mgr"
local game_scheme = require "game_scheme"
local log = require "log"
local gw_task_data = require "gw_task_data"
local data_mgr = require "data_mgr"
local event = require "event"
local red_system = require "red_system"
local red_const = require "red_const"
local event_task_define = require "event_task_define"
local gw_event_activity_define = require "gw_event_activity_define"
local gw_task_const = require "gw_task_const"

local halloween_activity_slot_machine_const = require "halloween_activity_slot_machine_const"
local halloween_activity_slot_machine_setting_data = require "halloween_activity_slot_machine_setting_data"
local halloween_activity_slot_machine_history_data = require "halloween_activity_slot_machine_history_data"
local halloween_activity_slot_machine_rate_data = require "halloween_activity_slot_machine_rate_data"


local net_login_module = require "net_login_module"
module("halloween_slot_machine_mgr")

---所有数据存储
local _d = data_mgr:CreateData("halloween_slot_machine_mgr")
---非服务器数据存储
local mc = _d.mde.const

function Init()
    event.Register(event.USER_DATA_RESET, Clear)
    
    event.Register(event_task_define.REFRESH_TASK, TaskPointUpdate) --任务刷新,红点注册
    red_system.RegisterRedFunc(red_const.Enum.HalloweenSlotMachine, TaskCanReceiveRewardRed)

    halloween_activity_slot_machine_setting_data:Init()
    halloween_activity_slot_machine_rate_data.Init(GetActivityID())


    local serverTimeZone = net_login_module.GetServerTimeZone()
    halloween_activity_slot_machine_history_data:Init()
    -- test
    --halloween_activity_slot_machine_history_data:SetTestData()
end



---@public function 设置活动ID
function SetActivityID(activityID)
    mc.activityID = activityID
end

---@public function 获取活动ID
function GetActivityID()
    return mc.activityID or halloween_activity_slot_machine_const.act_id.slot_game
end

function GetActivityCfg()
    return festival_activity_mgr.GetActivityCfgByActivityID(GetActivityID())
end

function GetCoinID()
    local festivalData = GetActivityCfg()
    return festivalData.ctnID1[1][0]
end

---@public function 获取当前已经抽奖多少次
function GetCurDrawCount()
    --TODO serverData
    return 0
end

---@public function 获取进度任务array
---@return Array[task_id]
function GetProgressRewardTasks()
    local festivalData = festival_activity_mgr.GetActivityCfgByActivityID(halloween_activity_slot_machine_const.act_id.progressReward)
    local id_array = festivalData.ctnID1
    local task_data_list = {}
    for i = 0, festivalData.ctnID1.count - 1 do
        local taskData = gw_task_data.GetTaskData(id_array[i])
        if taskData ~= nil then
            table.insert(task_data_list, taskData)
        end
    end
    
    return task_data_list
end


---@public function 获取下一个进度
---@return task_data
function GetNextProgressTask()
    local task_array = GetProgressRewardTasks()
    local curCount = GetCurDrawCount()
    --根据配置的进度要求计算下一个进度奖励ID
    for i = 1, #task_array do
        local taskData = task_array[i]
        if taskData.ConditionValue1 > curCount then
            return taskData
        end
    end
    
    -- 没找到，说明已经满了，显示最后一个
    return task_array[task_array.count]
end

---@public function 获取下一个进度奖励ID
---@return item_id
function GetNextProgressReward()
    local task_array = GetProgressRewardTasks()
    local curCount = GetCurDrawCount()
    --根据配置的进度要求计算下一个进度奖励ID
    for i = 1, task_array.count do
        local taskData = task_array[i]
        if taskData.ConditionValue1 > curCount then
            return GetTaskReward(taskData)
        end
    end
end

function GetTaskReward(task_data)
    if task_data == nil then
        return {}
    end
    local reward_mgr = require "reward_mgr"
    local rewardCfg = reward_mgr.GetRewardGoodsList(task_data.TaskReward)
    local dataList = {}
    local dataCount = 0
    for i2,v in pairs(rewardCfg) do
        local item =
        {
            id = v.id,
            count = v.num,
        }
        table.insert(dataList,item)
        dataCount = dataCount + 1
    end
    -- 这里默认只有一个
    return dataList[1]
end



--- 通过TaskId 检查是否能领奖
function TaskIdCheckReqTask(taskId)
    local taskData = GetTaskMainData(taskId)
    local canReq = CheckReqTask(taskData)
    return canReq
end


-- 获取任务总表的数据
function GetTaskMainData(taskId)
    local taskData = game_scheme:TaskMain_0(taskId)
    return taskData
end

--获取任务数据的图标
function GetTaskMainDataIcon(taskId)
    local taskData = GetTaskMainData(taskId)
    return taskData.Icon.data[taskData.Icon.count - 1]
end


---检查是否能领取宝箱奖励
function CheckReqTask(taskData)
    local len = taskData.ConditionValue2.count
    local data = taskData.ConditionValue2.data
    if data == nil then
        return false
    end
    
    local result = 0
    for i = 0, len do
        local taskData = gw_task_data.GetTaskData(data[i])
        if taskData ~= nil then
            if taskData.rate >= taskData.completeValue then
                result = result + 1
            end
        end
    end
    return result == taskData.ConditionValue2.count
end

---检查宝箱是否已经给领取
function CheckBoxReqState(task)
    local taskData =  gw_task_data.GetTaskData(task)
    if taskData == nil then
        return false
    end
    
    return taskData.status
end


--- 某一个宝箱可以领取时红点
function GetCurTaskReceiveRewardRed(taskId)
    local canReceive = CheckBoxReqState(taskId)
    if canReceive == true then --已经给领取了没有红点
        return 0 
    end
    local gw_task_mgr = require "gw_task_mgr"
    local result =  gw_task_mgr.GetTaskIsReceive(taskId)
    if result == true then
        return 1
    end
    return 0
end

---@public function 任务刷新，更新红点
function TaskPointUpdate(_, _,moduleId, moduleList)
    local gw_task_const = require "gw_task_const"
    if moduleList[gw_task_const.TaskModuleType.HalloweenSignIn] then
        red_system.TriggerRed(red_const.Enum.HalloweenSignInReward)
        event.Trigger(gw_event_activity_define.GW_ACTIVITY_RED_NEED_UPDATE,GetActivityID())
    end
end

---@public function 获取红点数量
function TaskCanReceiveRewardRed()
    local festivalData = festival_activity_mgr.GetActivityCfgByActivityID(GetActivityID())
    local receiveCount = 0
    if festivalData then
        local gw_task_mgr = require "gw_task_mgr"
        for i = 0, festivalData.ctnID1.count - 1 do
            local result =  gw_task_mgr.GetTaskIsReceive(festivalData.ctnID1.data[i])
            if result then
                receiveCount = receiveCount + 1
            end
        end
    end
    return receiveCount
end

function Clear()
    _d.mde:clear()
    mc = _d.mde.const
end