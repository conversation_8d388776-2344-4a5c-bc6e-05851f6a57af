--- Created by fgy.
--- DateTime: 2024/8/30 18:59
--- Des:mark数据创建销毁管理(联盟标记直接展示所有，个人展示范围内)

local require = require
local data_mgr = require "data_mgr"
local sand_mark_data = require "sand_mark_data"
local game_scheme = require "game_scheme"
local lang = require "lang"

---所有数据存储
local _d = data_mgr:CreateData("gw_sand_mark_mgr")
---非服务器数据存储
local mc = _d.mde.const

local gw_map_util = nil

---@class GWSandMarkMgr
local GWSandMarkMgr = {}

---@see 初始化
function GWSandMarkMgr.Init()
    mc.unionType = 4
    --已经创建的markId
    mc.unionCompIds = {}
    --已经创建的个人id
    mc.personCompIds = {}

    mc.unionCfgs = {}
    mc.personCfgs = {}
    mc.initUnionMark = false

    mc.unionLang = 1
    mc.unionMark = 1
    GWSandMarkMgr.InitCfg()
end

local function GetMarkKey(index)
    return string.format("mark_%s", index)
end

function GWSandMarkMgr.InitCfg()
    local color_palette = require "color_palette"
    local cfgKeys = game_scheme:GWMapConstant_0(54).szParam.data
    local cfgLang = game_scheme:GWMapConstant_0(55).szParam.data
    local cfgStr = game_scheme:GWMapConstant_0(83).szParamString
    local colorCountList = string.split(cfgStr, "#")
    for i = 1, 10 do
        local key = cfgKeys[i - 1]
        local lang = cfgLang[i - 1]
        local color = color_palette.HexToColor(colorCountList[i], true)
        mc.unionCfgs[key] = { color = color, lang = lang }
    end
    local lang = game_scheme:GWMapConstant_0(69).szParam.data[1]
    local key = game_scheme:GWMapConstant_0(69).szParam.data[0]
    local color = color_palette.HexToColor(game_scheme:GWMapConstant_0(69).szParamString, true)
    mc.unionCfgs[key] = { color = color, lang = lang }
    mc.unionLang = lang
    mc.unionMark = key

    for i = 1, 3 do
        local lang = game_scheme:GWMapConstant_0(52).szParam.data[i - 1]
        local key = game_scheme:GWMapConstant_0(53).szParam.data[i - 1]
        mc.personCfgs[key] = lang
    end
end

---@see 创建mark
function GWSandMarkMgr.UpdateUnionMark(params,forced)
    if GWMgr.curScene ~= GWConst.ESceneType.Sand then
        return
    end

    gw_map_util = gw_map_util or require "gw_map_util"
    if not gw_map_util.GetMap() then
        return
    end
    
    if not mc.unionCompIds then
        return
    end
    mc.initUnionMark = true
    local unionData, gatherMark = sand_mark_data.GetMarkListByType(mc.unionType, false, true)
    --local allianceRallyPointData = game_scheme:GWMapConstant_0(69).szParam.data
    --print("UpdateUnionMark"..Edump(unionData))
    local hasCheckedIndex = {}
    if gatherMark or unionData then
        local count = unionData and #unionData or 0
        for k = 0, count do
            local v = {}
            if k == 0 then
                if gatherMark then
                    v = gatherMark
                end
            else
                v = unionData[k]
            end
            if v and v.pos then
                local markKey = GetMarkKey(v.nIndex)
                hasCheckedIndex[markKey] = true
                if not mc.unionCompIds[markKey] then
                    local hudData = {
                        targetPos = { x = v.pos.x, y = 0, z = v.pos.y },
                        needScale = false,
                        loadImmediate = true
                    }
                    local markIconId = v.nSymbol > 0 and v.nSymbol or mc.unionMark
                    local str = lang.Get(mc.unionCfgs[markIconId] and mc.unionCfgs[markIconId].lang or 1)
                    if not string.empty(v.sRemark) then
                        str = v.sRemark
                    end
                    local id, comp = GWAdmin.HudUtil.InitHudComponent(GWCompName.comp_union_mark, hudData, { markIconId = markIconId, langDes = str, sRemark = v.sRemark, color = mc.unionCfgs[markIconId] and mc.unionCfgs[markIconId].color or Color.white, markData = v })
                    comp:RefreshMarkData({ markIconId = markIconId, langDes = str, sRemark = v.sRemark, color = mc.unionCfgs[markIconId] and mc.unionCfgs[markIconId].color or Color.white, markData = v })
                    mc.unionCompIds[markKey] = comp
                else
                    local comp = mc.unionCompIds[markKey]
                    comp.hudData.targetPos = { x = v.pos.x, y = 0, z = v.pos.y }
                    local markIconId = v.nSymbol > 0 and v.nSymbol or mc.unionMark
                    local str = lang.Get(mc.unionCfgs[markIconId] and mc.unionCfgs[markIconId].lang or 1)
                    if not string.empty(v.sRemark) then
                        str = v.sRemark
                    end
                    comp:RefreshMarkData({ markIconId = markIconId, langDes = str, sRemark = v.sRemark, color = mc.unionCfgs[markIconId] and mc.unionCfgs[markIconId].color or Color.white, markData = v }, forced)
                    comp:UpdateChangeTargetPos(comp.hudData.targetPos)
                end
            end
        end
    end
    for i = 11, 1, -1 do
        local markKey = GetMarkKey(i)
        if mc.unionCompIds[markKey] and not hasCheckedIndex[markKey] then
            GWAdmin.PushComponent(mc.unionCompIds[markKey])
            mc.unionCompIds[markKey] = nil
        end
    end
end

local personMarkHideViewLevel = GWCameraConst.SPersonMarkHideViewLevel
---@see 创建销毁个人mark
function GWSandMarkMgr.OnUpdateVision(params)
    --视野内不展示个人标记的时候，不创建也不去销毁
    if params and params.viewLevel < personMarkHideViewLevel then
        return
    end
    if not mc.personCompIds then
        return
    end
    if not mc.initUnionMark then
        GWSandMarkMgr.UpdateUnionMark()
    end
    local tab = sand_mark_data.GetPersonalMarkPosList()
    if tab and params.viewGrids then
        local gridScope = params.viewGrids
        local hasCheckedIndex = {}
        for x = gridScope.minX, gridScope.maxX do
            for y = gridScope.minY, gridScope.maxY do
                if tab[x] and tab[x][y] then
                    local markData = tab[x][y]
                    local markKey = GetMarkKey(markData.nIndex)

                    hasCheckedIndex[markKey] = true
                    GWG.GWAdmin.SwitchUtility.SandWarn("OnUpdateVision", markData.nIndex, markData.pos.x, markData.pos.y)
                    if not mc.personCompIds[markKey] then
                        local hudData = {
                            targetPos = { x = markData.pos.x, y = 0, z = markData.pos.y },
                            minLod = GWCameraConst.SPersonMarkHideViewLevel,
                            loadImmediate = true
                        }
                        local id, comp = GWAdmin.HudUtil.InitHudComponent(GWCompName.comp_personal_mark, hudData, { markIconId = markData.nSymbol })
                        mc.personCompIds[markKey] = comp
                    else
                        local comp = mc.personCompIds[markKey]
                        comp.hudData.targetPos = { x = markData.pos.x, y = 0, z = markData.pos.y }
                        comp:UpdateIcon(markData.nSymbol)
                    end
                end
            end
        end
        for k, v in pairs(mc.personCompIds) do
            if not hasCheckedIndex[k] then
                GWG.GWAdmin.SwitchUtility.SandWarn("hasCheckedIndex", k)
                GWAdmin.PushComponent(v)
                mc.personCompIds[k] = nil
            end
        end
    end
end

function GWSandMarkMgr.UpdateServerPersonalMark(msg)
    local markData = msg.info
    if not markData then
        GWAdmin.SwitchUtility.SandWarn("UpdateServerPersonalMark:markData is nil")
        return
    end
    local markKey = GetMarkKey(markData.nIndex)
    --操作类型(添加0/删除1/修改2)
    if msg.operate ~= 1 then
        if not mc.personCompIds[markKey] then
            local hudData = {
                targetPos = { x = markData.pos.x, y = 0, z = markData.pos.y },
                minLod = GWCameraConst.SPersonMarkHideViewLevel,
                loadImmediate = true,
            }
            local id, comp = GWAdmin.HudUtil.InitHudComponent(GWCompName.comp_personal_mark, hudData, { markIconId = markData.nSymbol })
            mc.personCompIds[markKey] = comp
        else
            local comp = mc.personCompIds[markKey]
            comp.hudData.targetPos = { x = markData.pos.x, y = 0, z = markData.pos.y }
            comp:UpdateIcon(markData.nSymbol)
        end
    else
        if mc.personCompIds[markKey] then
            GWAdmin.PushComponent(mc.personCompIds[markKey])
            mc.personCompIds[markKey] = nil
        end
    end
end

---@see 销毁
function GWSandMarkMgr.Dispose()
    if mc.personCompIds then
        for k, v in pairs(mc.personCompIds) do
            GWAdmin.PushComponent(v)
        end
        mc.personCompIds = {}
    end
    if mc.unionCompIds then
        for k, v in pairs(mc.unionCompIds) do
            GWAdmin.PushComponent(v)
        end
        mc.unionCompIds = {}
    end
    mc.initUnionMark = false
end

return GWSandMarkMgr